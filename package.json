{"name": "map", "version": "0.1.0", "private": true, "scripts": {"serve": "daas-cli serve", "build": "daas-cli build", "published": "cd ./lib && npm run published", "published:dev": "cd ./lib && npm run published:dev", "cinstall": "rimraf -rf package-lock.json yarn.lock node_modules/@daas && npm i"}, "dependencies": {"@daas/components": "5.0.0-snapshot", "@daas/surface-web-lib": "5.0.1-snapshot", "@daas/meta-web-lib": "5.0.0-snapshot", "@daas/graph-next-lib": "0.0.1"}, "devDependencies": {"@daas/cli": "5.0.0-snapshot"}}