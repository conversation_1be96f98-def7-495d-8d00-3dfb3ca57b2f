export function loadJS(url) {
  return new Promise((resolve, reject) => {
    // 检查是否已存在相同的脚本
    const existingScripts = Array.from(document.getElementsByTagName("script"));
    const scriptExists = existingScripts.some((script) => script.src === url);

    // 如果脚本已经存在，直接返回成功
    if (scriptExists) {
      resolve();
      return;
    }

    // 创建并加载新脚本
    const script = document.createElement("script");
    script.src = url;
    script.onload = resolve;
    script.onerror = reject;
    document.body.appendChild(script);
  });
}

export async function loadBaiduMap() {
  window.bmapcfg = {
    imgext: ".jpg", //瓦片图的后缀 ------ 根据需要修改，一般是 .png .jpg
    tiles_dir: "", //普通瓦片图的地址，为空默认在 offlinemap/tiles/ 目录
    tiles_hybrid: "", //卫星瓦片图的地址，为空默认在 offlinemap/tiles_hybrid/ 目录
    tiles_self: "", //自定义图层的地址，为空默认在 offlinemap/tiles_self/ 目录
  };

  window.bmapcfg.home = "/daas/static/maps/bmap/";

  window.BMap_loadScriptTime = new Date().getTime();
  window.BMap = window.BMap || {};
  window.BMap.apiLoad = function () {
    delete window.BMap.apiLoad;
    if (window.onBMapCallback && typeof onBMapCallback == "function") {
      window.onBMapCallback();
    }
  };

  await loadJS(window.bmapcfg.home + "bmap_offline_api_v3.0_min.js");
  await loadJS(window.bmapcfg.home + "map_plus.js");
  await loadJS(window.bmapcfg.home + "map_city.js");

  window.bmapLoadJScript = loadJS;
}
