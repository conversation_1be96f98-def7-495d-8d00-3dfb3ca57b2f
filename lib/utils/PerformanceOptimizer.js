/**
 * 性能优化工具类
 * 提供各种性能优化相关的工具和方法
 */

import { globalResourceManager } from './ResourceManager.js';

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {Number} wait 等待时间
 * @param {Boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(this, args);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {Number} limit 时间限制
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;
  
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * requestAnimationFrame 节流
 * @param {Function} func 要节流的函数
 * @returns {Function} 节流后的函数
 */
export function rafThrottle(func) {
  let rafId = null;
  
  return function(...args) {
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, args);
        rafId = null;
      });
    }
  };
}

/**
 * 虚拟滚动管理器
 */
export class VirtualScrollManager {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 50;
    this.containerHeight = options.containerHeight || 400;
    this.buffer = options.buffer || 5;
    this.items = options.items || [];
    
    this.scrollTop = 0;
    this.visibleStart = 0;
    this.visibleEnd = 0;
    this.totalHeight = 0;
    
    this.updateVisibleRange();
  }

  /**
   * 设置数据项
   * @param {Array} items 数据项数组
   */
  setItems(items) {
    this.items = items;
    this.totalHeight = items.length * this.itemHeight;
    this.updateVisibleRange();
  }

  /**
   * 更新滚动位置
   * @param {Number} scrollTop 滚动位置
   */
  updateScrollTop(scrollTop) {
    this.scrollTop = scrollTop;
    this.updateVisibleRange();
  }

  /**
   * 更新可见范围
   */
  updateVisibleRange() {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
    this.visibleStart = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.buffer);
    this.visibleEnd = Math.min(this.items.length, this.visibleStart + visibleCount + this.buffer * 2);
  }

  /**
   * 获取可见项
   * @returns {Array} 可见项数组
   */
  getVisibleItems() {
    return this.items.slice(this.visibleStart, this.visibleEnd).map((item, index) => ({
      item,
      index: this.visibleStart + index,
      top: (this.visibleStart + index) * this.itemHeight
    }));
  }

  /**
   * 获取容器样式
   * @returns {Object} 样式对象
   */
  getContainerStyle() {
    return {
      height: `${this.containerHeight}px`,
      overflow: 'auto'
    };
  }

  /**
   * 获取内容样式
   * @returns {Object} 样式对象
   */
  getContentStyle() {
    return {
      height: `${this.totalHeight}px`,
      position: 'relative'
    };
  }
}

/**
 * 图片懒加载管理器
 */
export class ImageLazyLoader {
  constructor(options = {}) {
    this.rootMargin = options.rootMargin || '50px';
    this.threshold = options.threshold || 0.1;
    this.placeholder = options.placeholder || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGNUY1RjUiLz48L3N2Zz4=';
    
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: this.rootMargin,
        threshold: this.threshold
      }
    );
    
    this.loadingImages = new Set();
    this.loadedImages = new Set();
  }

  /**
   * 观察图片元素
   * @param {HTMLImageElement} img 图片元素
   */
  observe(img) {
    if (!img.dataset.src) return;
    
    // 设置占位图
    if (!img.src || img.src === '') {
      img.src = this.placeholder;
    }
    
    this.observer.observe(img);
  }

  /**
   * 取消观察图片元素
   * @param {HTMLImageElement} img 图片元素
   */
  unobserve(img) {
    this.observer.unobserve(img);
  }

  /**
   * 处理交叉观察
   * @param {Array} entries 观察条目
   * @private
   */
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.loadImage(entry.target);
      }
    });
  }

  /**
   * 加载图片
   * @param {HTMLImageElement} img 图片元素
   * @private
   */
  async loadImage(img) {
    if (this.loadingImages.has(img) || this.loadedImages.has(img)) {
      return;
    }

    this.loadingImages.add(img);
    this.observer.unobserve(img);

    try {
      const src = img.dataset.src;
      await this.preloadImage(src);
      
      img.src = src;
      img.classList.add('loaded');
      this.loadedImages.add(img);
    } catch (error) {
      img.classList.add('error');
      console.error('Image load failed:', error);
    } finally {
      this.loadingImages.delete(img);
    }
  }

  /**
   * 预加载图片
   * @param {String} src 图片地址
   * @returns {Promise} 加载 Promise
   * @private
   */
  preloadImage(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = resolve;
      img.onerror = reject;
      img.src = src;
    });
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.observer.disconnect();
    this.loadingImages.clear();
    this.loadedImages.clear();
  }
}

/**
 * 组件缓存管理器
 */
export class ComponentCache {
  constructor(maxSize = 50) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessOrder = [];
  }

  /**
   * 获取缓存的组件
   * @param {String} key 缓存键
   * @returns {Object} 组件实例
   */
  get(key) {
    if (this.cache.has(key)) {
      // 更新访问顺序
      this.updateAccessOrder(key);
      return this.cache.get(key);
    }
    return null;
  }

  /**
   * 设置组件缓存
   * @param {String} key 缓存键
   * @param {Object} component 组件实例
   */
  set(key, component) {
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      // 移除最久未访问的组件
      const oldestKey = this.accessOrder.shift();
      const oldComponent = this.cache.get(oldestKey);
      
      // 销毁组件
      if (oldComponent && oldComponent.destroy) {
        oldComponent.destroy();
      }
      
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, component);
    this.updateAccessOrder(key);
  }

  /**
   * 删除缓存
   * @param {String} key 缓存键
   */
  delete(key) {
    if (this.cache.has(key)) {
      const component = this.cache.get(key);
      
      // 销毁组件
      if (component && component.destroy) {
        component.destroy();
      }
      
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
    }
  }

  /**
   * 清空缓存
   */
  clear() {
    // 销毁所有组件
    for (const component of this.cache.values()) {
      if (component && component.destroy) {
        component.destroy();
      }
    }
    
    this.cache.clear();
    this.accessOrder = [];
  }

  /**
   * 更新访问顺序
   * @param {String} key 缓存键
   * @private
   */
  updateAccessOrder(key) {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  /**
   * 从访问顺序中移除
   * @param {String} key 缓存键
   * @private
   */
  removeFromAccessOrder(key) {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * 获取缓存统计
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys()),
      accessOrder: [...this.accessOrder]
    };
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
    this.isMonitoring = false;
    this.startTime = performance.now();
  }

  /**
   * 开始监控
   */
  start() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.startTime = performance.now();

    // 监控页面性能
    this.observePagePerformance();

    // 监控内存使用
    this.observeMemory();

    // 监控长任务
    this.observeLongTasks();
  }

  /**
   * 停止监控
   */
  stop() {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }

  /**
   * 记录性能指标
   * @param {String} name 指标名称
   * @param {Number} value 指标值
   * @param {Object} metadata 元数据
   */
  recordMetric(name, value, metadata = {}) {
    const timestamp = performance.now();

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    this.metrics.get(name).push({
      value,
      timestamp,
      metadata
    });

    // 限制每个指标的记录数量
    const records = this.metrics.get(name);
    if (records.length > 1000) {
      records.splice(0, records.length - 1000);
    }
  }

  /**
   * 获取性能指标
   * @param {String} name 指标名称
   * @returns {Array} 指标记录
   */
  getMetric(name) {
    return this.metrics.get(name) || [];
  }

  /**
   * 获取所有指标
   * @returns {Object} 所有指标
   */
  getAllMetrics() {
    const result = {};
    for (const [name, records] of this.metrics) {
      result[name] = records;
    }
    return result;
  }

  /**
   * 计算指标统计
   * @param {String} name 指标名称
   * @returns {Object} 统计信息
   */
  getMetricStats(name) {
    const records = this.getMetric(name);
    if (records.length === 0) return null;

    const values = records.map(r => r.value);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return {
      count: values.length,
      sum,
      avg,
      min,
      max,
      latest: values[values.length - 1]
    };
  }

  /**
   * 监控页面性能
   */
  observePagePerformance() {
    // 监控导航性能
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric(`navigation.${entry.name}`, entry.duration, {
            type: entry.entryType,
            startTime: entry.startTime
          });
        }
      });

      observer.observe({ entryTypes: ['navigation', 'measure', 'mark'] });
      this.observers.push(observer);
    }
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('longTask', entry.duration, {
              startTime: entry.startTime,
              name: entry.name
            });
          }
        });

        observer.observe({ entryTypes: ['longtask'] });
        this.observers.push(observer);
      } catch (error) {
        // longtask 可能不被支持
        console.warn('Long task monitoring not supported:', error);
      }
    }
  }

  /**
   * 监控内存使用
   */
  observeMemory() {
    if ('memory' in performance) {
      const logMemory = () => {
        if (!this.isMonitoring) return;

        const memory = performance.memory;
        this.recordMetric('memory.used', memory.usedJSHeapSize);
        this.recordMetric('memory.total', memory.totalJSHeapSize);
        this.recordMetric('memory.limit', memory.jsHeapSizeLimit);
      };

      // 每30秒记录一次内存使用
      const intervalId = setInterval(logMemory, 30000);
      globalResourceManager.setInterval(logMemory, 30000);
    }
  }

  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    const report = {
      timestamp: Date.now(),
      uptime: performance.now() - this.startTime,
      metrics: {},
      summary: {}
    };

    // 收集所有指标的统计信息
    for (const name of this.metrics.keys()) {
      report.metrics[name] = this.getMetricStats(name);
    }

    // 生成摘要
    report.summary = {
      totalMetrics: this.metrics.size,
      memoryUsage: report.metrics['memory.used']?.latest || 0,
      longTaskCount: report.metrics['longTask']?.count || 0,
      averageLongTaskDuration: report.metrics['longTask']?.avg || 0
    };

    return report;
  }

  /**
   * 清除指标
   * @param {String} name 指标名称，不传则清除所有
   */
  clearMetrics(name) {
    if (name) {
      this.metrics.delete(name);
    } else {
      this.metrics.clear();
    }
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stop();
    this.clearMetrics();
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();
export const componentCache = new ComponentCache();

export default {
  debounce,
  throttle,
  rafThrottle,
  VirtualScrollManager,
  ImageLazyLoader,
  ComponentCache,
  PerformanceMonitor,
  performanceMonitor,
  componentCache
};
