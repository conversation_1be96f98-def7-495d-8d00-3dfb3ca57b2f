/**
 * 懒加载工具类
 * 提供组件、模块、资源的懒加载功能
 */

import { formatError, ERROR_TYPES } from '../model/layer/types.js';

/**
 * 懒加载管理器
 */
export class LazyLoader {
  static cache = new Map();
  static loadingPromises = new Map();

  /**
   * 懒加载模块
   * @param {String} modulePath 模块路径
   * @param {Object} options 选项
   * @returns {Promise} 模块导入 Promise
   */
  static async module(modulePath, options = {}) {
    const { cache = true, retry = 3 } = options;

    // 检查缓存
    if (cache && this.cache.has(modulePath)) {
      return this.cache.get(modulePath);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(modulePath)) {
      return this.loadingPromises.get(modulePath);
    }

    try {
      const loadingPromise = this.withRetry(() => import(modulePath), retry);
      this.loadingPromises.set(modulePath, loadingPromise);

      const module = await loadingPromise;

      // 缓存结果
      if (cache) {
        this.cache.set(modulePath, module);
      }

      this.loadingPromises.delete(modulePath);
      return module;
    } catch (error) {
      this.loadingPromises.delete(modulePath);
      console.error(`LazyLoader.module[${modulePath}]:`, error);
      throw error;
    }
  }

  /**
   * 懒加载脚本
   * @param {String} src 脚本地址
   * @param {Object} options 选项
   * @returns {Promise} 加载 Promise
   */
  static async script(src, options = {}) {
    const { cache = true, timeout = 10000, async = true } = options;

    // 检查缓存
    if (cache && this.cache.has(src)) {
      return this.cache.get(src);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    const loadingPromise = new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.async = async;
      
      const timeoutId = setTimeout(() => {
        reject(new Error(`Script load timeout: ${src}`));
      }, timeout);

      script.onload = () => {
        clearTimeout(timeoutId);
        resolve(script);
      };

      script.onerror = () => {
        clearTimeout(timeoutId);
        reject(new Error(`Script load failed: ${src}`));
      };

      document.head.appendChild(script);
    });

    this.loadingPromises.set(src, loadingPromise);

    try {
      const script = await loadingPromise;
      
      if (cache) {
        this.cache.set(src, script);
      }

      this.loadingPromises.delete(src);
      return script;
    } catch (error) {
      this.loadingPromises.delete(src);
      console.error(`LazyLoader.script[${src}]:`, error);
      throw error;
    }
  }

  /**
   * 懒加载样式
   * @param {String} href 样式地址
   * @param {Object} options 选项
   * @returns {Promise} 加载 Promise
   */
  static async style(href, options = {}) {
    const { cache = true, timeout = 10000 } = options;

    // 检查缓存
    if (cache && this.cache.has(href)) {
      return this.cache.get(href);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(href)) {
      return this.loadingPromises.get(href);
    }

    const loadingPromise = new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      
      const timeoutId = setTimeout(() => {
        reject(new Error(`Style load timeout: ${href}`));
      }, timeout);

      link.onload = () => {
        clearTimeout(timeoutId);
        resolve(link);
      };

      link.onerror = () => {
        clearTimeout(timeoutId);
        reject(new Error(`Style load failed: ${href}`));
      };

      document.head.appendChild(link);
    });

    this.loadingPromises.set(href, loadingPromise);

    try {
      const link = await loadingPromise;
      
      if (cache) {
        this.cache.set(href, link);
      }

      this.loadingPromises.delete(href);
      return link;
    } catch (error) {
      this.loadingPromises.delete(href);
      console.error(`LazyLoader.style[${href}]:`, error);
      throw error;
    }
  }

  /**
   * 预加载资源
   * @param {Array} resources 资源列表
   * @param {Object} options 选项
   */
  static async preload(resources, options = {}) {
    const { concurrency = 3 } = options;
    
    const chunks = this.chunkArray(resources, concurrency);
    
    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(resource => this.loadResource(resource))
      );
    }
  }

  /**
   * 加载单个资源
   * @param {Object} resource 资源配置
   * @returns {Promise} 加载 Promise
   * @private
   */
  static async loadResource(resource) {
    const { type, path, options = {} } = resource;

    switch (type) {
      case 'module':
        return this.module(path, options);
      case 'script':
        return this.script(path, options);
      case 'style':
        return this.style(path, options);
      default:
        throw new Error(`Unknown resource type: ${type}`);
    }
  }

  /**
   * 重试机制
   * @param {Function} fn 要重试的函数
   * @param {Number} retries 重试次数
   * @returns {Promise} 重试 Promise
   * @private
   */
  static async withRetry(fn, retries = 3) {
    let lastError;
    
    for (let i = 0; i <= retries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        if (i < retries) {
          // 指数退避
          await this.delay(Math.pow(2, i) * 1000);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 延迟函数
   * @param {Number} ms 延迟毫秒数
   * @returns {Promise} 延迟 Promise
   * @private
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 数组分块
   * @param {Array} array 数组
   * @param {Number} size 块大小
   * @returns {Array} 分块后的数组
   * @private
   */
  static chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 清除缓存
   * @param {String} key 缓存键，不传则清除所有
   */
  static clearCache(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计信息
   */
  static getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      loadingCount: this.loadingPromises.size
    };
  }
}

/**
 * 地图资源懒加载器
 * 专门用于地图相关资源的懒加载
 */
export class MapResourceLoader {
  constructor() {
    this.loadedResources = new Set();
    this.loadingPromises = new Map();
  }

  /**
   * 懒加载百度地图API
   * @param {String} ak API密钥
   * @param {Object} options 选项
   * @returns {Promise} 加载 Promise
   */
  async loadBaiduMapAPI(ak, options = {}) {
    const { version = '3.0', callback = 'initBaiduMap' } = options;
    const url = `https://api.map.baidu.com/api?v=${version}&ak=${ak}&callback=${callback}`;
    
    if (this.loadedResources.has('baiduMapAPI')) {
      return window.BMap;
    }

    if (this.loadingPromises.has('baiduMapAPI')) {
      return this.loadingPromises.get('baiduMapAPI');
    }

    const loadingPromise = new Promise((resolve, reject) => {
      window[callback] = () => {
        this.loadedResources.add('baiduMapAPI');
        resolve(window.BMap);
      };

      const script = document.createElement('script');
      script.src = url;
      script.onerror = () => reject(new Error('Failed to load Baidu Map API'));
      document.head.appendChild(script);
    });

    this.loadingPromises.set('baiduMapAPI', loadingPromise);
    return loadingPromise;
  }

  /**
   * 懒加载百度地图库
   * @param {String} libName 库名称
   * @param {Object} options 选项
   * @returns {Promise} 加载 Promise
   */
  async loadBaiduMapLib(libName, options = {}) {
    const { version = '1.4' } = options;
    const resourceKey = `baiduMapLib_${libName}`;
    
    if (this.loadedResources.has(resourceKey)) {
      return window.BMapLib[libName];
    }

    if (this.loadingPromises.has(resourceKey)) {
      return this.loadingPromises.get(resourceKey);
    }

    const baseUrl = 'https://api.map.baidu.com/library';
    const jsUrl = `${baseUrl}/${libName}/${version}/src/${libName}_min.js`;
    const cssUrl = `${baseUrl}/${libName}/${version}/src/${libName}_min.css`;

    const loadingPromise = Promise.all([
      LazyLoader.script(jsUrl),
      LazyLoader.style(cssUrl)
    ]).then(() => {
      this.loadedResources.add(resourceKey);
      return window.BMapLib[libName];
    });

    this.loadingPromises.set(resourceKey, loadingPromise);
    return loadingPromise;
  }

  /**
   * 获取加载状态
   * @returns {Object} 加载状态
   */
  getLoadStatus() {
    return {
      loadedResources: Array.from(this.loadedResources),
      loadingCount: this.loadingPromises.size,
      isMapAPILoaded: this.loadedResources.has('baiduMapAPI')
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.loadedResources.clear();
    this.loadingPromises.clear();
  }
}

// 创建全局实例
export const mapResourceLoader = new MapResourceLoader();

export default {
  LazyLoader,
  MapResourceLoader,
  mapResourceLoader
};
