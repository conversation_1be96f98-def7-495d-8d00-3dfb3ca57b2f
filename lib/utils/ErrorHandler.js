/**
 * 错误处理系统
 * 提供统一的错误处理、日志记录和错误恢复机制
 */

import { formatError, ERROR_TYPES } from '../model/layer/types.js';
import eventBus from './event-bus.js';

/**
 * 错误级别枚举
 */
export const ERROR_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  FATAL: 'fatal'
};

/**
 * 错误处理器类
 */
export class ErrorHandler {
  constructor(options = {}) {
    this.options = {
      enableConsoleLog: options.enableConsoleLog !== false,
      enableRemoteLog: options.enableRemoteLog || false,
      maxErrorCount: options.maxErrorCount || 1000,
      enableRecovery: options.enableRecovery !== false,
      ...options
    };

    this.errors = [];
    this.errorCounts = new Map();
    this.recoveryStrategies = new Map();
    this.loggers = new Set();

    this._initDefaultRecoveryStrategies();
    this._initGlobalErrorHandlers();
  }

  /**
   * 处理错误
   * @param {Error|String} error 错误对象或错误消息
   * @param {Object} context 错误上下文
   * @param {String} level 错误级别
   */
  handle(error, context = {}, level = ERROR_LEVELS.ERROR) {
    const errorInfo = this._normalizeError(error, context, level);
    
    // 记录错误
    this._recordError(errorInfo);
    
    // 输出日志
    this._logError(errorInfo);
    
    // 尝试恢复
    if (this.options.enableRecovery && level !== ERROR_LEVELS.DEBUG && level !== ERROR_LEVELS.INFO) {
      this._attemptRecovery(errorInfo);
    }
    
    // 触发错误事件
    eventBus.emit('error', errorInfo);
    
    return errorInfo;
  }

  /**
   * 记录调试信息
   * @param {String} message 消息
   * @param {Object} context 上下文
   */
  debug(message, context = {}) {
    return this.handle(message, context, ERROR_LEVELS.DEBUG);
  }

  /**
   * 记录信息
   * @param {String} message 消息
   * @param {Object} context 上下文
   */
  info(message, context = {}) {
    return this.handle(message, context, ERROR_LEVELS.INFO);
  }

  /**
   * 记录警告
   * @param {String} message 消息
   * @param {Object} context 上下文
   */
  warn(message, context = {}) {
    return this.handle(message, context, ERROR_LEVELS.WARN);
  }

  /**
   * 记录错误
   * @param {Error|String} error 错误
   * @param {Object} context 上下文
   */
  error(error, context = {}) {
    return this.handle(error, context, ERROR_LEVELS.ERROR);
  }

  /**
   * 记录致命错误
   * @param {Error|String} error 错误
   * @param {Object} context 上下文
   */
  fatal(error, context = {}) {
    return this.handle(error, context, ERROR_LEVELS.FATAL);
  }

  /**
   * 注册恢复策略
   * @param {String} errorType 错误类型
   * @param {Function} strategy 恢复策略函数
   */
  registerRecoveryStrategy(errorType, strategy) {
    this.recoveryStrategies.set(errorType, strategy);
  }

  /**
   * 添加日志记录器
   * @param {Function} logger 日志记录器函数
   */
  addLogger(logger) {
    this.loggers.add(logger);
  }

  /**
   * 移除日志记录器
   * @param {Function} logger 日志记录器函数
   */
  removeLogger(logger) {
    this.loggers.delete(logger);
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计信息
   */
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      byLevel: {},
      byType: {},
      recent: this.errors.slice(-10)
    };

    // 按级别统计
    for (const level of Object.values(ERROR_LEVELS)) {
      stats.byLevel[level] = this.errors.filter(e => e.level === level).length;
    }

    // 按类型统计
    for (const [type, count] of this.errorCounts) {
      stats.byType[type] = count;
    }

    return stats;
  }

  /**
   * 清除错误记录
   * @param {String} level 要清除的错误级别，不传则清除所有
   */
  clearErrors(level) {
    if (level) {
      this.errors = this.errors.filter(e => e.level !== level);
    } else {
      this.errors = [];
      this.errorCounts.clear();
    }
  }

  /**
   * 标准化错误对象
   * @param {Error|String} error 错误
   * @param {Object} context 上下文
   * @param {String} level 级别
   * @returns {Object} 标准化的错误对象
   * @private
   */
  _normalizeError(error, context, level) {
    let message, stack, name;

    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
      name = error.name;
    } else {
      message = String(error);
      stack = new Error().stack;
      name = 'CustomError';
    }

    return {
      id: this._generateErrorId(),
      message,
      stack,
      name,
      level,
      context,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : '',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : ''
    };
  }

  /**
   * 记录错误
   * @param {Object} errorInfo 错误信息
   * @private
   */
  _recordError(errorInfo) {
    // 添加到错误列表
    this.errors.push(errorInfo);

    // 限制错误数量
    if (this.errors.length > this.options.maxErrorCount) {
      this.errors.shift();
    }

    // 统计错误类型
    const errorType = errorInfo.context.type || errorInfo.name;
    this.errorCounts.set(errorType, (this.errorCounts.get(errorType) || 0) + 1);
  }

  /**
   * 输出日志
   * @param {Object} errorInfo 错误信息
   * @private
   */
  _logError(errorInfo) {
    // 控制台日志
    if (this.options.enableConsoleLog) {
      const logMethod = console[errorInfo.level] || console.log;
      logMethod(`[${errorInfo.level.toUpperCase()}] ${errorInfo.message}`, errorInfo);
    }

    // 自定义日志记录器
    for (const logger of this.loggers) {
      try {
        logger(errorInfo);
      } catch (error) {
        console.error('Logger error:', error);
      }
    }

    // 远程日志
    if (this.options.enableRemoteLog && this.options.remoteLogUrl) {
      this._sendRemoteLog(errorInfo);
    }
  }

  /**
   * 发送远程日志
   * @param {Object} errorInfo 错误信息
   * @private
   */
  async _sendRemoteLog(errorInfo) {
    try {
      await fetch(this.options.remoteLogUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorInfo)
      });
    } catch (error) {
      console.error('Failed to send remote log:', error);
    }
  }

  /**
   * 尝试错误恢复
   * @param {Object} errorInfo 错误信息
   * @private
   */
  _attemptRecovery(errorInfo) {
    const errorType = errorInfo.context.type || errorInfo.name;
    const strategy = this.recoveryStrategies.get(errorType);

    if (strategy) {
      try {
        strategy(errorInfo);
      } catch (recoveryError) {
        console.error('Recovery strategy failed:', recoveryError);
      }
    }
  }

  /**
   * 初始化默认恢复策略
   * @private
   */
  _initDefaultRecoveryStrategies() {
    // 网络错误恢复
    this.registerRecoveryStrategy('NetworkError', (errorInfo) => {
      console.log('Attempting network error recovery...');
      // 可以实现重试逻辑
    });

    // 资源加载错误恢复
    this.registerRecoveryStrategy('ResourceLoadError', (errorInfo) => {
      console.log('Attempting resource load error recovery...');
      // 可以实现资源重新加载逻辑
    });

    // 地图API错误恢复
    this.registerRecoveryStrategy('MapAPIError', (errorInfo) => {
      console.log('Attempting map API error recovery...');
      // 可以实现地图重新初始化逻辑
    });
  }

  /**
   * 初始化全局错误处理器
   * @private
   */
  _initGlobalErrorHandlers() {
    if (typeof window !== 'undefined') {
      // 捕获未处理的错误
      window.addEventListener('error', (event) => {
        this.error(event.error || event.message, {
          type: 'UnhandledError',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        });
      });

      // 捕获未处理的Promise拒绝
      window.addEventListener('unhandledrejection', (event) => {
        this.error(event.reason, {
          type: 'UnhandledPromiseRejection'
        });
      });
    }
  }

  /**
   * 生成错误ID
   * @returns {String} 错误ID
   * @private
   */
  _generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 错误边界装饰器
 * @param {Function} target 目标函数
 * @param {Object} options 选项
 * @returns {Function} 包装后的函数
 */
export function withErrorBoundary(target, options = {}) {
  const { fallback, onError } = options;

  return function(...args) {
    try {
      const result = target.apply(this, args);
      
      // 处理Promise
      if (result && typeof result.catch === 'function') {
        return result.catch(error => {
          errorHandler.error(error, { function: target.name });
          if (onError) onError(error);
          return fallback;
        });
      }
      
      return result;
    } catch (error) {
      errorHandler.error(error, { function: target.name });
      if (onError) onError(error);
      return fallback;
    }
  };
}

/**
 * 重试装饰器
 * @param {Number} maxRetries 最大重试次数
 * @param {Number} delay 重试延迟
 * @returns {Function} 装饰器函数
 */
export function withRetry(maxRetries = 3, delay = 1000) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function(...args) {
      let lastError;
      
      for (let i = 0; i <= maxRetries; i++) {
        try {
          return await originalMethod.apply(this, args);
        } catch (error) {
          lastError = error;
          
          if (i < maxRetries) {
            errorHandler.warn(`Retry ${i + 1}/${maxRetries} for ${propertyKey}`, { error });
            await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
          }
        }
      }
      
      errorHandler.error(`All retries failed for ${propertyKey}`, { error: lastError });
      throw lastError;
    };

    return descriptor;
  };
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler();

export default {
  ErrorHandler,
  ERROR_LEVELS,
  errorHandler,
  withErrorBoundary,
  withRetry
};
