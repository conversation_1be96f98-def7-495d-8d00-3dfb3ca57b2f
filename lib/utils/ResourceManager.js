/**
 * 资源管理器
 * 统一管理应用中的资源，防止内存泄漏
 */

/**
 * 资源管理器类
 */
export class ResourceManager {
  constructor() {
    this.resources = new Map();
    this.eventListeners = new Set();
    this.timers = new Set();
    this.intervals = new Set();
    this.observers = new Set();
    this.subscriptions = new Set();
  }

  /**
   * 注册资源
   * @param {String} id 资源ID
   * @param {Object} resource 资源对象
   * @param {Function} cleanup 清理函数
   */
  register(id, resource, cleanup) {
    if (this.resources.has(id)) {
      console.warn(`Resource ${id} already exists, replacing...`);
      this.unregister(id);
    }

    this.resources.set(id, {
      resource,
      cleanup: cleanup || (() => {}),
      createdAt: Date.now()
    });
  }

  /**
   * 注销资源
   * @param {String} id 资源ID
   */
  unregister(id) {
    const resourceInfo = this.resources.get(id);
    if (resourceInfo) {
      try {
        resourceInfo.cleanup(resourceInfo.resource);
      } catch (error) {
        console.error(`Error cleaning up resource ${id}:`, error);
      }
      this.resources.delete(id);
    }
  }

  /**
   * 获取资源
   * @param {String} id 资源ID
   * @returns {Object} 资源对象
   */
  get(id) {
    const resourceInfo = this.resources.get(id);
    return resourceInfo ? resourceInfo.resource : null;
  }

  /**
   * 添加事件监听器
   * @param {Object} target 目标对象
   * @param {String} event 事件名
   * @param {Function} handler 处理函数
   * @param {Object} options 选项
   */
  addEventListener(target, event, handler, options = {}) {
    target.addEventListener(event, handler, options);
    
    const listenerInfo = {
      target,
      event,
      handler,
      options,
      id: `${event}_${Date.now()}_${Math.random()}`
    };
    
    this.eventListeners.add(listenerInfo);
    return listenerInfo.id;
  }

  /**
   * 移除事件监听器
   * @param {String} listenerId 监听器ID
   */
  removeEventListener(listenerId) {
    for (const listener of this.eventListeners) {
      if (listener.id === listenerId) {
        listener.target.removeEventListener(listener.event, listener.handler, listener.options);
        this.eventListeners.delete(listener);
        break;
      }
    }
  }

  /**
   * 清理所有事件监听器
   */
  clearAllEventListeners() {
    for (const listener of this.eventListeners) {
      try {
        listener.target.removeEventListener(listener.event, listener.handler, listener.options);
      } catch (error) {
        console.error('Error removing event listener:', error);
      }
    }
    this.eventListeners.clear();
  }

  /**
   * 设置定时器
   * @param {Function} callback 回调函数
   * @param {Number} delay 延迟时间
   * @returns {Number} 定时器ID
   */
  setTimeout(callback, delay) {
    const timerId = setTimeout(callback, delay);
    this.timers.add(timerId);
    return timerId;
  }

  /**
   * 清除定时器
   * @param {Number} timerId 定时器ID
   */
  clearTimeout(timerId) {
    clearTimeout(timerId);
    this.timers.delete(timerId);
  }

  /**
   * 清理所有定时器
   */
  clearAllTimers() {
    for (const timerId of this.timers) {
      clearTimeout(timerId);
    }
    this.timers.clear();
  }

  /**
   * 设置间隔器
   * @param {Function} callback 回调函数
   * @param {Number} interval 间隔时间
   * @returns {Number} 间隔器ID
   */
  setInterval(callback, interval) {
    const intervalId = setInterval(callback, interval);
    this.intervals.add(intervalId);
    return intervalId;
  }

  /**
   * 清除间隔器
   * @param {Number} intervalId 间隔器ID
   */
  clearInterval(intervalId) {
    clearInterval(intervalId);
    this.intervals.delete(intervalId);
  }

  /**
   * 清理所有间隔器
   */
  clearAllIntervals() {
    for (const intervalId of this.intervals) {
      clearInterval(intervalId);
    }
    this.intervals.clear();
  }

  /**
   * 添加观察者
   * @param {Object} observer 观察者对象
   * @returns {String} 观察者ID
   */
  addObserver(observer) {
    const observerId = `observer_${Date.now()}_${Math.random()}`;
    this.observers.set(observerId, observer);
    return observerId;
  }

  /**
   * 移除观察者
   * @param {String} observerId 观察者ID
   */
  removeObserver(observerId) {
    const observer = this.observers.get(observerId);
    if (observer) {
      try {
        if (observer.disconnect) {
          observer.disconnect();
        }
        if (observer.unobserve) {
          observer.unobserve();
        }
      } catch (error) {
        console.error('Error disconnecting observer:', error);
      }
      this.observers.delete(observerId);
    }
  }

  /**
   * 清理所有观察者
   */
  clearAllObservers() {
    for (const [id, observer] of this.observers) {
      try {
        if (observer.disconnect) {
          observer.disconnect();
        }
        if (observer.unobserve) {
          observer.unobserve();
        }
      } catch (error) {
        console.error('Error disconnecting observer:', error);
      }
    }
    this.observers.clear();
  }

  /**
   * 添加订阅
   * @param {Object} subscription 订阅对象
   * @returns {String} 订阅ID
   */
  addSubscription(subscription) {
    const subscriptionId = `subscription_${Date.now()}_${Math.random()}`;
    this.subscriptions.set(subscriptionId, subscription);
    return subscriptionId;
  }

  /**
   * 移除订阅
   * @param {String} subscriptionId 订阅ID
   */
  removeSubscription(subscriptionId) {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      try {
        if (subscription.unsubscribe) {
          subscription.unsubscribe();
        }
        if (subscription.cancel) {
          subscription.cancel();
        }
      } catch (error) {
        console.error('Error unsubscribing:', error);
      }
      this.subscriptions.delete(subscriptionId);
    }
  }

  /**
   * 清理所有订阅
   */
  clearAllSubscriptions() {
    for (const [id, subscription] of this.subscriptions) {
      try {
        if (subscription.unsubscribe) {
          subscription.unsubscribe();
        }
        if (subscription.cancel) {
          subscription.cancel();
        }
      } catch (error) {
        console.error('Error unsubscribing:', error);
      }
    }
    this.subscriptions.clear();
  }

  /**
   * 清理所有资源
   */
  clearAll() {
    // 清理注册的资源
    for (const [id] of this.resources) {
      this.unregister(id);
    }

    // 清理事件监听器
    this.clearAllEventListeners();

    // 清理定时器
    this.clearAllTimers();

    // 清理间隔器
    this.clearAllIntervals();

    // 清理观察者
    this.clearAllObservers();

    // 清理订阅
    this.clearAllSubscriptions();
  }

  /**
   * 获取资源统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      resources: this.resources.size,
      eventListeners: this.eventListeners.size,
      timers: this.timers.size,
      intervals: this.intervals.size,
      observers: this.observers.size,
      subscriptions: this.subscriptions.size
    };
  }

  /**
   * 获取详细的资源信息
   * @returns {Object} 详细信息
   */
  getDetailedInfo() {
    return {
      resources: Array.from(this.resources.keys()),
      eventListeners: Array.from(this.eventListeners).map(l => ({
        id: l.id,
        event: l.event,
        target: l.target.constructor.name
      })),
      timers: Array.from(this.timers),
      intervals: Array.from(this.intervals),
      observers: Array.from(this.observers.keys()),
      subscriptions: Array.from(this.subscriptions.keys())
    };
  }

  /**
   * 检查资源泄漏
   * @returns {Object} 泄漏检查结果
   */
  checkLeaks() {
    const stats = this.getStats();
    const warnings = [];

    if (stats.eventListeners > 100) {
      warnings.push(`Too many event listeners: ${stats.eventListeners}`);
    }

    if (stats.timers > 50) {
      warnings.push(`Too many timers: ${stats.timers}`);
    }

    if (stats.intervals > 20) {
      warnings.push(`Too many intervals: ${stats.intervals}`);
    }

    if (stats.observers > 50) {
      warnings.push(`Too many observers: ${stats.observers}`);
    }

    return {
      hasLeaks: warnings.length > 0,
      warnings,
      stats
    };
  }
}

// 创建全局资源管理器实例
export const globalResourceManager = new ResourceManager();

// 在页面卸载时自动清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    globalResourceManager.clearAll();
  });
}

export default ResourceManager;
