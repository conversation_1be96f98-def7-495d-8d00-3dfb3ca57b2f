<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图库增强功能测试</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #40a9ff;
        }
        
        #mapContainer {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>地图库增强功能测试</h1>
        
        <div class="controls">
            <h3>省份多边形测试</h3>
            <button onclick="testProvincePolygon('广东省')">显示广东省边界</button>
            <button onclick="testProvincePolygon('北京市')">显示北京市边界</button>
            <button onclick="testProvincePolygon('上海市')">显示上海市边界</button>
            <button onclick="clearProvincePolygon()">清除省份边界</button>
            
            <h3>信息窗口测试</h3>
            <button onclick="testInfoWindow()">显示信息窗口</button>
            <button onclick="testCustomInfoWindow()">显示自定义信息窗口</button>
            <button onclick="closeInfoWindow()">关闭信息窗口</button>
            
            <h3>地图控制测试</h3>
            <button onclick="testMapCenter()">设置地图中心</button>
            <button onclick="testMapZoom()">设置缩放级别</button>
        </div>
        
        <div id="mapContainer"></div>
        
        <div class="status" id="status">
            <h3>测试状态</h3>
            <p>等待测试...</p>
        </div>
    </div>

    <script type="module">
        import Map from './model/Map.js';
        import MapSettings from './model/MapSettings.js';
        
        let mapInstance = null;
        
        // 初始化地图
        function initMap() {
            const settings = new MapSettings({
                center: { lng: 116.404, lat: 39.915 },
                zoom: 11,
                dragEnabled: true
            });
            
            mapInstance = new Map('mapContainer', null, settings);
            updateStatus('地图初始化完成');
        }
        
        // 更新状态显示
        function updateStatus(message) {
            const statusElement = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusElement.innerHTML = `
                <h3>测试状态</h3>
                <p><strong>[${timestamp}]</strong> ${message}</p>
            `;
        }
        
        // 测试省份多边形
        window.testProvincePolygon = function(province) {
            if (!mapInstance) {
                updateStatus('错误：地图未初始化');
                return;
            }
            
            try {
                mapInstance.provincePolygon.setProvince(province, { autoViewport: true });
                updateStatus(`正在显示 ${province} 的边界`);
            } catch (error) {
                updateStatus(`错误：${error.message}`);
            }
        };
        
        // 清除省份多边形
        window.clearProvincePolygon = function() {
            if (!mapInstance) {
                updateStatus('错误：地图未初始化');
                return;
            }
            
            try {
                mapInstance.provincePolygon.clearMarkProvince();
                updateStatus('已清除省份边界');
            } catch (error) {
                updateStatus(`错误：${error.message}`);
            }
        };
        
        // 测试信息窗口
        window.testInfoWindow = function() {
            if (!mapInstance) {
                updateStatus('错误：地图未初始化');
                return;
            }
            
            try {
                const center = mapInstance.getCenter();
                mapInstance.openInfoWindow(center, {
                    title: '测试信息窗口',
                    content: '这是一个测试信息窗口，用于验证信息窗口功能是否正常工作。',
                    onConfirm: () => {
                        updateStatus('用户点击了确认按钮');
                        mapInstance.closeInfoWindow();
                    },
                    onCancel: () => {
                        updateStatus('用户点击了取消按钮');
                        mapInstance.closeInfoWindow();
                    }
                });
                updateStatus('信息窗口已打开');
            } catch (error) {
                updateStatus(`错误：${error.message}`);
            }
        };
        
        // 测试自定义信息窗口
        window.testCustomInfoWindow = function() {
            if (!mapInstance) {
                updateStatus('错误：地图未初始化');
                return;
            }
            
            try {
                const center = mapInstance.getCenter();
                mapInstance.openInfoWindow(center, {
                    title: '自定义信息窗口',
                    content: `
                        <div style="padding: 10px;">
                            <h4>地图坐标信息</h4>
                            <p><strong>经度：</strong>${center.lng.toFixed(6)}</p>
                            <p><strong>纬度：</strong>${center.lat.toFixed(6)}</p>
                            <p><strong>缩放级别：</strong>${mapInstance.getZoom()}</p>
                        </div>
                    `,
                    onConfirm: () => {
                        updateStatus('确认查看坐标信息');
                        mapInstance.closeInfoWindow();
                    },
                    onCancel: () => {
                        updateStatus('取消查看坐标信息');
                        mapInstance.closeInfoWindow();
                    }
                });
                updateStatus('自定义信息窗口已打开');
            } catch (error) {
                updateStatus(`错误：${error.message}`);
            }
        };
        
        // 关闭信息窗口
        window.closeInfoWindow = function() {
            if (!mapInstance) {
                updateStatus('错误：地图未初始化');
                return;
            }
            
            try {
                mapInstance.closeInfoWindow();
                updateStatus('信息窗口已关闭');
            } catch (error) {
                updateStatus(`错误：${error.message}`);
            }
        };
        
        // 测试地图中心设置
        window.testMapCenter = function() {
            if (!mapInstance) {
                updateStatus('错误：地图未初始化');
                return;
            }
            
            try {
                // 设置到上海
                const shanghaiCenter = new BMap.Point(121.4737, 31.2304);
                mapInstance.setCenter(shanghaiCenter);
                updateStatus('地图中心已设置到上海');
            } catch (error) {
                updateStatus(`错误：${error.message}`);
            }
        };
        
        // 测试缩放级别设置
        window.testMapZoom = function() {
            if (!mapInstance) {
                updateStatus('错误：地图未初始化');
                return;
            }
            
            try {
                const currentZoom = mapInstance.getZoom();
                const newZoom = currentZoom >= 15 ? 8 : 15;
                mapInstance.setZoom(newZoom);
                updateStatus(`缩放级别已从 ${currentZoom} 调整到 ${newZoom}`);
            } catch (error) {
                updateStatus(`错误：${error.message}`);
            }
        };
        
        // 页面加载完成后初始化地图
        document.addEventListener('DOMContentLoaded', function() {
            // 等待百度地图API加载完成
            if (typeof BMap !== 'undefined') {
                initMap();
            } else {
                // 如果API还未加载，等待一段时间后重试
                setTimeout(() => {
                    if (typeof BMap !== 'undefined') {
                        initMap();
                    } else {
                        updateStatus('错误：百度地图API加载失败，请检查API密钥');
                    }
                }, 2000);
            }
        });
    </script>
</body>
</html>
