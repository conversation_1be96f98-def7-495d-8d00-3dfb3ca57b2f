# @daas/map-lib

内置地图组件库，基于百度地图 API 封装的 Vue 组件。

## 安装

```bash
npm install @daas/map-lib
```

## 使用方法

### 基础用法

```vue
<script>
import Map from "@daas/map-lib";

export default {
  components: {
    Map,
  },
  data() {
    return {
      // 标记点数据
      markers: [
        {
          lat: 28.2568,
          lng: 112.9388,
          options: {
            icon: {
              url: "http://************:8081/daas/static/img/icon/side-menu/architecture-design.svg",
              size: {
                width: 140,
                height: 140,
              },
            },
          },
          onClick(ev) {
            console.log("🚀 ~ onClick ~ ev:", ev);
          },
        },
      ],
      // 多边形
      polygons: [
        {
          pointList: [
            {
              lat: 23.029112,
              lng: 113.164435,
            },
            {
              lat: 23.229163,
              lng: 113.364435,
            },
            {
              lat: 23.329163,
              lng: 113.564435,
            },
            {
              lat: 23.229163,
              lng: 113.764435,
            },
            {
              lat: 23.029112,
              lng: 113.864435,
            },
            {
              lat: 22.829112,
              lng: 113.764435,
            },
            {
              lat: 22.729112,
              lng: 113.564435,
            },
            {
              lat: 22.829112,
              lng: 113.364435,
            },
            {
              lat: 22.929112,
              lng: 113.264435,
            },
            {
              lat: 22.979112,
              lng: 113.184435,
            },
          ],
          onClick(ev) {
            console.log("🚀 ~ onClick ~ ev:", ev);
          },
        },
      ],
      // 线
      polylines: [
        {
          pointList: [
            {
              lat: 30.5928,
              lng: 114.3055,
            },
            {
              lat: 30.6304,
              lng: 114.2195,
            },
            {
              lat: 30.668,
              lng: 114.1335,
            },
            {
              lat: 30.7056,
              lng: 114.0475,
            },
            {
              lat: 30.7432,
              lng: 113.9615,
            },
            {
              lat: 30.7808,
              lng: 113.8755,
            },
            {
              lat: 30.8184,
              lng: 113.7895,
            },
            {
              lat: 30.856,
              lng: 113.7035,
            },
            {
              lat: 30.8936,
              lng: 113.6175,
            },
            {
              lat: 30.9312,
              lng: 113.5315,
            },
            {
              lat: 30.9688,
              lng: 113.4455,
            },
          ],
          onClick(ev) {
            console.log("🚀 ~ onClick ~ ev:", ev);
          },
        },
      ],
      // 地图配置
      mapConfig: {},
      // 中心点
      center: {
        lat: 30.5928,
        lng: 114.3055,
      },
      // 缩放级别
      zoom: 10,
      coordinateIndex: 0,
      timer: null,

      // 中国主要城市坐标数据
      chinaCoordinates: [
        {
          lat: 23.129163,
          lng: 113.264435,
        },
        {
          lat: 22.548457,
          lng: 114.057868,
        },
        {
          lat: 21.277122,
          lng: 110.365067,
        },
        {
          lat: 23.037447,
          lng: 113.126586,
        },
        {
          lat: 22.937976,
          lng: 112.04449,
        },
        { lat: 39.9042, lng: 116.4074 },
        { lat: 31.2304, lng: 121.4737 },
        { lat: 30.5928, lng: 114.3055 },
        { lat: 29.5633, lng: 106.553 },
        { lat: 36.0614, lng: 103.8341 },
        { lat: 30.654, lng: 104.0655 },
        { lat: 34.2652, lng: 108.9541 },
        { lat: 38.4681, lng: 106.2731 },
        { lat: 43.8256, lng: 87.6168 },
        { lat: 25.0389, lng: 102.7183 },
        { lat: 36.6683, lng: 117.0206 },
        { lat: 24.4798, lng: 118.0819 },
      ],
    };
  },
  watch: {
    markers: {
      handler(newVal) {
        console.log("🚀 ~ markers ~ newVal:", newVal);
      },
      deep: true,
    },
  },
  methods: {
    // 地图加载完成事件处理
    handleMapMounted(mapInstance) {
      console.log("地图已加载", mapInstance);

      this.timer = setInterval(() => {
        if (this.coordinateIndex >= this.chinaCoordinates.length) {
          clearInterval(this.timer);
          return;
        }

        // 循环使用中国城市坐标
        const nextCoordinate =
          this.chinaCoordinates[
            this.coordinateIndex % this.chinaCoordinates.length
          ];

        // 使用组件实例方法添加标记点
        if (this.$refs.mapRef) {
          this.$refs.mapRef.addMarker(nextCoordinate);
        } else {
          this.markers.push(nextCoordinate);
        }

        this.coordinateIndex++;
      }, 1000);
    },
    // 地图销毁事件处理
    handleMapDestroy(mapInstance) {
      console.log("地图已销毁", mapInstance);
      this.timer && clearInterval(this.timer);
    },
    // 添加随机标记点
    addRandomMarker() {
      // 生成中国范围内的随机坐标
      const randomLat = 18 + Math.random() * 30; // 中国纬度范围大约在18-48之间
      const randomLng = 73 + Math.random() * 62; // 中国经度范围大约在73-135之间

      const newMarker = {
        lat: randomLat,
        lng: randomLng,
        options: {
          icon: {
            url: "http://************:8081/daas/static/img/icon/side-menu/architecture-design.svg",
            size: {
              width: 140,
              height: 140,
            },
          },
        },
      };

      // 使用地图实例方法添加或直接添加到数组
      if (this.$refs.mapRef) {
        this.$refs.mapRef.addMarker(newMarker);
      } else {
        this.markers.push(newMarker);
      }

      console.log("已添加标记点", newMarker);
    },
    // 添加随机线段
    addRandomPolyline() {
      // 初始化多段线数组，如果不存在
      if (!this.polylines) {
        this.polylines = [];
      }

      // 创建一条有3-5个点的随机线段
      const pointCount = 3 + Math.floor(Math.random() * 3);
      const path = [];

      // 生成随机点
      for (let i = 0; i < pointCount; i++) {
        const randomLat = 18 + Math.random() * 30;
        const randomLng = 73 + Math.random() * 62;
        path.push({ lat: randomLat, lng: randomLng });
      }

      const newPolyline = {
        pointList: path,
        options: {
          strokeColor: `#${Math.floor(Math.random() * 16777215).toString(16)}`, // 随机颜色
          strokeWeight: 3 + Math.floor(Math.random() * 5), // 3-7的线宽
          strokeOpacity: 0.8,
        },
      };

      if (this.$refs.mapRef) {
        this.$refs.mapRef.addPolyline(newPolyline);
      } else {
        this.polylines.push(newPolyline);
      }
      console.log("已添加线段", newPolyline);
    },
    // 添加随机多边形
    addRandomPolygon() {
      // 初始化多边形数组，如果不存在
      if (!this.polygons) {
        this.polygons = [];
      }

      // 创建一个有4-6个点的随机多边形
      const pointCount = 4 + Math.floor(Math.random() * 3);
      const pointList = [];

      // 以某个中心点为基准，生成围绕它的多边形顶点
      const centerLat = 18 + Math.random() * 30;
      const centerLng = 73 + Math.random() * 62;
      const radius = 0.5 + Math.random() * 2; // 0.5-2.5度的半径

      for (let i = 0; i < pointCount; i++) {
        const angle = (i / pointCount) * 2 * Math.PI;
        const lat = centerLat + radius * Math.sin(angle);
        const lng = centerLng + radius * Math.cos(angle);
        pointList.push({ lat, lng });
      }

      const newPolygon = {
        pointList,
        options: {
          strokeColor: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
          strokeWeight: 2,
          strokeOpacity: 0.8,
          fillColor: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
          fillOpacity: 0.4,
        },
      };

      if (this.$refs.mapRef) {
        this.$refs.mapRef.addPolygon(newPolygon);
      } else {
        this.polygons.push(newPolygon);
      }
      console.log("已添加多边形", newPolygon);
    },
  },
  beforeUnmount() {
    this.timer && clearInterval(this.timer);
  },
};
</script>

<template>
  <div class="map-container">
    <!-- 控制按钮 -->
    <div class="map-container-header">
      <a-space>
        <a-button @click="addRandomMarker" class="control-button">
          添加标记点
        </a-button>
        <a-button @click="addRandomPolyline" class="control-button">
          添加线段
        </a-button>
        <a-button @click="addRandomPolygon" class="control-button">
          添加多边形
        </a-button>
      </a-space>
    </div>

    <!-- 地图组件 -->
    <Map
      ref="mapRef"
      class="map-container-map"
      :markers.sync="markers"
      :polygons.sync="polygons"
      :polylines.sync="polylines"
      :mapConfig="mapConfig"
      :center="center"
      :zoom="zoom"
      @onMounted="handleMapMounted"
      @onDestroy="handleMapDestroy"
    />
  </div>
</template>

<style lang="less" scoped>
.map-container {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 12px;
  flex-direction: column;
  gap: 12px;
  .map-container-header {
    flex: none;
  }
  .map-container-map {
    flex: 1;
  }
}
</style>

```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| markers | Array | [{lat: string, lng: string, options: MarkerOptions}] | 标记点数组 |
| polygons | Array | [{pointList: Array<{lat: string, lng: string}>, options: PolygonOptions}] | 多边形数组 |
| polylines | Array | [{pointList: Array<{lat: string, lng: string}>, options: PolylineOptions}] | 折线数组 |
| mapConfig | Object | {} | 地图配置项 |
| center | Object | {} | 地图中心点 |
| zoom | Number | 12 | 地图缩放级别 |

### MarkerOptions

- Marker的参数。它没有构造函数，但可通过对象字面量形式表示。
  
| 属性 | 类型 | 描述 |
|------|------|------|
| offset | Size | 标注的位置偏移值 |
| icon | Icon | 标注所用的图标对象, 参考下面的Icon |
| raiseOnDrag | Boolean | 拖拽标注时，标注是否开启离开地图表面效果。默认为false |
| draggingCursor | String | 拖拽标注时的鼠标指针样式。此属性值需遵循CSS的cursor属性规范 |
| rotation | Number | 旋转角度 |
| shadow | Icon | 阴影图标 |
| title | String | 鼠标移到marker上的显示内容 |

### MarkerEvent

| 事件名 | 回调参数 | 说明 |
|--------|----------|------|
| onClick | Function(e: Event, cell: Marker) | 点击事件 |
| onDblclick | Function(e: Event, cell: Marker) | 双击事件 |
| onMousedown | Function(e: Event, cell: Marker) | 鼠标按下事件 |
| onMouseup | Function(e: Event, cell: Marker) | 鼠标抬起事件 |
| onMouseout | Function(e: Event, cell: Marker) | 鼠标移出事件 |
| onMouseover | Function(e: Event, cell: Marker) | 鼠标移入事件 |
| onRemove | Function(e: Event, cell: Marker) | 移除事件 |
| onInfowindowclose | Function(e: Event, cell: Marker) | 信息窗口关闭事件 |
| onInfowindowopen | Function(e: Event, cell: Marker) | 信息窗口打开事件 |
| onDragstart | Function(e: Event, cell: Marker) | 拖拽开始事件 |
| onDragging | Function(e: Event, cell: Marker) | 拖拽中事件 |
| onDragend | Function(e: Event, cell: Marker) | 拖拽结束事件 |
| onRightclick | Function(e: Event, cell: Marker) | 右键点击事件 |

### Icon

- Marker的图标参数。它没有构造函数，但可通过对象字面量形式表示。

| 属性 | 类型 | 描述 |
|------|------|------|
| anchor | Size | 图标的定位点相对于图标左上角的偏移值 |
| size | Size | 图标可视区域的大小 |
| imageOffset | Size | 图标所用的图片相对于可视区域的偏移值，此功能的作用等同于CSS中的background-position属性 |
| imageSize | Size | 图标所用的图片的大小，此功能的作用等同于CSS中的background-size属性。可用于实现高清屏的高清效果 |
| imageUrl | String | 图标所用图像资源的位置 |
| infoWindowAnchor | Size | 信息窗口开启位置相对于图标左上角的偏移值 |

### PolygonOptions

- 此类表示Polygon构造函数的可选参数。它没有构造函数，但可通过对象字面量形式表示。

| 属性 | 类型 | 描述 |
|------|------|------|
| strokeColor | String | 边线颜色 |
| fillColor | String | 填充颜色。当参数为空时，折线覆盖物将没有填充效果 |
| strokeWeight | Number | 边线的宽度，以像素为单位 |
| strokeOpacity | Number | 边线透明度，取值范围0 - 1 |
| fillOpacity | Number | 填充的透明度，取值范围0 - 1 |
| strokeStyle | String | 边线的样式，solid或dashed |

### PolygonEvent

| 事件名 | 回调参数 | 说明 |
|--------|----------|------|
| onClick | Function(e: Event, cell: Polygon) | 点击事件 |
| onDblclick | Function(e: Event, cell: Polygon) | 双击事件 |
| onRightclick | Function(e: Event, cell: Marker) | 右键点击事件 |

### PolylineOptions

- 此类表示Polyline构造函数的可选参数。它没有构造函数，但可通过对象字面量形式表示。

| 属性 | 类型 | 描述 |
|------|------|------|
| strokeColor | String | 折线颜色 |
| strokeWeight | Number | 折线的宽度，以像素为单位 |
| strokeOpacity | Number | 折线的透明度，取值范围0 - 1 |
| strokeStyle | String | 折线的样式，solid或dashed |
| icons | Array<IconSequence> | 配置贴合折线的图标 |

### PolylineEvent

| 事件名 | 回调参数 | 说明 |
|--------|----------|------|
| onClick | Function(e: Event, cell: Polyline) | 点击事件 |
| onDblclick | Function(e: Event, cell: Polyline) | 双击事件 |
| onRightclick | Function(e: Event, cell: Marker) | 右键点击事件 |

## MapEvents

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onMounted | 地图加载完成时触发 | mapInstance: 地图实例 |
| onDestroy | 地图销毁时触发 | mapInstance: 地图实例 |

## 方法

组件实例提供以下方法：

### 获取实例
- `getMap()`: 获取地图实例
- `getMarkerLayer()`: 获取标记点图层实例
- `getPolygonLayer()`: 获取多边形图层实例
- `getPolylineLayer()`: 获取折线图层实例

### 添加覆盖物
- `addMarker(cell)`: 添加单个标记点
- `addPolygon(cell)`: 添加单个多边形
- `addPolyline(cell)`: 添加单个折线

### 批量添加覆盖物
- `batchAddMarker(cells)`: 批量添加标记点
- `batchAddPolygon(cells)`: 批量添加多边形
- `batchAddPolyline(cells)`: 批量添加折线

## 注意事项

1. 组件会自动加载百度地图 API
2. 组件会自动处理图层的清理和重新渲染
3. 组件会在销毁时自动清理所有资源
