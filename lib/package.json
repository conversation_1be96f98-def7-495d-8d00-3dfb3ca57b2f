{"name": "@daas/map-lib", "version": "0.1.0", "description": "内置地图组件库", "author": "", "license": "ISC", "main": "index.js", "files": ["index.js", "components", "model", "utils", "README.md", "Map.vue", "package.json"], "keywords": [], "directories": {}, "scripts": {"published": "npm publish  --registry=https://repo.bingosoft.net/repository/npm-bingo/", "published:dev": "npm publish --registry=https://repo.bingosoft.net/repository/npm-snapshot/", "cinstall": "rimraf -rf package-lock.json yarn.lock node_modules/@daas && npm i"}, "dependencies": {"lodash-es": "^4.17.21"}, "devDependencies": {"@daas/cli": "5.0.0-snapshot"}}