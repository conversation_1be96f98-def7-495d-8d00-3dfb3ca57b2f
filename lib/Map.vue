<script>
import { defineComponent, onMounted, onUnmounted, watch } from "vue";
import { loadJS, loadBaiduMap } from "./utils/preload";
import Map from "./model/Map.js";
import Layer from "./model/Layer.js";
import eventBus, { eventMap } from "./utils/event-bus";
import { cloneDeep } from "lodash-es";

export default defineComponent({
  name: "MapComponent",
  props: {
    markers: {
      type: Array,
      default: () => [],
    },
    polygons: {
      type: Array,
      default: () => [],
    },
    polylines: {
      type: Array,
      default: () => [],
    },
    mapConfig: {
      type: Object,
      default: () => ({}),
    },
    center: {
      type: Object,
      default: () => ({}),
    },
    zoom: {
      type: Number,
      default: 12,
    },
  },
  emits: ["onMounted", "onDestroy", "update:markers", "update:polygons", "update:polylines"],
  setup(props, { emit }) {
    // 地图和图层实例
    let map = null;
    let markerLayer = null;
    let polygonLayer = null;
    let polylineLayer = null;

    // 监听器管理
    const watchHandlers = [];
    const eventListeners = new Set();
    const watcherTypes = new Set();

    // 标记是否来自内部更新，防止死循环
    let isInternalUpdate = false;

    /**
     * 初始化地图库
     */
    async function init() {
      await loadBaiduMap();
      await loadJS("/daas/static/maps/maplib/DrawingManager.js");
    }

    /**
     * 创建图层数据监听器
     */
    function createLayerWatcher(layerType, layer) {
      return watch(
        () => props[layerType],
        (newVal) => {
          if (isInternalUpdate) {
            isInternalUpdate = false;
            return;
          }
          layer.clear();
          layer.batchAdd(cloneDeep(newVal));
        },
        { deep: true },
      );
    }

    /**
     * 初始化各类图层
     */
    function initLayers() {
      markerLayer = new Layer(map._map, {
        type: "marker",
        coverCells: [...props.markers],
      });

      polygonLayer = new Layer(map._map, {
        type: "polygon",
        coverCells: [...props.polygons],
      });

      polylineLayer = new Layer(map._map, {
        type: "polyline",
        coverCells: [...props.polylines],
      });
    }

    /**
     * 添加事件监听器（防重复）
     */
    function addEventListenerOnce(eventName, callback) {
      const eventKey = `${eventName}:${callback.name || "anonymous"}`;
      if (!eventListeners.has(eventKey)) {
        eventBus.on(eventName, callback);
        eventListeners.add(eventKey);
        return true;
      }
      return false;
    }

    /**
     * 添加数据监听器（防重复）
     */
    function addWatcher(watcherType, watcherFn) {
      if (!watcherTypes.has(watcherType)) {
        watchHandlers.push(watcherFn);
        watcherTypes.add(watcherType);
        return true;
      }
      return false;
    }

    /**
     * 设置所有数据监听器
     */
    function setupWatchers() {
      // 图层数据监听
      addWatcher("markers", createLayerWatcher("markers", markerLayer));
      addWatcher("polygons", createLayerWatcher("polygons", polygonLayer));
      addWatcher("polylines", createLayerWatcher("polylines", polylineLayer));

      // 地图属性监听
      addWatcher(
        "center",
        watch(
          () => props.center,
          (newVal) => {
            map.setCenter(newVal);
          },
          { deep: true },
        ),
      );

      addWatcher(
        "zoom",
        watch(
          () => props.zoom,
          (newVal) => {
            map.setZoom(newVal);
          },
        ),
      );
    }

    /**
     * 清理所有监听器
     */
    function cleanupWatchers() {
      // 清理数据监听器
      watchHandlers.forEach((unwatch) => unwatch?.());
      watchHandlers.length = 0;
      watcherTypes.clear();

      // 清理事件监听器
      eventListeners.forEach((eventKey) => {
        const [eventName] = eventKey.split(":");
        eventBus.off(eventName);
      });
      eventListeners.clear();
    }

    /**
     * 更新图层数据到父组件
     */
    function updateLayerData(layerType, layer) {
      if (layer) {
        const cells = layer.layerConfig.coverCells.map((cell) => {
          // 从覆盖物实例转为原始配置对象
          return cloneDeep(cell.param || cell);
        });

        isInternalUpdate = true;
        emit(`update:${layerType}`, cells);
      }
    }

    // 地图初始化事件处理
    function handleMapInit(mapInstance) {
      emit("onMounted", mapInstance);
    }
    addEventListenerOnce(eventMap.mapInit, handleMapInit);

    // 地图销毁事件处理
    function handleMapDestroy(mapInstance) {
      cleanupWatchers();
      emit("onDestroy", mapInstance);
    }
    addEventListenerOnce(eventMap.mapDestroy, handleMapDestroy);

    // 组件挂载
    onMounted(async () => {
      await init();

      // 初始化地图
      map = new Map(
        "map-container",
        (type, data) => {
          console.log(type, data);
        },
        props.mapConfig,
      );

      // 初始化图层
      initLayers();

      // 设置监听器
      setupWatchers();
    });

    // 组件卸载
    onUnmounted(() => {
      // 销毁资源
      map?.destroy();
      markerLayer?.destroy();
      polygonLayer?.destroy();
      polylineLayer?.destroy();
    });

    // 暴露方法
    return {
      // 获取地图和图层实例
      getMap: () => map || null,
      getMarkerLayer: () => markerLayer || null,
      getPolygonLayer: () => polygonLayer || null,
      getPolylineLayer: () => polylineLayer || null,

      // 添加单个覆盖物
      addMarker: (cell) => {
        const result = markerLayer?.add(cell);
        updateLayerData("markers", markerLayer);
        return result;
      },
      addPolygon: (cell) => {
        const result = polygonLayer?.add(cell);
        updateLayerData("polygons", polygonLayer);
        return result;
      },
      addPolyline: (cell) => {
        const result = polylineLayer?.add(cell);
        updateLayerData("polylines", polylineLayer);
        return result;
      },

      // 批量添加覆盖物
      batchAddMarker: (cells) => {
        const result = markerLayer?.batchAdd(cells);
        updateLayerData("markers", markerLayer);
        return result;
      },
      batchAddPolygon: (cells) => {
        const result = polygonLayer?.batchAdd(cells);
        updateLayerData("polygons", polygonLayer);
        return result;
      },
      batchAddPolyline: (cells) => {
        const result = polylineLayer?.batchAdd(cells);
        updateLayerData("polylines", polylineLayer);
        return result;
      },
    };
  },
});
</script>

<template>
  <div id="map-container" class="map-container" />
</template>

<style lang="less" scoped>
.map-container {
  width: 100%;
  height: 100%;
}
</style>

<style lang="less">
.anchorBL {
  display: none;
}
</style>
