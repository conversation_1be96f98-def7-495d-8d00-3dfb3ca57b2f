import eventBus, { eventMap } from "../utils/event-bus";

export default class MapSettings {
  constructor(settings) {
    this.zoom = settings?.zoom || 6;
    this.dragEnabled = settings?.dragEnabled || true;
    this.center = settings?.center || { lng: 108.961413, lat: 34.352991 };
    this.markProvince = settings?.markProvince || "";
    this.provincePolygonConfig = settings?.provincePolygonConfig || undefined;
  }

  change(key, value) {
    this[key] = value;
    eventBus.emit(eventMap.mapSettingsChange, this);
  }
}
