import BaseCell from "./BaseCell";

/**
 * 增强的多边形类
 * 支持编辑模式、样式配置、交互等功能
 */
export default class Polygon extends BaseCell {
  constructor(layer, param) {
    super(layer, param);

    // 基础属性
    this.id = param.id || this._generateId();
    this.name = param.name || "未命名多边形";
    this.editable = param.editable || false;
    this.pointList = param.pointList || [];

    // 扩展数据
    this.extData = param.extData || {};
    this.data = param.data || {};

    // 创建多边形
    this._cell = new BMap.Polygon(
      this.pointList.map((p) => new BMap.Point(p.lng, p.lat)),
      param.options || this._getDefaultOptions(),
    );

    // 初始化编辑功能
    if (this.editable) {
      this._initEditMode();
    }

    this.initEvent();
    this._initEnhancedEvents();
  }

  /**
   * 生成唯一ID
   */
  _generateId() {
    return 'polygon_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取默认样式选项
   */
  _getDefaultOptions() {
    return {
      strokeColor: "#1890ff",
      fillColor: "#bae7ff",
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillOpacity: 0.3,
      strokeStyle: "solid"
    };
  }

  /**
   * 初始化编辑模式
   */
  _initEditMode() {
    // 编辑模式相关属性
    this.isEditing = false;
    this.editPoints = [];
  }

  /**
   * 初始化增强事件
   */
  _initEnhancedEvents() {
    // 右键菜单事件
    this._cell.addEventListener("rightclick", (e) => {
      this._onRightClick(e);
    });

    // 双击编辑事件
    this._cell.addEventListener("dblclick", (e) => {
      this._onDoubleClick(e);
    });
  }

  /**
   * 右键点击事件处理
   */
  _onRightClick(e) {
    if (this.onRightClick) {
      this.onRightClick({
        polygon: this,
        position: e.point,
        event: e
      });
    }
  }

  /**
   * 双击事件处理
   */
  _onDoubleClick(e) {
    if (this.editable && this.onDoubleClick) {
      this.onDoubleClick({
        polygon: this,
        position: e.point,
        event: e
      });
    }
  }

  /**
   * 显示多边形
   */
  show() {
    this._cell.show();
  }

  /**
   * 隐藏多边形
   */
  hide() {
    this._cell.hide();
  }

  /**
   * 设置样式
   */
  setStyle(options) {
    const styleMap = {
      strokeColor: 'setStrokeColor',
      fillColor: 'setFillColor',
      strokeWeight: 'setStrokeWeight',
      strokeOpacity: 'setStrokeOpacity',
      fillOpacity: 'setFillOpacity',
      strokeStyle: 'setStrokeStyle'
    };

    Object.keys(options).forEach(key => {
      const method = styleMap[key];
      if (method && this._cell[method]) {
        this._cell[method](options[key]);
      }
    });
  }

  /**
   * 获取路径点
   */
  getPath() {
    return this._cell.getPath();
  }

  /**
   * 设置路径点
   */
  setPath(pointList) {
    this.pointList = pointList;
    const bmapPoints = pointList.map(p => new BMap.Point(p.lng, p.lat));
    this._cell.setPath(bmapPoints);
  }

  /**
   * 添加点
   */
  addPoint(lng, lat, index) {
    const point = { lng, lat };

    if (typeof index === 'number') {
      this.pointList.splice(index, 0, point);
    } else {
      this.pointList.push(point);
    }

    this.setPath(this.pointList);
  }

  /**
   * 删除点
   */
  removePoint(index) {
    if (index >= 0 && index < this.pointList.length) {
      this.pointList.splice(index, 1);
      this.setPath(this.pointList);
    }
  }

  /**
   * 更新点位置
   */
  updatePoint(index, lng, lat) {
    if (index >= 0 && index < this.pointList.length) {
      this.pointList[index] = { lng, lat };
      this.setPath(this.pointList);
    }
  }

  /**
   * 启用编辑模式
   */
  enableEditing() {
    if (!this.editable) return;

    this.isEditing = true;
    // 这里可以添加编辑模式的视觉反馈
    this.setStyle({
      strokeColor: "#ff4d4f",
      strokeWeight: 3
    });
  }

  /**
   * 禁用编辑模式
   */
  disableEditing() {
    this.isEditing = false;
    // 恢复原始样式
    this.setStyle(this._getDefaultOptions());
  }

  /**
   * 计算面积（平方米）
   */
  getArea() {
    const path = this.getPath();
    if (path.length < 3) return 0;

    // 使用百度地图的面积计算工具
    if (typeof BMapLib !== 'undefined' && BMapLib.GeoUtils) {
      return BMapLib.GeoUtils.getPolygonArea(path);
    }

    // 简单的面积计算（近似值）
    return this._calculateSimpleArea(path);
  }

  /**
   * 简单面积计算（近似值）
   */
  _calculateSimpleArea(points) {
    if (points.length < 3) return 0;

    let area = 0;
    for (let i = 0; i < points.length; i++) {
      const j = (i + 1) % points.length;
      area += points[i].lng * points[j].lat;
      area -= points[j].lng * points[i].lat;
    }

    return Math.abs(area) / 2 * 111000 * 111000; // 粗略转换为平方米
  }

  /**
   * 计算周长（米）
   */
  getPerimeter() {
    const path = this.getPath();
    if (path.length < 2) return 0;

    let perimeter = 0;
    for (let i = 0; i < path.length; i++) {
      const j = (i + 1) % path.length;
      perimeter += this._getDistance(path[i], path[j]);
    }

    return perimeter;
  }

  /**
   * 计算两点间距离（米）
   */
  _getDistance(point1, point2) {
    if (typeof BMapLib !== 'undefined' && BMapLib.GeoUtils) {
      return BMapLib.GeoUtils.getDistance(point1, point2);
    }

    // 简单的距离计算
    const R = 6371000; // 地球半径（米）
    const lat1 = point1.lat * Math.PI / 180;
    const lat2 = point2.lat * Math.PI / 180;
    const deltaLat = (point2.lat - point1.lat) * Math.PI / 180;
    const deltaLng = (point2.lng - point1.lng) * Math.PI / 180;

    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * 检查点是否在多边形内
   */
  containsPoint(lng, lat) {
    const point = new BMap.Point(lng, lat);

    if (typeof BMapLib !== 'undefined' && BMapLib.GeoUtils) {
      return BMapLib.GeoUtils.isPointInPolygon(point, this._cell);
    }

    // 简单的点在多边形内判断
    return this._pointInPolygon(point, this.getPath());
  }

  /**
   * 简单的点在多边形内判断算法
   */
  _pointInPolygon(point, polygon) {
    let inside = false;
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      if (((polygon[i].lat > point.lat) !== (polygon[j].lat > point.lat)) &&
          (point.lng < (polygon[j].lng - polygon[i].lng) * (point.lat - polygon[i].lat) / (polygon[j].lat - polygon[i].lat) + polygon[i].lng)) {
        inside = !inside;
      }
    }
    return inside;
  }

  /**
   * 获取多边形实例
   */
  getPolygon() {
    return this._cell;
  }

  /**
   * 导出配置
   */
  exportConfig() {
    return {
      id: this.id,
      name: this.name,
      pointList: this.pointList,
      editable: this.editable,
      extData: this.extData,
      data: this.data,
      options: {
        strokeColor: this._cell.getStrokeColor(),
        fillColor: this._cell.getFillColor(),
        strokeWeight: this._cell.getStrokeWeight(),
        strokeOpacity: this._cell.getStrokeOpacity(),
        fillOpacity: this._cell.getFillOpacity(),
        strokeStyle: this._cell.getStrokeStyle()
      }
    };
  }

  /**
   * 销毁多边形
   */
  destroy() {
    // 清理事件监听器
    this._cell.removeEventListener("rightclick");
    this._cell.removeEventListener("dblclick");

    this._cell = null;
    this.pointList = null;
    this.extData = null;
    this.data = null;
  }
}
