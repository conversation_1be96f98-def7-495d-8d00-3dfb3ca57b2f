import { merge } from "lodash-es";

export default class LayerConfig {
  constructor({ name, displayName, icon, type, defaultStyle, coverCells }) {
    this.name = name || ""; // 图层名称
    this.displayName = displayName || ""; // 图层显示名称
    this.icon = icon || ""; // 图层图标
    this.type = type || ""; // 图层类型: polygon(面)、marker(点)、polyline(线)

    this.style = merge({}, defaultStyle, {
      strokeColor: "#096dd9", // 边线颜色
      fillColor: "#40a9ff7d", // 填充颜色，当参数为空时，圆形将没有填充效果
      strokeWeight: 1, // 边线的宽度，以像素为单位
      strokeOpacity: 0.8, // 边线透明度，取值范围0 - 1
      fillOpacity: 0.6, // 填充的透明度，取值范围0 - 1
    });

    // 覆盖物配置
    this.coverCells = coverCells || [];
  }

  addCoverCell(cell) {
    this.coverCells.push(cell);
  }

  clear() {
    this.coverCells = [];
  }
}
