export default class BaseCell {
  constructor(layer, param) {
    this.layer = layer;
    this.param = param;
  }

  /**
   * 初始化事件
   */
  initEvent() {
    // 保存事件处理函数引用，用于后续移除
    this._eventHandlers = {};

    // 初始化参数中的事件处理函数
    if (this.param.onClick) {
      this.addEventListener("click", this.param.onClick);
    }
    if (this.param.onDblclick) {
      this.addEventListener("dblclick", this.param.onDblclick);
    }
    if (this.param.onMousedown) {
      this.addEventListener("mousedown", this.param.onMousedown);
    }
    if (this.param.onMouseup) {
      this.addEventListener("mouseup", this.param.onMouseup);
    }
    if (this.param.onMouseout) {
      this.addEventListener("mouseout", this.param.onMouseout);
    }
    if (this.param.onMouseover) {
      this.addEventListener("mouseover", this.param.onMouseover);
    }
    if (this.param.onRemove) {
      this.addEventListener("remove", this.param.onRemove);
    }
    if (this.param.onInfowindowclose) {
      this.addEventListener("infowindowclose", this.param.onInfowindowclose);
    }
    if (this.param.onInfowindowopen) {
      this.addEventListener("infowindowopen", this.param.onInfowindowopen);
    }
    if (this.param.onDragstart) {
      this.addEventListener("dragstart", this.param.onDragstart);
    }
    if (this.param.onDragging) {
      this.addEventListener("dragging", this.param.onDragging);
    }
    if (this.param.onDragend) {
      this.addEventListener("dragend", this.param.onDragend);
    }
    if (this.param.onRightclick) {
      this.addEventListener("rightclick", this.param.onRightclick);
    }
  }

  /**
   * 添加事件监听函数
   * @param {String} event - 事件名称
   * @param {Function} handler - 事件处理函数
   */
  addEventListener(event, handler) {
    if (!this._eventHandlers[event]) {
      this._eventHandlers[event] = [];
    }

    const fn = (e) => handler(e, this);

    // 保存处理函数引用
    this._eventHandlers[event].push(fn);

    // 添加到实际的百度地图标记点上
    this._cell.addEventListener(event, fn);
  }

  /**
   * 移除事件监听函数
   * @param {String} event - 事件名称
   * @param {Function} handler - 事件处理函数
   */
  removeEventListener(event, handler) {
    if (this._eventHandlers[event]) {
      // 如果提供了具体的处理函数，则只移除该函数
      if (handler) {
        const index = this._eventHandlers[event].indexOf(handler);
        if (index !== -1) {
          this._eventHandlers[event].splice(index, 1);
          this._cell.removeEventListener(event, handler);
        }
      }
      // 如果没有提供处理函数，则移除该事件的所有处理函数
      else {
        for (const h of this._eventHandlers[event]) {
          this._cell.removeEventListener(event, h);
        }
        this._eventHandlers[event] = [];
      }
    }
  }

  /**
   * 清除所有事件监听
   */
  clearEventListeners() {
    for (const event in this._eventHandlers) {
      for (const handler of this._eventHandlers[event]) {
        this._cell.removeEventListener(event, handler);
      }
    }
    this._eventHandlers = {};
  }

  /**
   * 销毁
   */
  destroy() {
    this.clearEventListeners();
    this._cell.remove();
  }
}
