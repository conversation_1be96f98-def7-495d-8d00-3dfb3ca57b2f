import DrawingTool from "./DrawingTool.js";
import MapSettings from "./MapSettings.js";
import ProvincePolygon from "./ProvincePolygon.js";
import InfoWindow from "./InfoWindow.js";
import LayerSystem from "./layer/index.js";
import DataSourceSystem from "./datasource/index.js";
import { performanceMonitor, componentCache } from "../utils/PerformanceOptimizer.js";
import { globalResourceManager } from "../utils/ResourceManager.js";
import { errorHandler } from "../utils/ErrorHandler.js";
import eventBus, { eventMap } from "../utils/event-bus";

export default class Map {
  constructor(dom, drawCallback, settings) {
    this._map = new BMap.Map(dom);
    this.settings = new MapSettings(settings);
    this.drawCallback = drawCallback;

    // 临时状态变量
    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;

    // 性能优化配置
    this.performanceConfig = {
      enableMonitoring: settings?.enablePerformanceMonitoring !== false,
      enableCaching: settings?.enableCaching !== false,
      enableErrorHandling: settings?.enableErrorHandling !== false,
      cacheSize: settings?.cacheSize || 50,
      ...settings?.performance
    };

    this._initPerformanceOptimization();
    this._initMap();
    this.initDrawingTool();
    this.initProvincePolygon();
    this.initInfoWindow();
    this.initLayerSystem();
    this.initDataSourceSystem();
    this.initEvent();

    // 通知地图初始化完成
    eventBus.emit(eventMap.mapInit, this);
  }

  _initMap() {
    // 初始化地图，设置中心点坐标和地图级别
    this._map.centerAndZoom(
      new BMap.Point(this.settings.center.lng, this.settings.center.lat),
      this.settings.zoom,
    );
    this._map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
  }

  // 初始化事件
  initEvent() {
    // 监听设置变更
    eventBus.on(eventMap.mapSettingsChange, (settings) => {
      this.updateMapSettings(settings);
    });

    // 监听地图点击事件，用于取消省份多边形高亮
    this._map.addEventListener("click", (ev) => {
      this.provincePolygon?.cancelHightLight(ev);
    });
  }

  // 更新地图设置
  updateMapSettings(settings) {
    if (settings.zoom !== this._map.getZoom()) {
      this._map.setZoom(settings.zoom);
    }

    if (settings.dragEnabled !== this._map.draggingEnabled) {
      settings.dragEnabled ? this._map.enableDragging() : this._map.disableDragging();
    }

    // 更新中心点
    if (
      settings.center &&
      (settings.center.lng !== this._map.getCenter().lng ||
        settings.center.lat !== this._map.getCenter().lat)
    ) {
      const point = new BMap.Point(settings.center.lng, settings.center.lat);
      this._map.setCenter(point);
      this._map.panTo(point);
    }
  }

  // 初始化绘图工具
  initDrawingTool() {
    this.drawingTool = new DrawingTool(this._map, {
      drawCallback: this.drawCallback,
    });
  }

  // 初始化省份多边形
  initProvincePolygon() {
    this.provincePolygon = new ProvincePolygon(this._map, this.settings?.provincePolygonConfig);
  }

  // 初始化信息窗口
  initInfoWindow() {
    this._infoWindow = new InfoWindow();
    this.infoWindow = this._infoWindow.getInfoWindow();
  }

  // 初始化图层系统
  initLayerSystem() {
    this.layerSystem = new LayerSystem(this._map);
  }

  // 初始化数据源系统
  initDataSourceSystem() {
    this.dataSourceSystem = new DataSourceSystem();
  }

  // 初始化性能优化
  _initPerformanceOptimization() {
    try {
      // 启动性能监控
      if (this.performanceConfig.enableMonitoring) {
        performanceMonitor.start();
        performanceMonitor.recordMetric('map.init.start', performance.now());
      }

      // 初始化组件缓存
      if (this.performanceConfig.enableCaching) {
        this.componentCache = new componentCache.constructor(this.performanceConfig.cacheSize);
      }

      // 注册资源管理
      globalResourceManager.register('map', this, (map) => {
        if (map.destroy) {
          map.destroy();
        }
      });

      // 错误处理配置
      if (this.performanceConfig.enableErrorHandling) {
        errorHandler.addLogger((errorInfo) => {
          if (errorInfo.level === 'error' || errorInfo.level === 'fatal') {
            console.error('Map Error:', errorInfo);
          }
        });
      }
    } catch (error) {
      console.error('Performance optimization initialization failed:', error);
    }
  }

  // 初始化设置
  initSettings(settings) {
    this.settings = new MapSettings(settings);
    this.setZoom(this.settings.zoom);
    this.targetDragging(this.settings.dragEnabled);
    this.setCenter(this.settings.center);
  }

  // 管理拖拽
  targetDragging(flag) {
    if (flag === undefined) {
      this.settings.change("dragEnabled", !this.settings.dragEnabled);
    } else {
      this.settings.change("dragEnabled", flag);
    }
  }

  // 确认修改地图中心点
  confirmChangeCenter(isCancel = false) {
    if (!isCancel && this.tempClickPoint) {
      this.setCenter(this.tempClickPoint);
    }

    this._cleanupCenterSelection();
  }

  // 清理中心点选择状态
  _cleanupCenterSelection() {
    if (this.openCenterModalCb) {
      this._map.removeEventListener("click", this.openCenterModalCb);
    }

    if (this.tempCenterPoint) {
      this._map.removeOverlay(this.tempCenterPoint);
    }

    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;
  }

  // 标记地图中心点
  markCenterPoint(center) {
    if (this.tempCenterPoint) {
      this.tempCenterPoint.setPosition(center);
      return;
    }

    this.tempCenterPoint = new BMap.Marker(center, {
      id: "default-center-point",
      name: "地图中心点",
    });
    this._map.addOverlay(this.tempCenterPoint);
  }

  // 点击选择地图中心
  _clickSelectCenter({ point }) {
    this.tempClickPoint = point;
    this.markCenterPoint(point);
  }

  // 打开中心点选择模式
  openCenterModal() {
    if (this.isOpenCenterModal) return;

    this._cleanupCenterSelection();
    this.isOpenCenterModal = true;
    this.openCenterModalCb = this._clickSelectCenter.bind(this);
    this._map.addEventListener("click", this.openCenterModalCb);
    this.markCenterPoint(this.settings.center);
  }

  // 设置地图中心
  setCenter(point) {
    if (!(point instanceof BMap.Point)) {
      point = new BMap.Point(point.lng, point.lat);
    }
    this._map.setCenter(point);
    this._map.panTo(point);
    this.settings.change("center", point);
  }

  getCenter() {
    return this._map.getCenter();
  }

  // 设置缩放级别
  setZoom(zoom) {
    this._map.setZoom(zoom);
    this.settings.change("zoom", zoom);
  }

  getZoom() {
    return this._map.getZoom();
  }

  /**
   * 打开信息窗口
   * @param {BMap.Point} position 位置
   * @param {Object} options 选项
   */
  openInfoWindow(position, options = {}) {
    if (!position) throw new Error("没有传入位置实例");

    if (options.title) {
      this._infoWindow.updateTitle(options.title);
    }

    if (options.content) {
      this._infoWindow.updateContent(options.content);
    }

    // 添加事件监听器
    if (options.onConfirm) {
      this._infoWindow.addEventListener("confirm", options.onConfirm);
    }

    if (options.onCancel) {
      this._infoWindow.addEventListener("cancel", options.onCancel);
    }

    this._map.openInfoWindow(this.infoWindow, position);
  }

  /**
   * 关闭信息窗口
   */
  closeInfoWindow() {
    this._map.closeInfoWindow();
  }

  // ==================== 图层系统方法 ====================

  /**
   * 创建图层
   * @param {Object} config 图层配置
   * @returns {String} 图层ID
   */
  createLayer(config = {}) {
    return this.layerSystem.createLayer(config);
  }

  /**
   * 删除图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否删除成功
   */
  deleteLayer(layerId) {
    return this.layerSystem.deleteLayer(layerId);
  }

  /**
   * 获取图层
   * @param {String} layerId 图层ID
   * @returns {Object|null} 图层对象
   */
  getLayer(layerId) {
    return this.layerSystem.getLayer(layerId);
  }

  /**
   * 获取所有图层
   * @returns {Array} 图层数组
   */
  getAllLayers() {
    return this.layerSystem.getAllLayers();
  }

  /**
   * 显示图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  showLayer(layerId) {
    return this.layerSystem.showLayer(layerId);
  }

  /**
   * 隐藏图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  hideLayer(layerId) {
    return this.layerSystem.hideLayer(layerId);
  }

  /**
   * 切换图层显示状态
   * @param {String} layerId 图层ID
   * @returns {Boolean} 切换后的显示状态
   */
  toggleLayer(layerId) {
    return this.layerSystem.toggleLayer(layerId);
  }

  /**
   * 选择图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  selectLayer(layerId) {
    return this.layerSystem.selectLayer(layerId);
  }

  /**
   * 获取当前选中的图层
   * @returns {Object|null} 选中的图层
   */
  getSelectedLayer() {
    return this.layerSystem.getSelectedLayer();
  }

  /**
   * 添加覆盖物到图层
   * @param {String} layerId 图层ID
   * @param {String} type 覆盖物类型
   * @param {Object} data 覆盖物数据
   * @returns {String|null} 覆盖物ID
   */
  addOverlayToLayer(layerId, type, data) {
    return this.layerSystem.addOverlay(layerId, type, data);
  }

  /**
   * 批量添加覆盖物到图层
   * @param {String} layerId 图层ID
   * @param {Array} overlays 覆盖物数组
   * @returns {Array} 添加成功的覆盖物ID数组
   */
  batchAddOverlaysToLayer(layerId, overlays) {
    return this.layerSystem.batchAddOverlays(layerId, overlays);
  }

  /**
   * 从图层删除覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @returns {Boolean} 是否删除成功
   */
  removeOverlayFromLayer(layerId, overlayId) {
    return this.layerSystem.removeOverlay(layerId, overlayId);
  }

  /**
   * 获取图层中的覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @returns {Object|null} 覆盖物对象
   */
  getLayerOverlay(layerId, overlayId) {
    return this.layerSystem.getOverlay(layerId, overlayId);
  }

  /**
   * 获取图层中的所有覆盖物
   * @param {String} layerId 图层ID
   * @returns {Array} 覆盖物数组
   */
  getLayerOverlays(layerId) {
    return this.layerSystem.getLayerOverlays(layerId);
  }

  /**
   * 更新图层中的覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @param {Object} data 新数据
   * @returns {Boolean} 是否更新成功
   */
  updateLayerOverlay(layerId, overlayId, data) {
    return this.layerSystem.updateOverlay(layerId, overlayId, data);
  }

  /**
   * 清除图层中的所有覆盖物
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  clearLayerOverlays(layerId) {
    return this.layerSystem.clearLayerOverlays(layerId);
  }

  /**
   * 设置图层顺序
   * @param {String} layerId 图层ID
   * @param {Number} zIndex 层级
   * @returns {Boolean} 是否成功
   */
  setLayerOrder(layerId, zIndex) {
    return this.layerSystem.setLayerOrder(layerId, zIndex);
  }

  /**
   * 聚焦到图层
   * @param {String} layerId 图层ID
   * @param {Object} options 选项
   * @returns {Boolean} 是否成功
   */
  focusLayer(layerId, options = {}) {
    return this.layerSystem.focusLayer(layerId, options);
  }

  /**
   * 获取图层统计信息
   * @param {String} layerId 图层ID
   * @returns {Object|null} 统计信息
   */
  getLayerStatistics(layerId) {
    return this.layerSystem.getLayerStatistics(layerId);
  }

  /**
   * 获取所有图层的统计信息
   * @returns {Object} 总体统计信息
   */
  getAllLayersStatistics() {
    return this.layerSystem.getAllStatistics();
  }

  /**
   * 获取渲染统计信息
   * @returns {Object} 渲染统计信息
   */
  getRenderStatistics() {
    return this.layerSystem.getRenderStatistics();
  }

  // ==================== 数据源系统方法 ====================

  /**
   * 创建静态数据源
   * @param {Object} config 配置对象
   * @returns {StaticDataSource} 静态数据源实例
   */
  createStaticDataSource(config = {}) {
    return this.dataSourceSystem.createStaticDataSource(config);
  }

  /**
   * 创建动态数据源
   * @param {Object} config 配置对象
   * @returns {DynamicDataSource} 动态数据源实例
   */
  createDynamicDataSource(config = {}) {
    return this.dataSourceSystem.createDynamicDataSource(config);
  }

  /**
   * 创建数据源
   * @param {String} type 数据源类型
   * @param {Object} config 配置对象
   * @returns {BaseDataSource} 数据源实例
   */
  createDataSource(type, config = {}) {
    return this.dataSourceSystem.createDataSource(type, config);
  }

  /**
   * 获取数据源
   * @param {String} id 数据源ID
   * @returns {BaseDataSource|null} 数据源实例
   */
  getDataSource(id) {
    return this.dataSourceSystem.getDataSource(id);
  }

  /**
   * 获取所有数据源
   * @returns {Array} 数据源数组
   */
  getAllDataSources() {
    return this.dataSourceSystem.getAllDataSources();
  }

  /**
   * 删除数据源
   * @param {String} id 数据源ID
   * @returns {Boolean} 是否删除成功
   */
  deleteDataSource(id) {
    return this.dataSourceSystem.deleteDataSource(id);
  }

  /**
   * 加载数据源数据
   * @param {String} id 数据源ID
   * @param {Boolean} useCache 是否使用缓存
   * @returns {Promise<Array>} 数据数组
   */
  async loadDataSourceData(id, useCache = true) {
    return this.dataSourceSystem.loadData(id, useCache);
  }

  /**
   * 刷新数据源
   * @param {String} id 数据源ID
   * @returns {Promise<Array>} 数据数组
   */
  async refreshDataSource(id) {
    return this.dataSourceSystem.refreshDataSource(id);
  }

  /**
   * 批量加载数据源数据
   * @param {Array} ids 数据源ID数组
   * @param {Boolean} useCache 是否使用缓存
   * @returns {Promise<Object>} 数据对象
   */
  async batchLoadDataSourceData(ids, useCache = true) {
    return this.dataSourceSystem.batchLoadData(ids, useCache);
  }

  /**
   * 合并多个数据源的数据
   * @param {Array} ids 数据源ID数组
   * @param {Function} mergeFunction 合并函数
   * @returns {Promise<Array>} 合并后的数据
   */
  async mergeDataSources(ids, mergeFunction = null) {
    return this.dataSourceSystem.mergeDataSources(ids, mergeFunction);
  }

  /**
   * 获取数据源统计信息
   * @returns {Object} 统计信息
   */
  getDataSourceStatistics() {
    return this.dataSourceSystem.getStatistics();
  }

  // ==================== 性能优化方法 ====================

  /**
   * 获取性能监控报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    if (!this.performanceConfig.enableMonitoring) {
      return { error: 'Performance monitoring is disabled' };
    }

    return performanceMonitor.getPerformanceReport();
  }

  /**
   * 记录性能指标
   * @param {String} name 指标名称
   * @param {Number} value 指标值
   * @param {Object} metadata 元数据
   */
  recordPerformanceMetric(name, value, metadata = {}) {
    if (this.performanceConfig.enableMonitoring) {
      performanceMonitor.recordMetric(`map.${name}`, value, metadata);
    }
  }

  /**
   * 获取组件缓存统计
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    if (this.componentCache) {
      return this.componentCache.getStats();
    }
    return { error: 'Component cache is disabled' };
  }

  /**
   * 清除组件缓存
   * @param {String} key 缓存键，不传则清除所有
   */
  clearCache(key) {
    if (this.componentCache) {
      if (key) {
        this.componentCache.delete(key);
      } else {
        this.componentCache.clear();
      }
    }
  }

  /**
   * 获取资源管理统计
   * @returns {Object} 资源统计
   */
  getResourceStats() {
    return globalResourceManager.getStats();
  }

  /**
   * 检查资源泄漏
   * @returns {Object} 泄漏检查结果
   */
  checkResourceLeaks() {
    return globalResourceManager.checkLeaks();
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计
   */
  getErrorStats() {
    if (this.performanceConfig.enableErrorHandling) {
      return errorHandler.getErrorStats();
    }
    return { error: 'Error handling is disabled' };
  }

  /**
   * 清除错误记录
   * @param {String} level 错误级别
   */
  clearErrors(level) {
    if (this.performanceConfig.enableErrorHandling) {
      errorHandler.clearErrors(level);
    }
  }

  /**
   * 优化地图性能
   * @param {Object} options 优化选项
   */
  optimizePerformance(options = {}) {
    const {
      clearCache = false,
      clearErrors = false,
      compactMemory = false
    } = options;

    try {
      // 清除缓存
      if (clearCache) {
        this.clearCache();
      }

      // 清除错误记录
      if (clearErrors) {
        this.clearErrors();
      }

      // 内存压缩
      if (compactMemory && typeof window !== 'undefined' && window.gc) {
        window.gc();
      }

      // 记录优化操作
      this.recordPerformanceMetric('optimization.performed', 1, options);

      return {
        success: true,
        timestamp: Date.now(),
        options
      };
    } catch (error) {
      errorHandler.error('Performance optimization failed', { error, options });
      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * 获取完整的性能诊断报告
   * @returns {Object} 诊断报告
   */
  getPerformanceDiagnostics() {
    return {
      timestamp: Date.now(),
      performance: this.getPerformanceReport(),
      cache: this.getCacheStats(),
      resources: this.getResourceStats(),
      resourceLeaks: this.checkResourceLeaks(),
      errors: this.getErrorStats(),
      layers: this.getAllLayersStatistics(),
      dataSources: this.getDataSourceStatistics(),
      rendering: this.getRenderStatistics()
    };
  }

  // 导出配置
  exportConfig() {
    return this.settings;
  }

  // 销毁地图
  destroy() {
    try {
      this._cleanupCenterSelection();

      // 记录销毁开始
      this.recordPerformanceMetric('destroy.start', performance.now());

      // 清理数据源系统
      if (this.dataSourceSystem) {
        this.dataSourceSystem.destroy();
        this.dataSourceSystem = null;
      }

      // 清理图层系统
      if (this.layerSystem) {
        this.layerSystem.destroy();
        this.layerSystem = null;
      }

      // 清理信息窗口
      if (this._infoWindow) {
        this._infoWindow.destroy();
        this._infoWindow = null;
        this.infoWindow = null;
      }

      // 清理省份多边形
      if (this.provincePolygon) {
        this.provincePolygon.clearMarkProvince();
        this.provincePolygon = null;
      }

      // 清理绘图工具
      if (this.drawingTool) {
        if (this.drawingTool.destroy) {
          this.drawingTool.destroy();
        }
        this.drawingTool = null;
      }

      // 清理性能优化系统
      this._cleanupPerformanceOptimization();

      // 记录销毁完成
      this.recordPerformanceMetric('destroy.end', performance.now());

      eventBus.emit(eventMap.mapDestroy, this);
      this._map = null;
    } catch (error) {
      errorHandler.error('Map destruction failed', { error });
      throw error;
    }
  }

  /**
   * 清理性能优化系统
   * @private
   */
  _cleanupPerformanceOptimization() {
    try {
      // 清理组件缓存
      if (this.componentCache) {
        this.componentCache.clear();
        this.componentCache = null;
      }

      // 停止性能监控
      if (this.performanceConfig.enableMonitoring) {
        // 注意：不要停止全局性能监控器，因为可能有其他地图实例在使用
        // performanceMonitor.stop();
      }

      // 从资源管理器中注销
      globalResourceManager.unregister('map');

      // 清理配置
      this.performanceConfig = null;
    } catch (error) {
      console.error('Performance optimization cleanup failed:', error);
    }
  }
}
