import eventBus, { eventMap } from "../../utils/event-bus";
import {
  LAYER_TYPES,
  EVENT_TYPES,
  ERROR_TYPES,
  validateLayerConfig,
  createDefaultLayerConfig,
  generateId,
  formatError
} from "./types.js";

/**
 * 图层管理器
 * 负责管理所有图层的创建、删除、显示、隐藏等操作
 */
export default class LayerManager {
  constructor(map) {
    this.map = map; // 地图实例
    this.layers = new Map(); // 图层集合
    this.layerOrder = []; // 图层顺序
    this.selectedLayer = null; // 当前选中的图层
    this.maxLayers = 50; // 最大图层数量
    
    this._initEvents();
  }

  /**
   * 初始化事件监听
   */
  _initEvents() {
    // 监听图层相关事件
    eventBus.on(EVENT_TYPES.LAYER_CREATED, this._onLayerCreated.bind(this));
    eventBus.on(EVENT_TYPES.LAYER_DELETED, this._onLayerDeleted.bind(this));
    eventBus.on(EVENT_TYPES.LAYER_SHOW, this._onLayerShow.bind(this));
    eventBus.on(EVENT_TYPES.LAYER_HIDE, this._onLayerHide.bind(this));
  }

  /**
   * 创建图层
   * @param {Object} config 图层配置
   * @returns {String} 图层ID
   */
  createLayer(config = {}) {
    try {
      // 检查图层数量限制
      if (this.layers.size >= this.maxLayers) {
        throw new Error(`图层数量已达到最大限制: ${this.maxLayers}`);
      }

      // 创建默认配置
      const layerConfig = createDefaultLayerConfig(config);
      
      // 验证配置
      if (!validateLayerConfig(layerConfig)) {
        throw new Error('无效的图层配置');
      }

      // 检查ID是否已存在
      if (this.layers.has(layerConfig.id)) {
        throw new Error(`图层ID已存在: ${layerConfig.id}`);
      }

      // 创建图层对象
      const layer = {
        id: layerConfig.id,
        name: layerConfig.name,
        displayName: layerConfig.displayName,
        type: layerConfig.type,
        visible: layerConfig.visible,
        zIndex: layerConfig.zIndex,
        opacity: layerConfig.opacity,
        editable: layerConfig.editable,
        selectable: layerConfig.selectable,
        config: layerConfig,
        overlays: new Map(), // 覆盖物集合
        overlayOrder: [], // 覆盖物顺序
        statistics: {
          totalOverlays: 0,
          visibleOverlays: 0,
          selectedOverlays: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 添加到图层集合
      this.layers.set(layer.id, layer);
      this.layerOrder.push(layer.id);

      // 设置图层顺序
      this._updateLayerOrder();

      // 触发事件
      eventBus.emit(EVENT_TYPES.LAYER_CREATED, layer);

      return layer.id;
    } catch (error) {
      const formattedError = formatError(
        ERROR_TYPES.INVALID_CONFIG,
        error.message,
        { config }
      );
      eventBus.emit(EVENT_TYPES.DATA_ERROR, formattedError);
      throw error;
    }
  }

  /**
   * 删除图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否删除成功
   */
  deleteLayer(layerId) {
    try {
      const layer = this.layers.get(layerId);
      if (!layer) {
        throw new Error(`图层不存在: ${layerId}`);
      }

      // 清除所有覆盖物
      this.clearLayerOverlays(layerId);

      // 从图层集合中移除
      this.layers.delete(layerId);
      
      // 从顺序数组中移除
      const index = this.layerOrder.indexOf(layerId);
      if (index > -1) {
        this.layerOrder.splice(index, 1);
      }

      // 如果是当前选中的图层，清除选中状态
      if (this.selectedLayer === layerId) {
        this.selectedLayer = null;
      }

      // 触发事件
      eventBus.emit(EVENT_TYPES.LAYER_DELETED, layer);

      return true;
    } catch (error) {
      const formattedError = formatError(
        ERROR_TYPES.LAYER_NOT_FOUND,
        error.message,
        { layerId }
      );
      eventBus.emit(EVENT_TYPES.DATA_ERROR, formattedError);
      return false;
    }
  }

  /**
   * 获取图层
   * @param {String} layerId 图层ID
   * @returns {Object|null} 图层对象
   */
  getLayer(layerId) {
    return this.layers.get(layerId) || null;
  }

  /**
   * 获取所有图层
   * @returns {Array} 图层数组
   */
  getAllLayers() {
    return this.layerOrder.map(id => this.layers.get(id)).filter(Boolean);
  }

  /**
   * 获取可见图层
   * @returns {Array} 可见图层数组
   */
  getVisibleLayers() {
    return this.getAllLayers().filter(layer => layer.visible);
  }

  /**
   * 显示图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  showLayer(layerId) {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    layer.visible = true;
    layer.updatedAt = new Date();

    // 显示图层中的所有覆盖物
    layer.overlays.forEach(overlay => {
      if (overlay.show) {
        overlay.show();
      }
    });

    // 触发事件
    eventBus.emit(EVENT_TYPES.LAYER_SHOW, layer);

    return true;
  }

  /**
   * 隐藏图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  hideLayer(layerId) {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    layer.visible = false;
    layer.updatedAt = new Date();

    // 隐藏图层中的所有覆盖物
    layer.overlays.forEach(overlay => {
      if (overlay.hide) {
        overlay.hide();
      }
    });

    // 触发事件
    eventBus.emit(EVENT_TYPES.LAYER_HIDE, layer);

    return true;
  }

  /**
   * 切换图层显示状态
   * @param {String} layerId 图层ID
   * @returns {Boolean} 切换后的显示状态
   */
  toggleLayer(layerId) {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    if (layer.visible) {
      this.hideLayer(layerId);
    } else {
      this.showLayer(layerId);
    }

    return layer.visible;
  }

  /**
   * 选择图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  selectLayer(layerId) {
    const layer = this.layers.get(layerId);
    if (!layer || !layer.selectable) return false;

    this.selectedLayer = layerId;
    
    // 触发事件
    eventBus.emit(EVENT_TYPES.LAYER_FOCUS, layer);

    return true;
  }

  /**
   * 获取当前选中的图层
   * @returns {Object|null} 选中的图层
   */
  getSelectedLayer() {
    return this.selectedLayer ? this.layers.get(this.selectedLayer) : null;
  }

  /**
   * 设置图层顺序
   * @param {String} layerId 图层ID
   * @param {Number} zIndex 层级
   * @returns {Boolean} 是否成功
   */
  setLayerOrder(layerId, zIndex) {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    layer.zIndex = zIndex;
    layer.updatedAt = new Date();

    this._updateLayerOrder();

    return true;
  }

  /**
   * 更新图层顺序
   */
  _updateLayerOrder() {
    this.layerOrder.sort((a, b) => {
      const layerA = this.layers.get(a);
      const layerB = this.layers.get(b);
      return (layerA?.zIndex || 0) - (layerB?.zIndex || 0);
    });
  }

  /**
   * 清除图层中的所有覆盖物
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  clearLayerOverlays(layerId) {
    const layer = this.layers.get(layerId);
    if (!layer) return false;

    // 从地图中移除所有覆盖物
    layer.overlays.forEach(overlay => {
      if (overlay._cell && this.map.removeOverlay) {
        this.map.removeOverlay(overlay._cell);
      }
      if (overlay.destroy) {
        overlay.destroy();
      }
    });

    // 清空覆盖物集合
    layer.overlays.clear();
    layer.overlayOrder = [];
    
    // 重置统计信息
    layer.statistics = {
      totalOverlays: 0,
      visibleOverlays: 0,
      selectedOverlays: 0
    };

    layer.updatedAt = new Date();

    return true;
  }

  /**
   * 获取图层统计信息
   * @param {String} layerId 图层ID
   * @returns {Object|null} 统计信息
   */
  getLayerStatistics(layerId) {
    const layer = this.layers.get(layerId);
    return layer ? layer.statistics : null;
  }

  /**
   * 获取所有图层的统计信息
   * @returns {Object} 总体统计信息
   */
  getAllStatistics() {
    const stats = {
      totalLayers: this.layers.size,
      visibleLayers: 0,
      totalOverlays: 0,
      visibleOverlays: 0,
      layerTypes: {}
    };

    this.layers.forEach(layer => {
      if (layer.visible) {
        stats.visibleLayers++;
      }
      
      stats.totalOverlays += layer.statistics.totalOverlays;
      stats.visibleOverlays += layer.statistics.visibleOverlays;
      
      // 统计图层类型
      if (!stats.layerTypes[layer.type]) {
        stats.layerTypes[layer.type] = 0;
      }
      stats.layerTypes[layer.type]++;
    });

    return stats;
  }

  /**
   * 事件处理器
   */
  _onLayerCreated(layer) {
    // 图层创建后的处理逻辑
  }

  _onLayerDeleted(layer) {
    // 图层删除后的处理逻辑
  }

  _onLayerShow(layer) {
    // 图层显示后的处理逻辑
  }

  _onLayerHide(layer) {
    // 图层隐藏后的处理逻辑
  }

  /**
   * 销毁图层管理器
   */
  destroy() {
    // 删除所有图层
    this.layerOrder.forEach(layerId => {
      this.deleteLayer(layerId);
    });

    // 清理事件监听
    eventBus.off(EVENT_TYPES.LAYER_CREATED, this._onLayerCreated);
    eventBus.off(EVENT_TYPES.LAYER_DELETED, this._onLayerDeleted);
    eventBus.off(EVENT_TYPES.LAYER_SHOW, this._onLayerShow);
    eventBus.off(EVENT_TYPES.LAYER_HIDE, this._onLayerHide);

    // 清理引用
    this.layers.clear();
    this.layerOrder = [];
    this.selectedLayer = null;
    this.map = null;
  }
}
