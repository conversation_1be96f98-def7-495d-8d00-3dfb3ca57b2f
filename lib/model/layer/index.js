import LayerManager from "./LayerManager.js";
import LayerRenderer from "./LayerRenderer.js";
import {
  LAYER_TYPES,
  EVENT_TYPES,
  ERROR_TYPES,
  OVERLAY_STATUS,
  DATA_SOURCE_TYPES,
  FIELD_MAPPING,
  STYLE_KEYS,
  DEFAULT_LAYER_CONFIG,
  CLUSTERING_CONFIG,
  validateLayerConfig,
  createDefaultLayerConfig,
  isValidOverlayType,
  isValidDataSourceType,
  formatError,
  generateId,
  deepMerge,
  validateStyleConfig
} from "./types.js";

/**
 * 图层系统主类
 * 整合图层管理器和渲染器，提供统一的图层操作接口
 */
export default class LayerSystem {
  constructor(map) {
    this.map = map; // 百度地图实例
    this.manager = new LayerManager(map);
    this.renderer = new LayerRenderer(this.manager, map);
    
    // 性能监控
    this.performanceMonitor = {
      renderTime: 0,
      lastRenderTime: 0,
      renderCount: 0,
      averageRenderTime: 0
    };
  }

  /**
   * 创建图层
   * @param {Object} config 图层配置
   * @returns {String} 图层ID
   */
  createLayer(config = {}) {
    const startTime = performance.now();
    
    try {
      const layerId = this.manager.createLayer(config);
      
      // 更新性能监控
      this._updatePerformanceMonitor(startTime);
      
      return layerId;
    } catch (error) {
      console.error('创建图层失败:', error);
      throw error;
    }
  }

  /**
   * 删除图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否删除成功
   */
  deleteLayer(layerId) {
    return this.manager.deleteLayer(layerId);
  }

  /**
   * 获取图层
   * @param {String} layerId 图层ID
   * @returns {Object|null} 图层对象
   */
  getLayer(layerId) {
    return this.manager.getLayer(layerId);
  }

  /**
   * 获取所有图层
   * @returns {Array} 图层数组
   */
  getAllLayers() {
    return this.manager.getAllLayers();
  }

  /**
   * 显示图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  showLayer(layerId) {
    const success = this.manager.showLayer(layerId);
    if (success) {
      this.renderer.showLayerOverlays(layerId);
    }
    return success;
  }

  /**
   * 隐藏图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  hideLayer(layerId) {
    const success = this.manager.hideLayer(layerId);
    if (success) {
      this.renderer.hideLayerOverlays(layerId);
    }
    return success;
  }

  /**
   * 切换图层显示状态
   * @param {String} layerId 图层ID
   * @returns {Boolean} 切换后的显示状态
   */
  toggleLayer(layerId) {
    const layer = this.getLayer(layerId);
    if (!layer) return false;

    if (layer.visible) {
      return !this.hideLayer(layerId);
    } else {
      return this.showLayer(layerId);
    }
  }

  /**
   * 选择图层
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  selectLayer(layerId) {
    return this.manager.selectLayer(layerId);
  }

  /**
   * 获取当前选中的图层
   * @returns {Object|null} 选中的图层
   */
  getSelectedLayer() {
    return this.manager.getSelectedLayer();
  }

  /**
   * 添加覆盖物到图层
   * @param {String} layerId 图层ID
   * @param {String} type 覆盖物类型
   * @param {Object} data 覆盖物数据
   * @returns {String|null} 覆盖物ID
   */
  addOverlay(layerId, type, data) {
    return this.renderer.addOverlay(layerId, type, data);
  }

  /**
   * 批量添加覆盖物
   * @param {String} layerId 图层ID
   * @param {Array} overlays 覆盖物数组
   * @returns {Array} 添加成功的覆盖物ID数组
   */
  batchAddOverlays(layerId, overlays) {
    return this.renderer.batchAddOverlays(layerId, overlays);
  }

  /**
   * 删除覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @returns {Boolean} 是否删除成功
   */
  removeOverlay(layerId, overlayId) {
    return this.renderer.removeOverlay(layerId, overlayId);
  }

  /**
   * 获取覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @returns {Object|null} 覆盖物对象
   */
  getOverlay(layerId, overlayId) {
    return this.renderer.getOverlay(layerId, overlayId);
  }

  /**
   * 获取图层中的所有覆盖物
   * @param {String} layerId 图层ID
   * @returns {Array} 覆盖物数组
   */
  getLayerOverlays(layerId) {
    return this.renderer.getLayerOverlays(layerId);
  }

  /**
   * 更新覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @param {Object} data 新数据
   * @returns {Boolean} 是否更新成功
   */
  updateOverlay(layerId, overlayId, data) {
    return this.renderer.updateOverlay(layerId, overlayId, data);
  }

  /**
   * 清除图层中的所有覆盖物
   * @param {String} layerId 图层ID
   * @returns {Boolean} 是否成功
   */
  clearLayerOverlays(layerId) {
    return this.manager.clearLayerOverlays(layerId);
  }

  /**
   * 设置图层顺序
   * @param {String} layerId 图层ID
   * @param {Number} zIndex 层级
   * @returns {Boolean} 是否成功
   */
  setLayerOrder(layerId, zIndex) {
    return this.manager.setLayerOrder(layerId, zIndex);
  }

  /**
   * 获取图层统计信息
   * @param {String} layerId 图层ID
   * @returns {Object|null} 统计信息
   */
  getLayerStatistics(layerId) {
    return this.manager.getLayerStatistics(layerId);
  }

  /**
   * 获取所有图层的统计信息
   * @returns {Object} 总体统计信息
   */
  getAllStatistics() {
    return this.manager.getAllStatistics();
  }

  /**
   * 获取渲染统计信息
   * @returns {Object} 渲染统计信息
   */
  getRenderStatistics() {
    return {
      ...this.renderer.getRenderStatistics(),
      performance: this.performanceMonitor
    };
  }

  /**
   * 聚焦到图层
   * @param {String} layerId 图层ID
   * @param {Object} options 选项
   * @returns {Boolean} 是否成功
   */
  focusLayer(layerId, options = {}) {
    const layer = this.getLayer(layerId);
    if (!layer) return false;

    const overlays = this.getLayerOverlays(layerId);
    if (overlays.length === 0) return false;

    // 计算边界
    const bounds = this._calculateBounds(overlays);
    if (!bounds) return false;

    // 设置地图视野
    try {
      this.map.setViewport(bounds.points, {
        margins: options.margins || [50, 50, 50, 50],
        zoomFactor: options.zoomFactor || 0
      });
      return true;
    } catch (error) {
      console.error('聚焦图层失败:', error);
      return false;
    }
  }

  /**
   * 计算覆盖物边界
   * @param {Array} overlays 覆盖物数组
   * @returns {Object|null} 边界信息
   */
  _calculateBounds(overlays) {
    if (overlays.length === 0) return null;

    const points = [];

    overlays.forEach(overlay => {
      if (overlay.getPosition) {
        // 标记点
        points.push(overlay.getPosition());
      } else if (overlay.getPath) {
        // 多边形或折线
        points.push(...overlay.getPath());
      }
    });

    return points.length > 0 ? { points } : null;
  }

  /**
   * 更新性能监控
   * @param {Number} startTime 开始时间
   */
  _updatePerformanceMonitor(startTime) {
    const renderTime = performance.now() - startTime;
    this.performanceMonitor.lastRenderTime = renderTime;
    this.performanceMonitor.renderCount++;
    this.performanceMonitor.renderTime += renderTime;
    this.performanceMonitor.averageRenderTime = 
      this.performanceMonitor.renderTime / this.performanceMonitor.renderCount;
  }

  /**
   * 导出图层配置
   * @param {String} layerId 图层ID
   * @returns {Object|null} 图层配置
   */
  exportLayerConfig(layerId) {
    const layer = this.getLayer(layerId);
    if (!layer) return null;

    const overlays = this.getLayerOverlays(layerId);
    
    return {
      ...layer.config,
      overlays: overlays.map(overlay => 
        overlay.exportConfig ? overlay.exportConfig() : overlay
      ),
      statistics: layer.statistics,
      createdAt: layer.createdAt,
      updatedAt: layer.updatedAt
    };
  }

  /**
   * 导出所有图层配置
   * @returns {Array} 图层配置数组
   */
  exportAllLayersConfig() {
    return this.getAllLayers().map(layer => this.exportLayerConfig(layer.id));
  }

  /**
   * 销毁图层系统
   */
  destroy() {
    // 销毁渲染器
    if (this.renderer) {
      this.renderer.destroy();
      this.renderer = null;
    }

    // 销毁管理器
    if (this.manager) {
      this.manager.destroy();
      this.manager = null;
    }

    // 清理引用
    this.map = null;
    this.performanceMonitor = null;
  }
}

// 导出类型和工具函数
export {
  LayerManager,
  LayerRenderer,
  LAYER_TYPES,
  EVENT_TYPES,
  ERROR_TYPES,
  OVERLAY_STATUS,
  DATA_SOURCE_TYPES,
  FIELD_MAPPING,
  STYLE_KEYS,
  DEFAULT_LAYER_CONFIG,
  CLUSTERING_CONFIG,
  validateLayerConfig,
  createDefaultLayerConfig,
  isValidOverlayType,
  isValidDataSourceType,
  formatError,
  generateId,
  deepMerge,
  validateStyleConfig
};
