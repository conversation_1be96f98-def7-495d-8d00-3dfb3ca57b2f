/**
 * 图层类型定义
 * 定义图层相关的常量和类型
 */

// 图层类型枚举
export const LAYER_TYPES = {
  MARKER: 'marker',
  POLYGON: 'polygon',
  POLYLINE: 'polyline',
  MIXED: 'mixed' // 混合图层
};

// 覆盖物状态枚举
export const OVERLAY_STATUS = {
  VISIBLE: 'visible',
  HIDDEN: 'hidden',
  EDITING: 'editing',
  SELECTED: 'selected',
  DISABLED: 'disabled'
};

// 数据源类型枚举
export const DATA_SOURCE_TYPES = {
  STATIC: 'static',
  DYNAMIC: 'dynamic',
  REALTIME: 'realtime'
};

// 事件类型枚举
export const EVENT_TYPES = {
  LAYER_CREATED: 'layer:created',
  LAYER_UPDATED: 'layer:updated',
  LAYER_DELETED: 'layer:deleted',
  LAYER_SHOW: 'layer:show',
  LAYER_HIDE: 'layer:hide',
  LAYER_FOCUS: 'layer:focus',
  OVERLAY_ADDED: 'overlay:added',
  OVERLAY_UPDATED: 'overlay:updated',
  OVERLAY_DELETED: 'overlay:deleted',
  OVERLAY_SELECTED: 'overlay:selected',
  STYLE_CHANGED: 'style:changed',
  DATA_LOADED: 'data:loaded',
  DATA_ERROR: 'data:error'
};

// 错误类型枚举
export const ERROR_TYPES = {
  INVALID_CONFIG: 'invalid_config',
  DATA_LOAD_FAILED: 'data_load_failed',
  RENDER_FAILED: 'render_failed',
  RESOURCE_NOT_FOUND: 'resource_not_found',
  PERMISSION_DENIED: 'permission_denied',
  LAYER_NOT_FOUND: 'layer_not_found',
  OVERLAY_NOT_FOUND: 'overlay_not_found'
};

// 默认配置
export const DEFAULT_LAYER_CONFIG = {
  id: null,
  name: '未命名图层',
  displayName: '未命名图层',
  type: LAYER_TYPES.MARKER,
  visible: true,
  zIndex: 0,
  opacity: 1,
  isStatic: true,
  editable: false,
  selectable: true,
  styleConfig: {},
  mappingConfig: [],
  dataSource: null,
  maxOverlays: 1000, // 最大覆盖物数量
  clustering: false, // 是否启用聚合
  animation: false // 是否启用动画
};

// 字段映射关系
export const FIELD_MAPPING = {
  ID: 'id',
  NAME: 'name',
  LONGITUDE: 'lng',
  LATITUDE: 'lat',
  POINT_LIST: 'pointList',
  CENTER_POINT: 'centerPoint',
  TIME: 'time',
  START_POINT: 'startPoint',
  END_POINT: 'endPoint',
  DESCRIPTION: 'description',
  CATEGORY: 'category',
  STATUS: 'status'
};

// 样式配置键
export const STYLE_KEYS = {
  // 通用样式
  OPACITY: 'opacity',
  Z_INDEX: 'zIndex',
  
  // 标记点样式
  ICON_TYPE: 'iconType',
  ICON_URL: 'iconUrl',
  ICON_SIZE: 'iconSize',
  ICON_ANCHOR: 'iconAnchor',
  
  // 线条样式
  STROKE_COLOR: 'strokeColor',
  STROKE_WEIGHT: 'strokeWeight',
  STROKE_OPACITY: 'strokeOpacity',
  STROKE_STYLE: 'strokeStyle',
  
  // 填充样式
  FILL_COLOR: 'fillColor',
  FILL_OPACITY: 'fillOpacity',
  
  // 标签样式
  LABEL_VISIBLE: 'labelVisible',
  LABEL_COLOR: 'labelColor',
  LABEL_SIZE: 'labelSize',
  LABEL_OFFSET: 'labelOffset'
};

// 聚合配置
export const CLUSTERING_CONFIG = {
  enabled: false,
  gridSize: 60,
  maxZoom: 15,
  minimumClusterSize: 2,
  styles: [
    {
      url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMTgiIGZpbGw9IiMxODkwZmYiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjIwIiB5PSIyNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5OPC90ZXh0Pgo8L3N2Zz4=',
      width: 40,
      height: 40,
      textColor: '#fff',
      textSize: 12
    }
  ]
};

/**
 * 验证图层配置
 * @param {Object} config 图层配置
 * @returns {Boolean} 是否有效
 */
export function validateLayerConfig(config) {
  if (!config || typeof config !== 'object') {
    return false;
  }

  // 检查必需字段
  if (!config.type || !Object.values(LAYER_TYPES).includes(config.type)) {
    return false;
  }

  // 检查名称
  if (!config.name || typeof config.name !== 'string') {
    return false;
  }

  return true;
}

/**
 * 创建默认图层配置
 * @param {Object} overrides 覆盖配置
 * @returns {Object} 图层配置
 */
export function createDefaultLayerConfig(overrides = {}) {
  return {
    ...DEFAULT_LAYER_CONFIG,
    ...overrides,
    id: overrides.id || `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  };
}

/**
 * 检查是否为有效的覆盖物类型
 * @param {String} type 类型
 * @returns {Boolean} 是否有效
 */
export function isValidOverlayType(type) {
  return Object.values(LAYER_TYPES).includes(type);
}

/**
 * 检查是否为有效的数据源类型
 * @param {String} type 类型
 * @returns {Boolean} 是否有效
 */
export function isValidDataSourceType(type) {
  return Object.values(DATA_SOURCE_TYPES).includes(type);
}

/**
 * 格式化错误信息
 * @param {String} type 错误类型
 * @param {String} message 错误消息
 * @param {Object} context 上下文信息
 * @returns {Object} 格式化的错误对象
 */
export function formatError(type, message, context = {}) {
  return {
    type,
    message,
    context,
    timestamp: new Date().toISOString(),
    stack: new Error().stack
  };
}

/**
 * 生成唯一ID
 * @param {String} prefix 前缀
 * @returns {String} 唯一ID
 */
export function generateId(prefix = 'item') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 深度合并对象
 * @param {Object} target 目标对象
 * @param {Object} source 源对象
 * @returns {Object} 合并后的对象
 */
export function deepMerge(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  
  return result;
}

/**
 * 验证样式配置
 * @param {Object} styleConfig 样式配置
 * @param {String} layerType 图层类型
 * @returns {Boolean} 是否有效
 */
export function validateStyleConfig(styleConfig, layerType) {
  if (!styleConfig || typeof styleConfig !== 'object') {
    return true; // 样式配置是可选的
  }
  
  // 根据图层类型验证特定的样式属性
  switch (layerType) {
    case LAYER_TYPES.MARKER:
      // 验证标记点样式
      break;
    case LAYER_TYPES.POLYGON:
      // 验证多边形样式
      break;
    case LAYER_TYPES.POLYLINE:
      // 验证折线样式
      break;
  }
  
  return true;
}
