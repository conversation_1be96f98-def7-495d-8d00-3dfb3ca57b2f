import Marker from "../Marker.js";
import Polygon from "../Polygon.js";
import Polyline from "../Polyline.js";
import eventBus from "../../utils/event-bus";
import {
  LAYER_TYPES,
  EVENT_TYPES,
  ERROR_TYPES,
  generateId,
  formatError
} from "./types.js";

/**
 * 图层渲染器
 * 负责图层中覆盖物的渲染、更新和管理
 */
export default class LayerRenderer {
  constructor(layerManager, map) {
    this.layerManager = layerManager;
    this.map = map; // 百度地图实例
    this.renderQueue = []; // 渲染队列
    this.isRendering = false; // 是否正在渲染
    this.batchSize = 100; // 批量渲染大小
    this.renderDelay = 16; // 渲染延迟（毫秒）
  }

  /**
   * 向图层添加覆盖物
   * @param {String} layerId 图层ID
   * @param {String} type 覆盖物类型
   * @param {Object} data 覆盖物数据
   * @returns {String|null} 覆盖物ID
   */
  addOverlay(layerId, type, data) {
    try {
      const layer = this.layerManager.getLayer(layerId);
      if (!layer) {
        throw new Error(`图层不存在: ${layerId}`);
      }

      // 检查覆盖物数量限制
      if (layer.overlays.size >= (layer.config.maxOverlays || 1000)) {
        throw new Error(`图层覆盖物数量已达到最大限制: ${layer.config.maxOverlays}`);
      }

      // 生成覆盖物ID
      const overlayId = data.id || generateId(type);

      // 创建覆盖物
      const overlay = this._createOverlay(type, data, layer);
      if (!overlay) {
        throw new Error(`无法创建覆盖物类型: ${type}`);
      }

      // 添加到图层
      layer.overlays.set(overlayId, overlay);
      layer.overlayOrder.push(overlayId);

      // 添加到地图
      if (overlay._cell && layer.visible) {
        this.map.addOverlay(overlay._cell);
      }

      // 更新统计信息
      this._updateLayerStatistics(layer);

      // 触发事件
      eventBus.emit(EVENT_TYPES.OVERLAY_ADDED, {
        layerId,
        overlayId,
        overlay,
        type
      });

      return overlayId;
    } catch (error) {
      const formattedError = formatError(
        ERROR_TYPES.RENDER_FAILED,
        error.message,
        { layerId, type, data }
      );
      eventBus.emit(EVENT_TYPES.DATA_ERROR, formattedError);
      return null;
    }
  }

  /**
   * 批量添加覆盖物
   * @param {String} layerId 图层ID
   * @param {Array} overlays 覆盖物数组
   * @returns {Array} 添加成功的覆盖物ID数组
   */
  batchAddOverlays(layerId, overlays) {
    const results = [];
    
    // 分批处理
    for (let i = 0; i < overlays.length; i += this.batchSize) {
      const batch = overlays.slice(i, i + this.batchSize);
      
      batch.forEach(overlayData => {
        const overlayId = this.addOverlay(layerId, overlayData.type, overlayData);
        if (overlayId) {
          results.push(overlayId);
        }
      });

      // 如果不是最后一批，添加延迟
      if (i + this.batchSize < overlays.length) {
        setTimeout(() => {}, this.renderDelay);
      }
    }

    return results;
  }

  /**
   * 删除覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @returns {Boolean} 是否删除成功
   */
  removeOverlay(layerId, overlayId) {
    try {
      const layer = this.layerManager.getLayer(layerId);
      if (!layer) {
        throw new Error(`图层不存在: ${layerId}`);
      }

      const overlay = layer.overlays.get(overlayId);
      if (!overlay) {
        throw new Error(`覆盖物不存在: ${overlayId}`);
      }

      // 从地图中移除
      if (overlay._cell) {
        this.map.removeOverlay(overlay._cell);
      }

      // 销毁覆盖物
      if (overlay.destroy) {
        overlay.destroy();
      }

      // 从图层中移除
      layer.overlays.delete(overlayId);
      
      const index = layer.overlayOrder.indexOf(overlayId);
      if (index > -1) {
        layer.overlayOrder.splice(index, 1);
      }

      // 更新统计信息
      this._updateLayerStatistics(layer);

      // 触发事件
      eventBus.emit(EVENT_TYPES.OVERLAY_DELETED, {
        layerId,
        overlayId,
        overlay
      });

      return true;
    } catch (error) {
      const formattedError = formatError(
        ERROR_TYPES.OVERLAY_NOT_FOUND,
        error.message,
        { layerId, overlayId }
      );
      eventBus.emit(EVENT_TYPES.DATA_ERROR, formattedError);
      return false;
    }
  }

  /**
   * 获取覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @returns {Object|null} 覆盖物对象
   */
  getOverlay(layerId, overlayId) {
    const layer = this.layerManager.getLayer(layerId);
    if (!layer) return null;

    return layer.overlays.get(overlayId) || null;
  }

  /**
   * 获取图层中的所有覆盖物
   * @param {String} layerId 图层ID
   * @returns {Array} 覆盖物数组
   */
  getLayerOverlays(layerId) {
    const layer = this.layerManager.getLayer(layerId);
    if (!layer) return [];

    return layer.overlayOrder.map(id => layer.overlays.get(id)).filter(Boolean);
  }

  /**
   * 更新覆盖物
   * @param {String} layerId 图层ID
   * @param {String} overlayId 覆盖物ID
   * @param {Object} data 新数据
   * @returns {Boolean} 是否更新成功
   */
  updateOverlay(layerId, overlayId, data) {
    try {
      const overlay = this.getOverlay(layerId, overlayId);
      if (!overlay) {
        throw new Error(`覆盖物不存在: ${overlayId}`);
      }

      // 更新覆盖物数据
      if (overlay.updateData) {
        overlay.updateData(data);
      }

      // 触发事件
      eventBus.emit(EVENT_TYPES.OVERLAY_UPDATED, {
        layerId,
        overlayId,
        overlay,
        data
      });

      return true;
    } catch (error) {
      const formattedError = formatError(
        ERROR_TYPES.RENDER_FAILED,
        error.message,
        { layerId, overlayId, data }
      );
      eventBus.emit(EVENT_TYPES.DATA_ERROR, formattedError);
      return false;
    }
  }

  /**
   * 显示图层覆盖物
   * @param {String} layerId 图层ID
   */
  showLayerOverlays(layerId) {
    const layer = this.layerManager.getLayer(layerId);
    if (!layer) return;

    layer.overlays.forEach(overlay => {
      if (overlay._cell) {
        this.map.addOverlay(overlay._cell);
      }
      if (overlay.show) {
        overlay.show();
      }
    });

    this._updateLayerStatistics(layer);
  }

  /**
   * 隐藏图层覆盖物
   * @param {String} layerId 图层ID
   */
  hideLayerOverlays(layerId) {
    const layer = this.layerManager.getLayer(layerId);
    if (!layer) return;

    layer.overlays.forEach(overlay => {
      if (overlay._cell) {
        this.map.removeOverlay(overlay._cell);
      }
      if (overlay.hide) {
        overlay.hide();
      }
    });

    this._updateLayerStatistics(layer);
  }

  /**
   * 创建覆盖物
   * @param {String} type 覆盖物类型
   * @param {Object} data 覆盖物数据
   * @param {Object} layer 图层对象
   * @returns {Object|null} 覆盖物实例
   */
  _createOverlay(type, data, layer) {
    try {
      switch (type) {
        case LAYER_TYPES.MARKER:
          return new Marker(layer, {
            ...data,
            lng: data.lng || data.longitude,
            lat: data.lat || data.latitude
          });
          
        case LAYER_TYPES.POLYGON:
          return new Polygon(layer, {
            ...data,
            pointList: data.pointList || data.points || []
          });
          
        case LAYER_TYPES.POLYLINE:
          return new Polyline(layer, {
            ...data,
            pointList: data.pointList || data.points || []
          });
          
        default:
          throw new Error(`不支持的覆盖物类型: ${type}`);
      }
    } catch (error) {
      console.error('创建覆盖物失败:', error);
      return null;
    }
  }

  /**
   * 更新图层统计信息
   * @param {Object} layer 图层对象
   */
  _updateLayerStatistics(layer) {
    let visibleCount = 0;
    let selectedCount = 0;

    layer.overlays.forEach(overlay => {
      if (overlay._cell && overlay._cell.isVisible && overlay._cell.isVisible()) {
        visibleCount++;
      }
      if (overlay.selected) {
        selectedCount++;
      }
    });

    layer.statistics = {
      totalOverlays: layer.overlays.size,
      visibleOverlays: visibleCount,
      selectedOverlays: selectedCount
    };

    layer.updatedAt = new Date();
  }

  /**
   * 清除所有覆盖物
   */
  clearAll() {
    this.layerManager.getAllLayers().forEach(layer => {
      this.layerManager.clearLayerOverlays(layer.id);
    });
  }

  /**
   * 获取渲染统计信息
   * @returns {Object} 渲染统计信息
   */
  getRenderStatistics() {
    const stats = {
      totalLayers: this.layerManager.layers.size,
      totalOverlays: 0,
      visibleOverlays: 0,
      renderQueueSize: this.renderQueue.length,
      isRendering: this.isRendering
    };

    this.layerManager.getAllLayers().forEach(layer => {
      stats.totalOverlays += layer.statistics.totalOverlays;
      stats.visibleOverlays += layer.statistics.visibleOverlays;
    });

    return stats;
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    // 清除所有覆盖物
    this.clearAll();

    // 清理渲染队列
    this.renderQueue = [];
    this.isRendering = false;

    // 清理引用
    this.layerManager = null;
    this.map = null;
  }
}
