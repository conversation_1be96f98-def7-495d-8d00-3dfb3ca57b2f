import StaticDataSource from "./StaticDataSource.js";
import DynamicDataSource from "./DynamicDataSource.js";
import { DATA_SOURCE_TYPES, isValidDataSourceType, generateId } from "../layer/types.js";

/**
 * 数据源工厂
 * 负责创建和管理不同类型的数据源
 */
export default class DataSourceFactory {
  constructor() {
    this.dataSources = new Map(); // 数据源实例集合
    this.dataSourceTypes = new Map(); // 自定义数据源类型
    
    // 注册内置数据源类型
    this._registerBuiltinTypes();
  }

  /**
   * 注册内置数据源类型
   */
  _registerBuiltinTypes() {
    this.dataSourceTypes.set(DATA_SOURCE_TYPES.STATIC, StaticDataSource);
    this.dataSourceTypes.set(DATA_SOURCE_TYPES.DYNAMIC, DynamicDataSource);
  }

  /**
   * 创建数据源
   * @param {String} type 数据源类型
   * @param {Object} config 配置对象
   * @returns {BaseDataSource} 数据源实例
   */
  createDataSource(type, config = {}) {
    if (!isValidDataSourceType(type)) {
      throw new Error(`不支持的数据源类型: ${type}`);
    }

    const DataSourceClass = this.dataSourceTypes.get(type);
    if (!DataSourceClass) {
      throw new Error(`未找到数据源类型: ${type}`);
    }

    // 生成唯一ID
    const id = config.id || generateId('datasource');
    
    // 检查ID是否已存在
    if (this.dataSources.has(id)) {
      throw new Error(`数据源ID已存在: ${id}`);
    }

    // 创建数据源实例
    const dataSource = new DataSourceClass({
      ...config,
      id,
      type
    });

    // 添加到集合
    this.dataSources.set(id, dataSource);

    return dataSource;
  }

  /**
   * 创建静态数据源
   * @param {Object} config 配置对象
   * @returns {StaticDataSource} 静态数据源实例
   */
  createStaticDataSource(config = {}) {
    return this.createDataSource(DATA_SOURCE_TYPES.STATIC, config);
  }

  /**
   * 创建动态数据源
   * @param {Object} config 配置对象
   * @returns {DynamicDataSource} 动态数据源实例
   */
  createDynamicDataSource(config = {}) {
    return this.createDataSource(DATA_SOURCE_TYPES.DYNAMIC, config);
  }

  /**
   * 获取数据源
   * @param {String} id 数据源ID
   * @returns {BaseDataSource|null} 数据源实例
   */
  getDataSource(id) {
    return this.dataSources.get(id) || null;
  }

  /**
   * 获取所有数据源
   * @returns {Array} 数据源数组
   */
  getAllDataSources() {
    return Array.from(this.dataSources.values());
  }

  /**
   * 获取指定类型的数据源
   * @param {String} type 数据源类型
   * @returns {Array} 数据源数组
   */
  getDataSourcesByType(type) {
    return this.getAllDataSources().filter(ds => ds.type === type);
  }

  /**
   * 删除数据源
   * @param {String} id 数据源ID
   * @returns {Boolean} 是否删除成功
   */
  deleteDataSource(id) {
    const dataSource = this.dataSources.get(id);
    if (!dataSource) {
      return false;
    }

    // 销毁数据源
    dataSource.destroy();
    
    // 从集合中移除
    this.dataSources.delete(id);

    return true;
  }

  /**
   * 注册自定义数据源类型
   * @param {String} type 类型名称
   * @param {Class} DataSourceClass 数据源类
   */
  registerDataSourceType(type, DataSourceClass) {
    if (this.dataSourceTypes.has(type)) {
      console.warn(`数据源类型 ${type} 已存在，将被覆盖`);
    }

    this.dataSourceTypes.set(type, DataSourceClass);
  }

  /**
   * 注销数据源类型
   * @param {String} type 类型名称
   * @returns {Boolean} 是否注销成功
   */
  unregisterDataSourceType(type) {
    // 不允许注销内置类型
    if (Object.values(DATA_SOURCE_TYPES).includes(type)) {
      throw new Error(`不能注销内置数据源类型: ${type}`);
    }

    return this.dataSourceTypes.delete(type);
  }

  /**
   * 获取支持的数据源类型
   * @returns {Array} 类型数组
   */
  getSupportedTypes() {
    return Array.from(this.dataSourceTypes.keys());
  }

  /**
   * 检查数据源类型是否支持
   * @param {String} type 类型名称
   * @returns {Boolean} 是否支持
   */
  isTypeSupported(type) {
    return this.dataSourceTypes.has(type);
  }

  /**
   * 批量创建数据源
   * @param {Array} configs 配置数组
   * @returns {Array} 创建结果数组
   */
  batchCreateDataSources(configs) {
    const results = [];

    configs.forEach((config, index) => {
      try {
        const dataSource = this.createDataSource(config.type, config);
        results.push({
          success: true,
          dataSource,
          index
        });
      } catch (error) {
        results.push({
          success: false,
          error: error.message,
          index
        });
      }
    });

    return results;
  }

  /**
   * 克隆数据源
   * @param {String} sourceId 源数据源ID
   * @param {Object} overrideConfig 覆盖配置
   * @returns {BaseDataSource} 新的数据源实例
   */
  cloneDataSource(sourceId, overrideConfig = {}) {
    const sourceDataSource = this.getDataSource(sourceId);
    if (!sourceDataSource) {
      throw new Error(`源数据源不存在: ${sourceId}`);
    }

    // 合并配置
    const newConfig = {
      ...sourceDataSource.config,
      ...overrideConfig,
      id: overrideConfig.id || generateId('datasource_clone')
    };

    return this.createDataSource(sourceDataSource.type, newConfig);
  }

  /**
   * 导出数据源配置
   * @param {String} id 数据源ID
   * @returns {Object|null} 配置对象
   */
  exportDataSourceConfig(id) {
    const dataSource = this.getDataSource(id);
    if (!dataSource) {
      return null;
    }

    return {
      id: dataSource.id,
      name: dataSource.name,
      type: dataSource.type,
      config: dataSource.config,
      info: dataSource.getInfo()
    };
  }

  /**
   * 导出所有数据源配置
   * @returns {Array} 配置数组
   */
  exportAllDataSourceConfigs() {
    return this.getAllDataSources().map(ds => this.exportDataSourceConfig(ds.id));
  }

  /**
   * 从配置导入数据源
   * @param {Object} config 配置对象
   * @returns {BaseDataSource} 数据源实例
   */
  importDataSourceFromConfig(config) {
    if (!config.type || !config.config) {
      throw new Error('无效的数据源配置');
    }

    return this.createDataSource(config.type, config.config);
  }

  /**
   * 批量从配置导入数据源
   * @param {Array} configs 配置数组
   * @returns {Array} 导入结果数组
   */
  batchImportDataSourcesFromConfigs(configs) {
    const results = [];

    configs.forEach((config, index) => {
      try {
        const dataSource = this.importDataSourceFromConfig(config);
        results.push({
          success: true,
          dataSource,
          index
        });
      } catch (error) {
        results.push({
          success: false,
          error: error.message,
          index
        });
      }
    });

    return results;
  }

  /**
   * 获取工厂统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const typeStats = {};
    
    this.getAllDataSources().forEach(ds => {
      if (!typeStats[ds.type]) {
        typeStats[ds.type] = 0;
      }
      typeStats[ds.type]++;
    });

    return {
      totalDataSources: this.dataSources.size,
      supportedTypes: this.getSupportedTypes().length,
      typeStatistics: typeStats,
      registeredTypes: Array.from(this.dataSourceTypes.keys())
    };
  }

  /**
   * 清理所有数据源
   */
  clearAll() {
    this.getAllDataSources().forEach(ds => {
      ds.destroy();
    });
    this.dataSources.clear();
  }

  /**
   * 销毁工厂
   */
  destroy() {
    this.clearAll();
    this.dataSourceTypes.clear();
  }
}
