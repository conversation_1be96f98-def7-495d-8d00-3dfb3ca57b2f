import BaseDataSource from "./BaseDataSource.js";

/**
 * 动态数据源
 * 用于处理来自API或其他远程数据源的动态数据
 */
export default class DynamicDataSource extends BaseDataSource {
  constructor(config = {}) {
    super({
      ...config,
      type: 'dynamic'
    });
    
    // 动态数据源特有配置
    this.method = config.method || 'GET';
    this.headers = config.headers || {};
    this.params = config.params || {};
    this.body = config.body || null;
    this.timeout = config.timeout || 30000; // 30秒超时
    
    // 重试配置
    this.retryCount = config.retryCount || 3;
    this.retryDelay = config.retryDelay || 1000; // 1秒
    this.currentRetry = 0;
    
    // 轮询配置
    this.polling = config.polling || false;
    this.pollingInterval = config.pollingInterval || 60000; // 1分钟
    this.pollingTimer = null;
    
    // 响应处理配置
    this.responseType = config.responseType || 'json';
    this.dataPath = config.dataPath || null; // 数据在响应中的路径，如 'data.items'
    
    // 认证配置
    this.auth = config.auth || null;
    
    // 自动开始轮询
    if (this.polling) {
      this.startPolling();
    }
  }

  /**
   * 加载数据
   * @returns {Promise<Array>} 数据数组
   */
  async load() {
    if (!this.url) {
      throw new Error('动态数据源必须提供URL');
    }

    this.currentRetry = 0;
    return this._loadWithRetry();
  }

  /**
   * 带重试的加载
   * @returns {Promise<Array>} 数据数组
   */
  async _loadWithRetry() {
    try {
      const response = await this._makeRequest();
      const data = this._parseResponse(response);
      
      if (!this.validateData(data)) {
        throw new Error('响应数据格式无效');
      }
      
      return data;
    } catch (error) {
      if (this.currentRetry < this.retryCount) {
        this.currentRetry++;
        console.warn(`数据加载失败，正在重试 (${this.currentRetry}/${this.retryCount}):`, error.message);
        
        // 等待重试延迟
        await this._delay(this.retryDelay * this.currentRetry);
        
        return this._loadWithRetry();
      } else {
        throw error;
      }
    }
  }

  /**
   * 发起HTTP请求
   * @returns {Promise<Response>} 响应对象
   */
  async _makeRequest() {
    const url = this._buildUrl();
    const options = this._buildRequestOptions();
    
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }
      
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`请求超时 (${this.timeout}ms)`);
      }
      
      throw error;
    }
  }

  /**
   * 构建请求URL
   * @returns {String} 完整的URL
   */
  _buildUrl() {
    if (this.method === 'GET' && Object.keys(this.params).length > 0) {
      const url = new URL(this.url);
      Object.keys(this.params).forEach(key => {
        url.searchParams.append(key, this.params[key]);
      });
      return url.toString();
    }
    
    return this.url;
  }

  /**
   * 构建请求选项
   * @returns {Object} 请求选项
   */
  _buildRequestOptions() {
    const options = {
      method: this.method,
      headers: {
        'Content-Type': 'application/json',
        ...this.headers
      }
    };

    // 添加认证头
    if (this.auth) {
      if (this.auth.type === 'bearer') {
        options.headers['Authorization'] = `Bearer ${this.auth.token}`;
      } else if (this.auth.type === 'basic') {
        const credentials = btoa(`${this.auth.username}:${this.auth.password}`);
        options.headers['Authorization'] = `Basic ${credentials}`;
      } else if (this.auth.type === 'apikey') {
        options.headers[this.auth.header || 'X-API-Key'] = this.auth.key;
      }
    }

    // 添加请求体
    if (this.method !== 'GET' && this.body) {
      if (typeof this.body === 'object') {
        options.body = JSON.stringify(this.body);
      } else {
        options.body = this.body;
      }
    } else if (this.method === 'POST' && Object.keys(this.params).length > 0) {
      options.body = JSON.stringify(this.params);
    }

    return options;
  }

  /**
   * 解析响应
   * @param {Response} response 响应对象
   * @returns {Promise<Array>} 解析后的数据
   */
  async _parseResponse(response) {
    let data;
    
    switch (this.responseType) {
      case 'json':
        data = await response.json();
        break;
      case 'text':
        data = await response.text();
        break;
      case 'blob':
        data = await response.blob();
        break;
      case 'arrayBuffer':
        data = await response.arrayBuffer();
        break;
      default:
        data = await response.json();
    }

    // 如果指定了数据路径，提取对应的数据
    if (this.dataPath && typeof data === 'object') {
      data = this._extractDataByPath(data, this.dataPath);
    }

    // 确保返回数组格式
    if (!Array.isArray(data)) {
      if (typeof data === 'object' && data !== null) {
        // 如果是对象，尝试找到数组字段
        const arrayFields = Object.keys(data).filter(key => Array.isArray(data[key]));
        if (arrayFields.length === 1) {
          data = data[arrayFields[0]];
        } else {
          // 将对象包装成数组
          data = [data];
        }
      } else {
        throw new Error('无法将响应数据转换为数组格式');
      }
    }

    return data;
  }

  /**
   * 根据路径提取数据
   * @param {Object} obj 对象
   * @param {String} path 路径，如 'data.items'
   * @returns {*} 提取的数据
   */
  _extractDataByPath(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * 延迟函数
   * @param {Number} ms 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 开始轮询
   */
  startPolling() {
    if (this.pollingTimer) {
      this.stopPolling();
    }

    this.polling = true;
    this.pollingTimer = setInterval(async () => {
      try {
        await this.refresh();
      } catch (error) {
        console.error('轮询数据加载失败:', error);
      }
    }, this.pollingInterval);
  }

  /**
   * 停止轮询
   */
  stopPolling() {
    this.polling = false;
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;
    }
  }

  /**
   * 设置轮询间隔
   * @param {Number} interval 间隔毫秒数
   */
  setPollingInterval(interval) {
    this.pollingInterval = interval;
    if (this.polling) {
      this.stopPolling();
      this.startPolling();
    }
  }

  /**
   * 更新请求参数
   * @param {Object} newParams 新参数
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  updateParams(newParams, autoRefresh = true) {
    this.params = { ...this.params, ...newParams };
    
    if (autoRefresh) {
      this.refresh();
    }
  }

  /**
   * 更新请求头
   * @param {Object} newHeaders 新请求头
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  updateHeaders(newHeaders, autoRefresh = true) {
    this.headers = { ...this.headers, ...newHeaders };
    
    if (autoRefresh) {
      this.refresh();
    }
  }

  /**
   * 更新认证信息
   * @param {Object} newAuth 新认证信息
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  updateAuth(newAuth, autoRefresh = true) {
    this.auth = newAuth;
    
    if (autoRefresh) {
      this.refresh();
    }
  }

  /**
   * 设置URL
   * @param {String} newUrl 新URL
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  setUrl(newUrl, autoRefresh = true) {
    this.url = newUrl;
    
    if (autoRefresh) {
      this.refresh();
    }
  }

  /**
   * 获取请求统计信息
   * @returns {Object} 统计信息
   */
  getRequestStatistics() {
    const baseInfo = this.getInfo();
    
    return {
      ...baseInfo,
      url: this.url,
      method: this.method,
      polling: this.polling,
      pollingInterval: this.pollingInterval,
      retryCount: this.retryCount,
      currentRetry: this.currentRetry,
      timeout: this.timeout,
      responseType: this.responseType,
      hasAuth: !!this.auth
    };
  }

  /**
   * 测试连接
   * @returns {Promise<Boolean>} 是否连接成功
   */
  async testConnection() {
    try {
      await this.load();
      return true;
    } catch (error) {
      console.error('连接测试失败:', error);
      return false;
    }
  }

  /**
   * 销毁数据源
   */
  destroy() {
    this.stopPolling();
    super.destroy();
  }
}
