import eventBus from "../../utils/event-bus";
import { EVENT_TYPES, ERROR_TYPES, formatError, generateId } from "../layer/types.js";

/**
 * 数据源基类
 * 定义数据源的基本接口和通用功能
 */
export default class BaseDataSource {
  constructor(config = {}) {
    this.id = config.id || generateId('datasource');
    this.name = config.name || '未命名数据源';
    this.type = config.type || 'static';
    this.url = config.url || null;
    this.data = config.data || null;
    this.config = config;
    
    // 状态管理
    this.status = 'idle'; // idle, loading, loaded, error
    this.lastLoadTime = null;
    this.errorInfo = null;
    
    // 缓存配置
    this.cacheEnabled = config.cacheEnabled !== false;
    this.cacheTimeout = config.cacheTimeout || 300000; // 5分钟
    this.cachedData = null;
    this.cacheTime = null;
    
    // 数据处理配置
    this.fieldMapping = config.fieldMapping || {};
    this.dataFilter = config.dataFilter || null;
    this.dataTransform = config.dataTransform || null;
    
    // 事件回调
    this.onDataLoaded = config.onDataLoaded || null;
    this.onDataError = config.onDataError || null;
    this.onDataUpdated = config.onDataUpdated || null;
    
    // 性能监控
    this.loadCount = 0;
    this.totalLoadTime = 0;
    this.averageLoadTime = 0;
  }

  /**
   * 加载数据（抽象方法，子类必须实现）
   * @returns {Promise<Array>} 数据数组
   */
  async load() {
    throw new Error('子类必须实现 load 方法');
  }

  /**
   * 获取数据
   * @param {Boolean} forceReload 是否强制重新加载
   * @returns {Promise<Array>} 数据数组
   */
  async getData(forceReload = false) {
    try {
      // 检查缓存
      if (!forceReload && this._isCacheValid()) {
        return this.cachedData;
      }

      // 设置加载状态
      this._setStatus('loading');
      
      const startTime = performance.now();
      
      // 加载数据
      const rawData = await this.load();
      
      // 处理数据
      const processedData = this._processData(rawData);
      
      // 更新缓存
      if (this.cacheEnabled) {
        this.cachedData = processedData;
        this.cacheTime = Date.now();
      }
      
      // 更新性能统计
      this._updatePerformanceStats(startTime);
      
      // 设置成功状态
      this._setStatus('loaded');
      this.lastLoadTime = new Date();
      this.errorInfo = null;
      
      // 触发事件
      this._emitDataLoaded(processedData);
      
      return processedData;
    } catch (error) {
      this._handleError(error);
      throw error;
    }
  }

  /**
   * 刷新数据
   * @returns {Promise<Array>} 数据数组
   */
  async refresh() {
    return this.getData(true);
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cachedData = null;
    this.cacheTime = null;
  }

  /**
   * 检查缓存是否有效
   * @returns {Boolean} 是否有效
   */
  _isCacheValid() {
    if (!this.cacheEnabled || !this.cachedData || !this.cacheTime) {
      return false;
    }
    
    const now = Date.now();
    return (now - this.cacheTime) < this.cacheTimeout;
  }

  /**
   * 处理原始数据
   * @param {Array} rawData 原始数据
   * @returns {Array} 处理后的数据
   */
  _processData(rawData) {
    if (!Array.isArray(rawData)) {
      throw new Error('数据源必须返回数组格式的数据');
    }

    let processedData = [...rawData];

    // 应用字段映射
    if (Object.keys(this.fieldMapping).length > 0) {
      processedData = this._applyFieldMapping(processedData);
    }

    // 应用数据过滤
    if (this.dataFilter && typeof this.dataFilter === 'function') {
      processedData = processedData.filter(this.dataFilter);
    }

    // 应用数据转换
    if (this.dataTransform && typeof this.dataTransform === 'function') {
      processedData = processedData.map(this.dataTransform);
    }

    return processedData;
  }

  /**
   * 应用字段映射
   * @param {Array} data 数据数组
   * @returns {Array} 映射后的数据
   */
  _applyFieldMapping(data) {
    return data.map(item => {
      const mappedItem = { ...item };
      
      Object.keys(this.fieldMapping).forEach(sourceField => {
        const targetField = this.fieldMapping[sourceField];
        if (item.hasOwnProperty(sourceField)) {
          mappedItem[targetField] = item[sourceField];
          // 如果目标字段不同，删除原字段
          if (sourceField !== targetField) {
            delete mappedItem[sourceField];
          }
        }
      });
      
      return mappedItem;
    });
  }

  /**
   * 设置状态
   * @param {String} status 状态
   */
  _setStatus(status) {
    this.status = status;
  }

  /**
   * 更新性能统计
   * @param {Number} startTime 开始时间
   */
  _updatePerformanceStats(startTime) {
    const loadTime = performance.now() - startTime;
    this.loadCount++;
    this.totalLoadTime += loadTime;
    this.averageLoadTime = this.totalLoadTime / this.loadCount;
  }

  /**
   * 处理错误
   * @param {Error} error 错误对象
   */
  _handleError(error) {
    this._setStatus('error');
    this.errorInfo = {
      message: error.message,
      timestamp: new Date(),
      stack: error.stack
    };

    const formattedError = formatError(
      ERROR_TYPES.DATA_LOAD_FAILED,
      error.message,
      { dataSourceId: this.id, dataSourceType: this.type }
    );

    // 触发错误事件
    eventBus.emit(EVENT_TYPES.DATA_ERROR, formattedError);
    
    if (this.onDataError) {
      this.onDataError(formattedError);
    }
  }

  /**
   * 触发数据加载完成事件
   * @param {Array} data 数据
   */
  _emitDataLoaded(data) {
    eventBus.emit(EVENT_TYPES.DATA_LOADED, {
      dataSourceId: this.id,
      dataSourceType: this.type,
      data: data,
      loadTime: this.lastLoadTime
    });

    if (this.onDataLoaded) {
      this.onDataLoaded(data);
    }
  }

  /**
   * 获取数据源信息
   * @returns {Object} 数据源信息
   */
  getInfo() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      status: this.status,
      lastLoadTime: this.lastLoadTime,
      errorInfo: this.errorInfo,
      cacheEnabled: this.cacheEnabled,
      cacheTimeout: this.cacheTimeout,
      isCacheValid: this._isCacheValid(),
      performance: {
        loadCount: this.loadCount,
        totalLoadTime: this.totalLoadTime,
        averageLoadTime: this.averageLoadTime
      }
    };
  }

  /**
   * 更新配置
   * @param {Object} newConfig 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // 更新相关属性
    if (newConfig.name) this.name = newConfig.name;
    if (newConfig.url) this.url = newConfig.url;
    if (newConfig.cacheEnabled !== undefined) this.cacheEnabled = newConfig.cacheEnabled;
    if (newConfig.cacheTimeout) this.cacheTimeout = newConfig.cacheTimeout;
    if (newConfig.fieldMapping) this.fieldMapping = newConfig.fieldMapping;
    if (newConfig.dataFilter) this.dataFilter = newConfig.dataFilter;
    if (newConfig.dataTransform) this.dataTransform = newConfig.dataTransform;
    
    // 如果禁用缓存，清除现有缓存
    if (newConfig.cacheEnabled === false) {
      this.clearCache();
    }
  }

  /**
   * 验证数据格式
   * @param {Array} data 数据数组
   * @returns {Boolean} 是否有效
   */
  validateData(data) {
    if (!Array.isArray(data)) {
      return false;
    }

    // 检查数据项的基本结构
    for (const item of data) {
      if (!item || typeof item !== 'object') {
        return false;
      }
    }

    return true;
  }

  /**
   * 销毁数据源
   */
  destroy() {
    this.clearCache();
    this.data = null;
    this.config = null;
    this.onDataLoaded = null;
    this.onDataError = null;
    this.onDataUpdated = null;
  }
}
