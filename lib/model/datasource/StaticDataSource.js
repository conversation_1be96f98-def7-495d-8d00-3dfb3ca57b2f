import BaseDataSource from "./BaseDataSource.js";

/**
 * 静态数据源
 * 用于处理静态数据，如直接传入的数组数据
 */
export default class StaticDataSource extends BaseDataSource {
  constructor(config = {}) {
    super({
      ...config,
      type: 'static'
    });
    
    // 静态数据源特有配置
    this.staticData = config.data || [];
    this.autoLoad = config.autoLoad !== false;
    
    // 如果启用自动加载，立即加载数据
    if (this.autoLoad && this.staticData.length > 0) {
      this._loadStaticData();
    }
  }

  /**
   * 加载数据
   * @returns {Promise<Array>} 数据数组
   */
  async load() {
    return new Promise((resolve, reject) => {
      try {
        // 模拟异步加载
        setTimeout(() => {
          if (!this.validateData(this.staticData)) {
            reject(new Error('静态数据格式无效'));
            return;
          }
          
          resolve([...this.staticData]);
        }, 10); // 10ms 延迟模拟异步
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 设置静态数据
   * @param {Array} data 数据数组
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  setData(data, autoRefresh = true) {
    if (!Array.isArray(data)) {
      throw new Error('数据必须是数组格式');
    }

    this.staticData = [...data];
    
    if (autoRefresh) {
      this._loadStaticData();
    }
  }

  /**
   * 添加数据项
   * @param {Object|Array} items 要添加的数据项
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  addData(items, autoRefresh = true) {
    const itemsArray = Array.isArray(items) ? items : [items];
    this.staticData.push(...itemsArray);
    
    if (autoRefresh) {
      this._loadStaticData();
    }
  }

  /**
   * 删除数据项
   * @param {Function} predicate 删除条件函数
   * @param {Boolean} autoRefresh 是否自动刷新
   * @returns {Array} 被删除的数据项
   */
  removeData(predicate, autoRefresh = true) {
    const removedItems = [];
    
    for (let i = this.staticData.length - 1; i >= 0; i--) {
      if (predicate(this.staticData[i], i)) {
        removedItems.push(this.staticData.splice(i, 1)[0]);
      }
    }
    
    if (autoRefresh && removedItems.length > 0) {
      this._loadStaticData();
    }
    
    return removedItems.reverse(); // 恢复原始顺序
  }

  /**
   * 更新数据项
   * @param {Function} predicate 更新条件函数
   * @param {Function|Object} updater 更新函数或更新对象
   * @param {Boolean} autoRefresh 是否自动刷新
   * @returns {Array} 被更新的数据项
   */
  updateData(predicate, updater, autoRefresh = true) {
    const updatedItems = [];
    
    this.staticData.forEach((item, index) => {
      if (predicate(item, index)) {
        if (typeof updater === 'function') {
          this.staticData[index] = updater(item, index);
        } else if (typeof updater === 'object') {
          this.staticData[index] = { ...item, ...updater };
        }
        updatedItems.push(this.staticData[index]);
      }
    });
    
    if (autoRefresh && updatedItems.length > 0) {
      this._loadStaticData();
    }
    
    return updatedItems;
  }

  /**
   * 查找数据项
   * @param {Function} predicate 查找条件函数
   * @returns {Object|null} 找到的数据项
   */
  findData(predicate) {
    return this.staticData.find(predicate) || null;
  }

  /**
   * 查找所有匹配的数据项
   * @param {Function} predicate 查找条件函数
   * @returns {Array} 找到的数据项数组
   */
  findAllData(predicate) {
    return this.staticData.filter(predicate);
  }

  /**
   * 获取数据项数量
   * @returns {Number} 数据项数量
   */
  getDataCount() {
    return this.staticData.length;
  }

  /**
   * 清空数据
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  clearData(autoRefresh = true) {
    this.staticData = [];
    
    if (autoRefresh) {
      this._loadStaticData();
    }
  }

  /**
   * 排序数据
   * @param {Function} compareFn 比较函数
   * @param {Boolean} autoRefresh 是否自动刷新
   */
  sortData(compareFn, autoRefresh = true) {
    this.staticData.sort(compareFn);
    
    if (autoRefresh) {
      this._loadStaticData();
    }
  }

  /**
   * 获取数据切片
   * @param {Number} start 开始索引
   * @param {Number} end 结束索引
   * @returns {Array} 数据切片
   */
  getDataSlice(start = 0, end = this.staticData.length) {
    return this.staticData.slice(start, end);
  }

  /**
   * 分页获取数据
   * @param {Number} page 页码（从1开始）
   * @param {Number} pageSize 每页大小
   * @returns {Object} 分页结果
   */
  getDataPage(page = 1, pageSize = 10) {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const data = this.getDataSlice(start, end);
    
    return {
      data,
      page,
      pageSize,
      total: this.staticData.length,
      totalPages: Math.ceil(this.staticData.length / pageSize),
      hasNext: end < this.staticData.length,
      hasPrev: page > 1
    };
  }

  /**
   * 私有方法：加载静态数据
   */
  async _loadStaticData() {
    try {
      await this.getData(true); // 强制重新加载
    } catch (error) {
      console.error('加载静态数据失败:', error);
    }
  }

  /**
   * 导出数据
   * @param {String} format 导出格式 (json, csv)
   * @returns {String} 导出的数据字符串
   */
  exportData(format = 'json') {
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(this.staticData, null, 2);
        
      case 'csv':
        return this._exportToCsv();
        
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 导出为CSV格式
   * @returns {String} CSV字符串
   */
  _exportToCsv() {
    if (this.staticData.length === 0) {
      return '';
    }

    // 获取所有字段名
    const fields = new Set();
    this.staticData.forEach(item => {
      Object.keys(item).forEach(key => fields.add(key));
    });
    
    const fieldArray = Array.from(fields);
    
    // 生成CSV头部
    const header = fieldArray.join(',');
    
    // 生成CSV数据行
    const rows = this.staticData.map(item => {
      return fieldArray.map(field => {
        const value = item[field];
        // 处理包含逗号或引号的值
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',');
    });
    
    return [header, ...rows].join('\n');
  }

  /**
   * 从文件导入数据
   * @param {File} file 文件对象
   * @param {String} format 文件格式 (json, csv)
   * @returns {Promise<Array>} 导入的数据
   */
  async importData(file, format = 'json') {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const content = e.target.result;
          let importedData;
          
          switch (format.toLowerCase()) {
            case 'json':
              importedData = JSON.parse(content);
              break;
              
            case 'csv':
              importedData = this._parseCsv(content);
              break;
              
            default:
              throw new Error(`不支持的导入格式: ${format}`);
          }
          
          if (!Array.isArray(importedData)) {
            throw new Error('导入的数据必须是数组格式');
          }
          
          this.setData(importedData);
          resolve(importedData);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsText(file);
    });
  }

  /**
   * 解析CSV内容
   * @param {String} csvContent CSV内容
   * @returns {Array} 解析后的数据数组
   */
  _parseCsv(csvContent) {
    const lines = csvContent.trim().split('\n');
    if (lines.length < 2) {
      return [];
    }
    
    const headers = lines[0].split(',').map(h => h.trim());
    const data = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      const item = {};
      
      headers.forEach((header, index) => {
        item[header] = values[index] || '';
      });
      
      data.push(item);
    }
    
    return data;
  }

  /**
   * 获取数据源统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const baseInfo = this.getInfo();
    
    return {
      ...baseInfo,
      dataCount: this.getDataCount(),
      dataSize: JSON.stringify(this.staticData).length,
      fields: this._getDataFields(),
      dataTypes: this._analyzeDataTypes()
    };
  }

  /**
   * 获取数据字段
   * @returns {Array} 字段数组
   */
  _getDataFields() {
    const fields = new Set();
    this.staticData.forEach(item => {
      Object.keys(item).forEach(key => fields.add(key));
    });
    return Array.from(fields);
  }

  /**
   * 分析数据类型
   * @returns {Object} 数据类型统计
   */
  _analyzeDataTypes() {
    const typeStats = {};
    const fields = this._getDataFields();
    
    fields.forEach(field => {
      const types = new Set();
      this.staticData.forEach(item => {
        if (item.hasOwnProperty(field)) {
          types.add(typeof item[field]);
        }
      });
      typeStats[field] = Array.from(types);
    });
    
    return typeStats;
  }
}
