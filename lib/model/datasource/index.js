import BaseDataSource from "./BaseDataSource.js";
import StaticDataSource from "./StaticDataSource.js";
import DynamicDataSource from "./DynamicDataSource.js";
import DataSourceFactory from "./DataSourceFactory.js";
import { DATA_SOURCE_TYPES } from "../layer/types.js";

/**
 * 数据源系统
 * 整合所有数据源相关功能，提供统一的数据源管理接口
 */
export default class DataSourceSystem {
  constructor() {
    this.factory = new DataSourceFactory();
    this.activeDataSources = new Set(); // 活跃的数据源ID集合
    this.dataCache = new Map(); // 全局数据缓存
    this.cacheTimeout = 300000; // 5分钟缓存超时
    
    // 性能监控
    this.performanceMonitor = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * 创建静态数据源
   * @param {Object} config 配置对象
   * @returns {StaticDataSource} 静态数据源实例
   */
  createStaticDataSource(config = {}) {
    const dataSource = this.factory.createStaticDataSource(config);
    this.activeDataSources.add(dataSource.id);
    return dataSource;
  }

  /**
   * 创建动态数据源
   * @param {Object} config 配置对象
   * @returns {DynamicDataSource} 动态数据源实例
   */
  createDynamicDataSource(config = {}) {
    const dataSource = this.factory.createDynamicDataSource(config);
    this.activeDataSources.add(dataSource.id);
    return dataSource;
  }

  /**
   * 创建数据源（通用方法）
   * @param {String} type 数据源类型
   * @param {Object} config 配置对象
   * @returns {BaseDataSource} 数据源实例
   */
  createDataSource(type, config = {}) {
    const dataSource = this.factory.createDataSource(type, config);
    this.activeDataSources.add(dataSource.id);
    return dataSource;
  }

  /**
   * 获取数据源
   * @param {String} id 数据源ID
   * @returns {BaseDataSource|null} 数据源实例
   */
  getDataSource(id) {
    return this.factory.getDataSource(id);
  }

  /**
   * 获取所有数据源
   * @returns {Array} 数据源数组
   */
  getAllDataSources() {
    return this.factory.getAllDataSources();
  }

  /**
   * 获取活跃的数据源
   * @returns {Array} 活跃数据源数组
   */
  getActiveDataSources() {
    return Array.from(this.activeDataSources)
      .map(id => this.getDataSource(id))
      .filter(Boolean);
  }

  /**
   * 删除数据源
   * @param {String} id 数据源ID
   * @returns {Boolean} 是否删除成功
   */
  deleteDataSource(id) {
    const success = this.factory.deleteDataSource(id);
    if (success) {
      this.activeDataSources.delete(id);
      this._clearDataCache(id);
    }
    return success;
  }

  /**
   * 激活数据源
   * @param {String} id 数据源ID
   * @returns {Boolean} 是否激活成功
   */
  activateDataSource(id) {
    const dataSource = this.getDataSource(id);
    if (dataSource) {
      this.activeDataSources.add(id);
      return true;
    }
    return false;
  }

  /**
   * 停用数据源
   * @param {String} id 数据源ID
   * @returns {Boolean} 是否停用成功
   */
  deactivateDataSource(id) {
    return this.activeDataSources.delete(id);
  }

  /**
   * 加载数据源数据
   * @param {String} id 数据源ID
   * @param {Boolean} useCache 是否使用缓存
   * @returns {Promise<Array>} 数据数组
   */
  async loadData(id, useCache = true) {
    const startTime = performance.now();
    
    try {
      // 检查缓存
      if (useCache) {
        const cachedData = this._getFromCache(id);
        if (cachedData) {
          this._updatePerformanceMonitor(startTime, true);
          return cachedData;
        }
      }

      const dataSource = this.getDataSource(id);
      if (!dataSource) {
        throw new Error(`数据源不存在: ${id}`);
      }

      const data = await dataSource.getData();
      
      // 更新缓存
      if (useCache) {
        this._setToCache(id, data);
      }

      this._updatePerformanceMonitor(startTime, true);
      return data;
    } catch (error) {
      this._updatePerformanceMonitor(startTime, false);
      throw error;
    }
  }

  /**
   * 批量加载数据
   * @param {Array} ids 数据源ID数组
   * @param {Boolean} useCache 是否使用缓存
   * @returns {Promise<Object>} 数据对象，键为数据源ID
   */
  async batchLoadData(ids, useCache = true) {
    const results = {};
    const promises = ids.map(async (id) => {
      try {
        const data = await this.loadData(id, useCache);
        results[id] = { success: true, data };
      } catch (error) {
        results[id] = { success: false, error: error.message };
      }
    });

    await Promise.all(promises);
    return results;
  }

  /**
   * 刷新数据源
   * @param {String} id 数据源ID
   * @returns {Promise<Array>} 数据数组
   */
  async refreshDataSource(id) {
    this._clearDataCache(id);
    return this.loadData(id, false);
  }

  /**
   * 刷新所有活跃数据源
   * @returns {Promise<Object>} 刷新结果
   */
  async refreshAllActiveDataSources() {
    const activeIds = Array.from(this.activeDataSources);
    return this.batchLoadData(activeIds, false);
  }

  /**
   * 合并多个数据源的数据
   * @param {Array} ids 数据源ID数组
   * @param {Function} mergeFunction 合并函数
   * @returns {Promise<Array>} 合并后的数据
   */
  async mergeDataSources(ids, mergeFunction = null) {
    const dataResults = await this.batchLoadData(ids);
    const dataArrays = [];

    // 收集成功加载的数据
    Object.keys(dataResults).forEach(id => {
      const result = dataResults[id];
      if (result.success) {
        dataArrays.push(result.data);
      }
    });

    // 使用自定义合并函数或默认合并
    if (mergeFunction && typeof mergeFunction === 'function') {
      return mergeFunction(dataArrays);
    } else {
      // 默认合并：简单连接所有数组
      return dataArrays.reduce((merged, current) => merged.concat(current), []);
    }
  }

  /**
   * 从缓存获取数据
   * @param {String} id 数据源ID
   * @returns {Array|null} 缓存的数据
   */
  _getFromCache(id) {
    const cached = this.dataCache.get(id);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.cacheTimeout) {
      this.dataCache.delete(id);
      return null;
    }

    return cached.data;
  }

  /**
   * 设置数据到缓存
   * @param {String} id 数据源ID
   * @param {Array} data 数据
   */
  _setToCache(id, data) {
    this.dataCache.set(id, {
      data: [...data], // 深拷贝
      timestamp: Date.now()
    });
  }

  /**
   * 清除数据缓存
   * @param {String} id 数据源ID
   */
  _clearDataCache(id) {
    this.dataCache.delete(id);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.dataCache.clear();
  }

  /**
   * 更新性能监控
   * @param {Number} startTime 开始时间
   * @param {Boolean} success 是否成功
   */
  _updatePerformanceMonitor(startTime, success) {
    const responseTime = performance.now() - startTime;
    
    this.performanceMonitor.totalRequests++;
    this.performanceMonitor.totalResponseTime += responseTime;
    this.performanceMonitor.averageResponseTime = 
      this.performanceMonitor.totalResponseTime / this.performanceMonitor.totalRequests;

    if (success) {
      this.performanceMonitor.successfulRequests++;
    } else {
      this.performanceMonitor.failedRequests++;
    }
  }

  /**
   * 注册自定义数据源类型
   * @param {String} type 类型名称
   * @param {Class} DataSourceClass 数据源类
   */
  registerDataSourceType(type, DataSourceClass) {
    this.factory.registerDataSourceType(type, DataSourceClass);
  }

  /**
   * 获取支持的数据源类型
   * @returns {Array} 类型数组
   */
  getSupportedTypes() {
    return this.factory.getSupportedTypes();
  }

  /**
   * 获取系统统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const factoryStats = this.factory.getStatistics();
    
    return {
      ...factoryStats,
      activeDataSources: this.activeDataSources.size,
      cacheSize: this.dataCache.size,
      performance: this.performanceMonitor
    };
  }

  /**
   * 导出系统配置
   * @returns {Object} 系统配置
   */
  exportConfig() {
    return {
      dataSources: this.factory.exportAllDataSourceConfigs(),
      activeDataSources: Array.from(this.activeDataSources),
      cacheTimeout: this.cacheTimeout,
      statistics: this.getStatistics()
    };
  }

  /**
   * 从配置导入系统
   * @param {Object} config 系统配置
   * @returns {Object} 导入结果
   */
  importConfig(config) {
    const results = {
      dataSources: [],
      errors: []
    };

    // 导入数据源
    if (config.dataSources && Array.isArray(config.dataSources)) {
      const importResults = this.factory.batchImportDataSourcesFromConfigs(config.dataSources);
      results.dataSources = importResults;
    }

    // 恢复活跃状态
    if (config.activeDataSources && Array.isArray(config.activeDataSources)) {
      config.activeDataSources.forEach(id => {
        if (this.getDataSource(id)) {
          this.activeDataSources.add(id);
        }
      });
    }

    // 恢复缓存超时设置
    if (config.cacheTimeout) {
      this.cacheTimeout = config.cacheTimeout;
    }

    return results;
  }

  /**
   * 销毁数据源系统
   */
  destroy() {
    this.clearAllCache();
    this.factory.destroy();
    this.activeDataSources.clear();
    this.performanceMonitor = null;
  }
}

// 导出所有相关类和常量
export {
  BaseDataSource,
  StaticDataSource,
  DynamicDataSource,
  DataSourceFactory,
  DATA_SOURCE_TYPES
};
