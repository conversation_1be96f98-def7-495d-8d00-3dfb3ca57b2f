import eventBus, { eventMap } from "../utils/event-bus";

/**
 * 增强绘图工具类
 * 支持编辑模式、撤销重做、辅助功能等高级特性
 */
export default class DrawingTool {
  /**
   * 创建绘图工具实例
   * @param {BMap.Map} map - 百度地图实例
   * @param {Object} options - 配置选项
   * @param {Object} [options.polygons] - 多边形配置
   * @param {Object} [options.polylines] - 折线配置
   * @param {Object} [options.circles] - 圆形配置
   * @param {Object} [options.rectangles] - 矩形配置
   * @param {Object} [options.markers] - 标记点配置
   * @param {Function} [options.drawCallback] - 绘制完成回调函数
   * @param {string} [options.drawType='marker'] - 默认绘制类型
   * @param {boolean} [options.enableEdit=true] - 是否启用编辑模式
   * @param {boolean} [options.enableUndo=true] - 是否启用撤销重做
   * @param {boolean} [options.enableSnap=false] - 是否启用吸附功能
   * @param {number} [options.maxHistorySize=50] - 最大历史记录数量
   */
  constructor(map, options = {}) {
    this._map = map;
    this._drawingManager = null;

    // 基础配置
    this.enableEdit = options.enableEdit !== false;
    this.enableUndo = options.enableUndo !== false;
    this.enableSnap = options.enableSnap || false;
    this.maxHistorySize = options.maxHistorySize || 50;

    // 绘制图形的样式配置
    this.styleOptions = {
      strokeColor: "#096dd9", // 边线颜色
      fillColor: "#40a9ff7d", // 填充颜色，当参数为空时，圆形将没有填充效果
      strokeWeight: 2, // 边线的宽度，以像素为单位
      strokeOpacity: 0.8, // 边线透明度，取值范围0 - 1
      fillOpacity: 0.6, // 填充的透明度，取值范围0 - 1
      strokeStyle: "solid", // 边线的样式，solid或dashed
    };

    // 编辑模式样式
    this.editStyleOptions = {
      strokeColor: "#ff4d4f",
      fillColor: "#ff7875",
      strokeWeight: 3,
      strokeOpacity: 1,
      fillOpacity: 0.3,
      strokeStyle: "solid",
    };

    // 初始化各类图形数据
    this.polygons = options.polygons || { pointList: [] };
    this.polylines = options.polylines || {};
    this.circles = options.circles || {};
    this.rectangles = options.rectangles || {};
    this.markers = options.markers || {};

    // 绘制完成的回调函数
    this.drawCallback = options.drawCallback;

    // 默认绘制类型
    this.drawType = options.drawType || "marker";

    // 编辑状态
    this.isEditing = false;
    this.editingOverlay = null;
    this.editingType = null;

    // 撤销重做历史记录
    this.history = [];
    this.historyIndex = -1;

    // 辅助功能
    this.snapDistance = 10; // 吸附距离（像素）
    this.showMeasurement = true; // 显示测量信息
    this.showGrid = false; // 显示网格

    // 临时覆盖物存储
    this.tempOverlays = [];

    // 初始化绘图工具
    this.init();
  }

  /**
   * 初始化绘图管理器
   */
  init() {
    // 创建百度地图绘图管理器实例
    if (typeof BMapLib !== 'undefined' && BMapLib.DrawingManager) {
      this._drawingManager = new BMapLib.DrawingManager(this._map, {
        isOpen: false, // 是否开启绘制模式
        enableDrawingTool: false, // 是否显示工具栏
        enableCalculate: this.showMeasurement, // 启用面积计算
        drawingToolOptions: {
          anchor: BMAP_ANCHOR_TOP_RIGHT, // 工具栏位置
          offset: new BMap.Size(5, 5), // 工具栏偏移量
        },
        // 设置各类图形的样式
        circleOptions: this.styleOptions, // 圆的样式
        polylineOptions: this.styleOptions, // 线的样式
        polygonOptions: this.styleOptions, // 多边形的样式
        rectangleOptions: this.styleOptions, // 矩形的样式
        markerOptions: {
          icon: this._createDefaultMarkerIcon()
        }
      });

      // 添加覆盖物绘制完成事件
      this._drawingManager.addEventListener("overlaycomplete", (n, e) => {
        this.drawOverlayComplete(e);
      });

      // 添加绘制开始事件
      this._drawingManager.addEventListener("drawingstart", (e) => {
        this._onDrawingStart(e);
      });
    }

    // 初始化键盘事件
    this._initKeyboardEvents();

    // 初始化历史记录
    this._saveToHistory();
  }

  /**
   * 创建默认标记图标
   */
  _createDefaultMarkerIcon() {
    return new BMap.Icon(
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDOC4xMyAyIDUgNS4xMyA1IDlDNSAxNC4yNSAxMiAyMiAxMiAyMkMxMiAyMiAxOSAxNC4yNSAxOSA5QzE5IDUuMTMgMTUuODcgMiAxMiAyWk0xMiAxMS41QzEwLjYyIDExLjUgOS41IDEwLjM4IDkuNSA5QzkuNSA3LjYyIDEwLjYyIDYuNSAxMiA2LjVDMTMuMzggNi41IDE0LjUgNy42MiAxNC41IDlDMTQuNSAxMC4zOCAxMy4zOCAxMS41IDEyIDExLjVaIiBmaWxsPSIjMDk2ZGQ5Ii8+Cjwvc3ZnPg==',
      new BMap.Size(24, 24),
      {
        anchor: new BMap.Size(12, 24),
        imageSize: new BMap.Size(24, 24)
      }
    );
  }

  /**
   * 初始化键盘事件
   */
  _initKeyboardEvents() {
    if (!this.enableUndo) return;

    document.addEventListener('keydown', (e) => {
      // Ctrl+Z 撤销
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        this.undo();
      }
      // Ctrl+Shift+Z 或 Ctrl+Y 重做
      else if ((e.ctrlKey && e.shiftKey && e.key === 'Z') || (e.ctrlKey && e.key === 'y')) {
        e.preventDefault();
        this.redo();
      }
      // Escape 退出编辑模式
      else if (e.key === 'Escape') {
        this.exitEditMode();
      }
      // Delete 删除选中的覆盖物
      else if (e.key === 'Delete' && this.editingOverlay) {
        this.deleteEditingOverlay();
      }
    });
  }

  /**
   * 绘制开始事件处理
   */
  _onDrawingStart(e) {
    // 退出当前编辑模式
    this.exitEditMode();

    // 触发绘制开始事件
    eventBus.emit(eventMap.drawingStart, {
      drawingMode: e.drawingMode,
      tool: this
    });
  }

  /**
   * 处理覆盖物绘制完成事件
   * @param {Object} e - 绘制完成事件对象
   */
  drawOverlayComplete(e) {
    if (!e.drawingMode) return;

    // 立即清除鼠标绘制的临时覆盖物
    this._map && this._map.removeOverlay(e.overlay);

    const overlay = e.overlay;
    const drawPolygonTypeList = ["polygon", "polyline", "circle", "rectangle"];

    // 生成唯一ID
    const overlayId = this._generateId();

    // 处理多边形类型的覆盖物
    if (drawPolygonTypeList.includes(e.drawingMode)) {
      const path = overlay.getPath();
      const overLayCenterPoint = this.getOverLayCenterPoint(path);

      const polygonOverlay = {
        id: overlayId,
        pointList: path.map((p) => [p.lng, p.lat]),
        calculate: e.calculate,
        editing: false,
        centerPoint: overLayCenterPoint,
        area: this._calculateArea(path, e.drawingMode),
        perimeter: this._calculatePerimeter(path),
        createdAt: new Date(),
        type: e.drawingMode
      };

      // 根据绘制类型设置对应的覆盖物数据
      switch (e.drawingMode) {
        case "polygon":
          this.polygons = polygonOverlay;
          this.drawType = "polygon";
          break;
        case "polyline":
          this.polylines = polygonOverlay;
          this.drawType = "polyline";
          break;
        case "circle":
          this.circles = polygonOverlay;
          this.drawType = "circle";
          break;
        case "rectangle":
          this.rectangles = polygonOverlay;
          this.drawType = "rectangle";
          break;
        default:
          break;
      }
    } else {
      // 处理标记点类型的覆盖物
      const { lat, lng } = overlay.point;
      this.markers = {
        id: overlayId,
        point: { lat, lng },
        createdAt: new Date(),
        type: "marker"
      };
      this.drawType = "marker";
    }

    // 保存到历史记录
    this._saveToHistory();

    // 触发绘制完成事件
    eventBus.emit(eventMap.drawingComplete, {
      drawingMode: e.drawingMode,
      overlay: this._getCurrentOverlayData(),
      tool: this
    });

    // 调用绘制完成回调函数
    if (this.drawCallback) {
      this.drawCallback(this.drawType, {
        marker: this.markers,
        polygon: this.polygons,
        polyline: this.polylines,
        circle: this.circles,
        rectangle: this.rectangles,
      });
    }
  }

  /**
   * 生成唯一ID
   */
  _generateId() {
    return 'overlay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取当前覆盖物数据
   */
  _getCurrentOverlayData() {
    switch (this.drawType) {
      case "marker":
        return this.markers;
      case "polygon":
        return this.polygons;
      case "polyline":
        return this.polylines;
      case "circle":
        return this.circles;
      case "rectangle":
        return this.rectangles;
      default:
        return null;
    }
  }

  /**
   * 计算面积
   */
  _calculateArea(path, type) {
    if (type === "polyline") return 0;

    if (typeof BMapLib !== 'undefined' && BMapLib.GeoUtils) {
      if (type === "circle") {
        // 圆形面积计算
        const center = path[0];
        const edge = path[1];
        const radius = BMapLib.GeoUtils.getDistance(center, edge);
        return Math.PI * radius * radius;
      } else {
        // 多边形面积计算
        return BMapLib.GeoUtils.getPolygonArea(path);
      }
    }

    return 0;
  }

  /**
   * 计算周长
   */
  _calculatePerimeter(path) {
    if (path.length < 2) return 0;

    let perimeter = 0;
    for (let i = 0; i < path.length - 1; i++) {
      perimeter += this._getDistance(path[i], path[i + 1]);
    }

    return perimeter;
  }

  /**
   * 计算两点间距离
   */
  _getDistance(point1, point2) {
    if (typeof BMapLib !== 'undefined' && BMapLib.GeoUtils) {
      return BMapLib.GeoUtils.getDistance(point1, point2);
    }

    // 简单的距离计算
    const R = 6371000; // 地球半径（米）
    const lat1 = point1.lat * Math.PI / 180;
    const lat2 = point2.lat * Math.PI / 180;
    const deltaLat = (point2.lat - point1.lat) * Math.PI / 180;
    const deltaLng = (point2.lng - point1.lng) * Math.PI / 180;

    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * 计算多边形的中心点坐标
   * @param {Array<BMap.Point>} path - 多边形路径点数组
   * @returns {Object} 中心点坐标对象，包含lng和lat属性
   */
  getOverLayCenterPoint(path) {
    let x = 0.0;
    let y = 0.0;

    // 计算所有点的坐标总和
    for (let i = 0; i < path.length; i++) {
      x = x + parseFloat(path[i].lng);
      y = y + parseFloat(path[i].lat);
    }

    // 计算平均值，即为中心点坐标
    x = x / path.length;
    y = y / path.length;

    return {
      lng: x,
      lat: y,
    };
  }

  // ==================== 编辑模式功能 ====================

  /**
   * 进入编辑模式
   * @param {String} type 覆盖物类型
   * @param {String} overlayId 覆盖物ID
   */
  enterEditMode(type, overlayId) {
    if (!this.enableEdit) return false;

    // 退出当前编辑模式
    this.exitEditMode();

    this.isEditing = true;
    this.editingType = type;
    this.editingOverlay = this._getCurrentOverlayData();

    // 触发编辑模式开始事件
    eventBus.emit(eventMap.editModeStart, {
      type,
      overlayId,
      overlay: this.editingOverlay,
      tool: this
    });

    return true;
  }

  /**
   * 退出编辑模式
   */
  exitEditMode() {
    if (!this.isEditing) return;

    this.isEditing = false;
    this.editingType = null;
    this.editingOverlay = null;

    // 清理临时覆盖物
    this._clearTempOverlays();

    // 触发编辑模式结束事件
    eventBus.emit(eventMap.editModeEnd, {
      tool: this
    });
  }

  /**
   * 删除正在编辑的覆盖物
   */
  deleteEditingOverlay() {
    if (!this.isEditing || !this.editingOverlay) return false;

    // 保存到历史记录
    this._saveToHistory();

    // 清空对应的覆盖物数据
    switch (this.editingType) {
      case "marker":
        this.markers = {};
        break;
      case "polygon":
        this.polygons = {};
        break;
      case "polyline":
        this.polylines = {};
        break;
      case "circle":
        this.circles = {};
        break;
      case "rectangle":
        this.rectangles = {};
        break;
    }

    // 退出编辑模式
    this.exitEditMode();

    // 触发删除事件
    eventBus.emit(eventMap.overlayDeleted, {
      type: this.editingType,
      overlay: this.editingOverlay,
      tool: this
    });

    return true;
  }

  /**
   * 清理临时覆盖物
   */
  _clearTempOverlays() {
    this.tempOverlays.forEach(overlay => {
      if (overlay && this._map) {
        this._map.removeOverlay(overlay);
      }
    });
    this.tempOverlays = [];
  }

  // ==================== 撤销重做功能 ====================

  /**
   * 保存到历史记录
   */
  _saveToHistory() {
    if (!this.enableUndo) return;

    const currentState = {
      markers: JSON.parse(JSON.stringify(this.markers)),
      polygons: JSON.parse(JSON.stringify(this.polygons)),
      polylines: JSON.parse(JSON.stringify(this.polylines)),
      circles: JSON.parse(JSON.stringify(this.circles)),
      rectangles: JSON.parse(JSON.stringify(this.rectangles)),
      drawType: this.drawType,
      timestamp: Date.now()
    };

    // 如果当前不在历史记录的末尾，删除后面的记录
    if (this.historyIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.historyIndex + 1);
    }

    // 添加新的历史记录
    this.history.push(currentState);

    // 限制历史记录数量
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    } else {
      this.historyIndex++;
    }
  }

  /**
   * 撤销操作
   */
  undo() {
    if (!this.enableUndo || this.historyIndex <= 0) return false;

    this.historyIndex--;
    this._restoreFromHistory();

    eventBus.emit(eventMap.drawingUndo, {
      historyIndex: this.historyIndex,
      tool: this
    });

    return true;
  }

  /**
   * 重做操作
   */
  redo() {
    if (!this.enableUndo || this.historyIndex >= this.history.length - 1) return false;

    this.historyIndex++;
    this._restoreFromHistory();

    eventBus.emit(eventMap.drawingRedo, {
      historyIndex: this.historyIndex,
      tool: this
    });

    return true;
  }

  /**
   * 从历史记录恢复状态
   */
  _restoreFromHistory() {
    if (this.historyIndex < 0 || this.historyIndex >= this.history.length) return;

    const state = this.history[this.historyIndex];
    this.markers = JSON.parse(JSON.stringify(state.markers));
    this.polygons = JSON.parse(JSON.stringify(state.polygons));
    this.polylines = JSON.parse(JSON.stringify(state.polylines));
    this.circles = JSON.parse(JSON.stringify(state.circles));
    this.rectangles = JSON.parse(JSON.stringify(state.rectangles));
    this.drawType = state.drawType;

    // 退出编辑模式
    this.exitEditMode();
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.history = [];
    this.historyIndex = -1;
    this._saveToHistory();
  }

  /**
   * 获取历史记录信息
   */
  getHistoryInfo() {
    return {
      total: this.history.length,
      current: this.historyIndex,
      canUndo: this.historyIndex > 0,
      canRedo: this.historyIndex < this.history.length - 1
    };
  }

  // ==================== 辅助功能 ====================

  /**
   * 启用/禁用吸附功能
   * @param {Boolean} enabled 是否启用
   */
  setSnapEnabled(enabled) {
    this.enableSnap = enabled;
  }

  /**
   * 设置吸附距离
   * @param {Number} distance 距离（像素）
   */
  setSnapDistance(distance) {
    this.snapDistance = Math.max(5, Math.min(50, distance));
  }

  /**
   * 启用/禁用测量显示
   * @param {Boolean} enabled 是否启用
   */
  setMeasurementEnabled(enabled) {
    this.showMeasurement = enabled;
    if (this._drawingManager) {
      this._drawingManager.enableCalculate = enabled;
    }
  }

  /**
   * 启用/禁用网格显示
   * @param {Boolean} enabled 是否启用
   */
  setGridEnabled(enabled) {
    this.showGrid = enabled;
    // 这里可以实现网格显示逻辑
  }

  /**
   * 获取吸附点
   * @param {BMap.Point} point 当前点
   * @returns {BMap.Point|null} 吸附点
   */
  _getSnapPoint(point) {
    if (!this.enableSnap) return null;

    // 这里可以实现吸附逻辑，比如吸附到现有覆盖物的顶点
    // 简化实现，返回null
    return null;
  }

  // ==================== 样式管理 ====================

  /**
   * 更新绘制样式
   * @param {Object} styleOptions 样式选项
   */
  updateStyle(styleOptions) {
    this.styleOptions = { ...this.styleOptions, ...styleOptions };

    if (this._drawingManager) {
      this._drawingManager.setDrawingOptions({
        circleOptions: this.styleOptions,
        polylineOptions: this.styleOptions,
        polygonOptions: this.styleOptions,
        rectangleOptions: this.styleOptions,
      });
    }
  }

  /**
   * 更新编辑样式
   * @param {Object} styleOptions 样式选项
   */
  updateEditStyle(styleOptions) {
    this.editStyleOptions = { ...this.editStyleOptions, ...styleOptions };
  }

  /**
   * 获取当前样式
   * @returns {Object} 样式对象
   */
  getCurrentStyle() {
    return this.isEditing ? this.editStyleOptions : this.styleOptions;
  }

  // ==================== 数据管理 ====================

  /**
   * 清空所有覆盖物数据
   */
  emptyOverlay() {
    // 保存到历史记录
    this._saveToHistory();

    this.polygons = {};
    this.polylines = {};
    this.circles = {};
    this.rectangles = {};
    this.markers = {};

    // 退出编辑模式
    this.exitEditMode();

    // 触发清空事件
    eventBus.emit(eventMap.overlaysCleared, {
      tool: this
    });
  }

  /**
   * 获取所有覆盖物数据
   * @returns {Object} 所有覆盖物数据
   */
  getAllOverlays() {
    return {
      markers: this.markers,
      polygons: this.polygons,
      polylines: this.polylines,
      circles: this.circles,
      rectangles: this.rectangles,
      drawType: this.drawType
    };
  }

  /**
   * 设置覆盖物数据
   * @param {Object} overlays 覆盖物数据
   */
  setAllOverlays(overlays) {
    // 保存到历史记录
    this._saveToHistory();

    this.markers = overlays.markers || {};
    this.polygons = overlays.polygons || {};
    this.polylines = overlays.polylines || {};
    this.circles = overlays.circles || {};
    this.rectangles = overlays.rectangles || {};
    this.drawType = overlays.drawType || "marker";

    // 退出编辑模式
    this.exitEditMode();
  }

  /**
   * 导出覆盖物数据
   * @param {String} format 导出格式 (json, geojson)
   * @returns {String} 导出的数据
   */
  exportOverlays(format = 'json') {
    const data = this.getAllOverlays();

    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'geojson':
        return this._convertToGeoJSON(data);
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 转换为GeoJSON格式
   * @param {Object} data 覆盖物数据
   * @returns {String} GeoJSON字符串
   */
  _convertToGeoJSON(data) {
    const features = [];

    // 转换标记点
    if (data.markers && data.markers.point) {
      features.push({
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [data.markers.point.lng, data.markers.point.lat]
        },
        properties: {
          type: "marker",
          id: data.markers.id,
          createdAt: data.markers.createdAt
        }
      });
    }

    // 转换多边形
    if (data.polygons && data.polygons.pointList) {
      features.push({
        type: "Feature",
        geometry: {
          type: "Polygon",
          coordinates: [data.polygons.pointList]
        },
        properties: {
          type: "polygon",
          id: data.polygons.id,
          area: data.polygons.area,
          perimeter: data.polygons.perimeter,
          createdAt: data.polygons.createdAt
        }
      });
    }

    // 转换折线
    if (data.polylines && data.polylines.pointList) {
      features.push({
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: data.polylines.pointList
        },
        properties: {
          type: "polyline",
          id: data.polylines.id,
          length: data.polylines.perimeter,
          createdAt: data.polylines.createdAt
        }
      });
    }

    const geoJSON = {
      type: "FeatureCollection",
      features
    };

    return JSON.stringify(geoJSON, null, 2);
  }

  /**
   * 获取覆盖物的中心点
   * @param {Array} path - 覆盖物路径点数组
   * @returns {Object} 中心点坐标 {lat, lng}
   */
  getOverLayCenterPoint(path) {
    if (!path || path.length === 0) return null;

    let totalLat = 0;
    let totalLng = 0;

    path.forEach((point) => {
      totalLat += point.lat;
      totalLng += point.lng;
    });

    return {
      lat: totalLat / path.length,
      lng: totalLng / path.length,
    };
  }

  /**
   * 获取绘图工具统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const overlays = this.getAllOverlays();
    const hasMarker = Object.keys(overlays.markers).length > 0;
    const hasPolygon = Object.keys(overlays.polygons).length > 0;
    const hasPolyline = Object.keys(overlays.polylines).length > 0;
    const hasCircle = Object.keys(overlays.circles).length > 0;
    const hasRectangle = Object.keys(overlays.rectangles).length > 0;

    return {
      totalOverlays: [hasMarker, hasPolygon, hasPolyline, hasCircle, hasRectangle].filter(Boolean).length,
      overlayTypes: {
        marker: hasMarker,
        polygon: hasPolygon,
        polyline: hasPolyline,
        circle: hasCircle,
        rectangle: hasRectangle
      },
      currentDrawType: this.drawType,
      isEditing: this.isEditing,
      editingType: this.editingType,
      historyInfo: this.getHistoryInfo(),
      features: {
        enableEdit: this.enableEdit,
        enableUndo: this.enableUndo,
        enableSnap: this.enableSnap,
        showMeasurement: this.showMeasurement,
        showGrid: this.showGrid
      }
    };
  }

  /**
   * 销毁绘图工具
   */
  destroy() {
    // 退出编辑模式
    this.exitEditMode();

    // 清理临时覆盖物
    this._clearTempOverlays();

    // 清理事件监听器
    if (this._drawingManager) {
      this._drawingManager.removeEventListener("overlaycomplete");
      this._drawingManager.removeEventListener("drawingstart");
      this._drawingManager.close();
      this._drawingManager = null;
    }

    // 清理键盘事件监听器
    document.removeEventListener('keydown', this._keydownHandler);

    // 清理数据
    this.emptyOverlay();
    this.clearHistory();

    // 清理引用
    this._map = null;
    this.drawCallback = null;
  }
}
