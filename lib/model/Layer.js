import LayerConfig from "./LayerConfig";
import Marker from "./Marker";
import Polygon from "./Polygon";
import Polyline from "./Polyline";
import { cloneDeep } from "lodash-es";

/**
 * 地图图层类
 * 管理地图上的覆盖物集合，提供统一的添加、删除和管理功能
 * @class Layer
 */
export default class Layer {
  /**
   * 创建图层实例
   * @param {Object} map - 百度地图实例
   * @param {Object} layerConfig - 图层配置信息
   */
  constructor(map, layerConfig) {
    /**
     * 百度地图实例
     * @type {Object}
     */
    this.map = map;
    /**
     * 图层配置实例
     * @type {LayerConfig}
     */
    this.layerConfig = new LayerConfig(layerConfig);
    /**
     * 覆盖物类型映射表
     * @type {Object}
     */
    this.overlayTypeMap = {
      marker: Marker,
      polygon: Polygon,
      polyline: Polyline,
    };

    this.init();
  }

  /**
   * 初始化图层
   * @private
   */
  init() {
    // 设置默认覆盖物类型处理方法
    this.addOverlay = this._createOverlay.bind(
      this,
      this.overlayTypeMap[this.layerConfig.type] || Marker,
    );

    // 初始化已有的覆盖物
    if (this.layerConfig.coverCells && this.layerConfig.coverCells.length) {
      const coverCells = [...this.layerConfig.coverCells];
      this.layerConfig.clear();
      this.batchAdd(coverCells);
    }
  }

  /**
   * 创建覆盖物的通用方法
   * @private
   * @param {Class} OverlayClass - 覆盖物类
   * @param {Object} config - 覆盖物配置
   * @returns {Object} 覆盖物实例
   */
  _createOverlay(OverlayClass, config) {
    // 合并图层样式与覆盖物自身样式
    config.options = {
      ...this.layerConfig.style,
      ...config.options,
    };

    // 创建覆盖物实例
    const overlay = new OverlayClass(this, cloneDeep(config));
    // 添加到地图并记录到图层配置
    this.map.addOverlay(overlay._cell);
    this.layerConfig.addCoverCell(overlay);

    return overlay;
  }

  /**
   * 清除图层上的所有覆盖物
   * @public
   */
  clear() {
    this.layerConfig.coverCells.forEach((cell) => {
      cell.destroy();
      this.map.removeOverlay(cell._cell);
    });

    this.layerConfig.clear();
  }

  /**
   * 批量添加覆盖物
   * @public
   * @param {Array|Object} cells - 覆盖物配置数组或单个覆盖物配置
   */
  batchAdd(cells) {
    if (Array.isArray(cells)) {
      cells.forEach((cell) => {
        this.addOverlay(cell);
      });
    } else {
      this.addOverlay(cells);
    }
  }

  /**
   * 添加单个覆盖物
   * @public
   * @param {Object} cell - 覆盖物配置
   * @returns {Object} 覆盖物实例
   */
  add(cell) {
    return this.addOverlay(cell);
  }

  /**
   * 销毁图层，清除所有覆盖物并释放资源
   * @public
   */
  destroy() {
    this.clear();
    this.layerConfig = null;
    this.map = null;
    this.overlayTypeMap = null;
  }
}
