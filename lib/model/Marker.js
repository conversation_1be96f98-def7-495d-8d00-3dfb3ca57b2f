import BaseCell from "./BaseCell";

/**
 * 增强的标记点类
 * 支持拖拽、自定义图标、标签、右键菜单等功能
 */
export default class Marker extends BaseCell {
  constructor(layer, param) {
    super(layer, param);

    // 基础属性
    this.id = param.id || this._generateId();
    this.name = param.name || "未命名标记";
    this.draggable = param.draggable || false;
    this.showLabel = param.showLabel !== false; // 默认显示标签

    // 扩展数据
    this.extData = param.extData || {};
    this.data = param.data || {};

    // 处理图标配置
    this._processIcon(param);

    // 创建标记点
    this._cell = new BMap.Marker(new BMap.Point(param.lng, param.lat), param.options);

    // 创建标签
    if (this.showLabel) {
      this._createLabel();
    }

    // 设置拖拽
    if (this.draggable) {
      this._cell.enableDragging();
    }

    this.initEvent();
    this._initEnhancedEvents();
  }

  /**
   * 生成唯一ID
   */
  _generateId() {
    return 'marker_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 处理图标配置
   */
  _processIcon(param) {
    if (param.options && param.options.icon) {
      const {
        url,
        size,
        anchor = { width: 0, height: 0 },
        imageOffset = { width: 0, height: 0 },
        infoWindowAnchor = { width: 0, height: 0 },
      } = param.options.icon;

      if (url) {
        const _size = new BMap.Size(size.width || 120, size.height || 120);
        const _anchor = new BMap.Size(
          anchor.width || size.width / 2 || 0,
          anchor.height || size.height || 0,
        );

        param.options.icon = new BMap.Icon(url, _size, {
          anchor: _anchor,
          imageOffset: new BMap.Size(imageOffset.width || 0, imageOffset.height || 0),
          imageSize: _size,
          infoWindowAnchor: new BMap.Size(
            infoWindowAnchor.width || 0,
            infoWindowAnchor.height || 0,
          ),
        });
      }
    }
  }

  /**
   * 创建标签
   */
  _createLabel() {
    const labelText = this.name || "";
    this.label = new BMap.Label(labelText);

    // 设置标签样式
    this.label.setStyle({
      color: "rgb(51, 51, 51)",
      border: "1px solid #e8e8e8",
      padding: "2px 6px",
      fontSize: "12px",
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      borderRadius: "3px",
      opacity: labelText ? 1 : 0,
    });

    // 计算标签偏移
    this._updateLabelOffset();

    // 将标签附加到标记点
    this._cell.setLabel(this.label);
  }

  /**
   * 更新标签偏移位置
   */
  _updateLabelOffset() {
    if (!this.label) return;

    const labelWidth = this._getLabelWidth(this.label.getContent());
    const iconSize = this._getIconSize();

    const offsetLeft = iconSize.width / 2 - labelWidth / 2;
    const offsetTop = iconSize.height + 5; // 标签在图标下方5px

    this.label.setOffset(new BMap.Size(offsetLeft, offsetTop));
  }

  /**
   * 获取标签文本宽度
   */
  _getLabelWidth(text) {
    const tempElement = document.createElement("span");
    tempElement.style.visibility = "hidden";
    tempElement.style.fontSize = "12px";
    tempElement.style.position = "absolute";
    tempElement.textContent = text || "";

    document.body.appendChild(tempElement);
    const width = tempElement.offsetWidth;
    document.body.removeChild(tempElement);

    return width;
  }

  /**
   * 获取图标尺寸
   */
  _getIconSize() {
    const icon = this._cell.getIcon();
    if (icon && icon.size) {
      return { width: icon.size.width, height: icon.size.height };
    }
    return { width: 20, height: 20 }; // 默认尺寸
  }

  /**
   * 初始化增强事件
   */
  _initEnhancedEvents() {
    // 拖拽事件
    if (this.draggable) {
      this._cell.addEventListener("dragend", (e) => {
        this._onDragEnd(e);
      });
    }

    // 右键菜单事件
    this._cell.addEventListener("rightclick", (e) => {
      this._onRightClick(e);
    });
  }

  /**
   * 拖拽结束事件处理
   */
  _onDragEnd(e) {
    const newPosition = this._cell.getPosition();

    // 触发拖拽结束事件
    if (this.onDragEnd) {
      this.onDragEnd({
        marker: this,
        oldPosition: this.position,
        newPosition: newPosition,
        event: e
      });
    }

    // 更新位置
    this.position = newPosition;
  }

  /**
   * 右键点击事件处理
   */
  _onRightClick(e) {
    if (this.onRightClick) {
      this.onRightClick({
        marker: this,
        position: this._cell.getPosition(),
        event: e
      });
    }
  }

  /**
   * 显示标记点
   */
  show() {
    this._cell.show();
    if (this.label) {
      this.label.show();
    }
  }

  /**
   * 隐藏标记点
   */
  hide() {
    this._cell.hide();
    if (this.label) {
      this.label.hide();
    }
  }

  /**
   * 设置位置
   */
  setPosition(lng, lat) {
    const point = new BMap.Point(lng, lat);
    this._cell.setPosition(point);
    this.position = point;
  }

  /**
   * 获取位置
   */
  getPosition() {
    return this._cell.getPosition();
  }

  /**
   * 更新标签文本
   */
  updateLabel(text, style = {}) {
    if (!this.label) {
      this._createLabel();
    }

    this.name = text;
    this.label.setContent(text);

    // 更新样式
    if (Object.keys(style).length > 0) {
      this.label.setStyle(style);
    }

    // 重新计算偏移
    this._updateLabelOffset();
  }

  /**
   * 设置图标
   */
  setIcon(iconConfig) {
    this._processIcon({ options: { icon: iconConfig } });
    if (iconConfig.url) {
      this._cell.setIcon(iconConfig);
      this._updateLabelOffset(); // 图标变化后重新计算标签位置
    }
  }

  /**
   * 启用拖拽
   */
  enableDragging() {
    this.draggable = true;
    this._cell.enableDragging();
  }

  /**
   * 禁用拖拽
   */
  disableDragging() {
    this.draggable = false;
    this._cell.disableDragging();
  }

  /**
   * 设置透明度
   */
  setOpacity(opacity) {
    // 百度地图标记点没有直接的透明度设置，可以通过CSS实现
    const icon = this._cell.getIcon();
    if (icon && icon.imageUrl) {
      // 这里可以根据需要实现透明度设置逻辑
    }
  }

  /**
   * 添加动画效果
   */
  setAnimation(animation) {
    // 百度地图支持的动画类型：BMAP_ANIMATION_DROP, BMAP_ANIMATION_BOUNCE
    if (typeof BMap !== 'undefined' && BMap.Animation) {
      switch (animation) {
        case 'drop':
          this._cell.setAnimation(BMap.Animation.BMAP_ANIMATION_DROP);
          break;
        case 'bounce':
          this._cell.setAnimation(BMap.Animation.BMAP_ANIMATION_BOUNCE);
          break;
        default:
          this._cell.setAnimation(null);
      }
    }
  }

  /**
   * 获取标记点实例
   */
  getMarker() {
    return this._cell;
  }

  /**
   * 导出配置
   */
  exportConfig() {
    const position = this.getPosition();
    return {
      id: this.id,
      name: this.name,
      lng: position.lng,
      lat: position.lat,
      draggable: this.draggable,
      showLabel: this.showLabel,
      extData: this.extData,
      data: this.data,
      options: {
        icon: this._cell.getIcon() ? {
          url: this._cell.getIcon().imageUrl,
          size: this._cell.getIcon().size
        } : null
      }
    };
  }

  /**
   * 销毁标记点
   */
  destroy() {
    if (this.label) {
      this.label = null;
    }

    // 清理事件监听器
    this._cell.removeEventListener("dragend");
    this._cell.removeEventListener("rightclick");

    this._cell = null;
    this.extData = null;
    this.data = null;
  }
}
