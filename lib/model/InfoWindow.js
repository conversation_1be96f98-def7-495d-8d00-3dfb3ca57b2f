/**
 * 信息窗口类
 * 用于在地图上显示信息窗口
 */

export const DEFAULT_CONFIG = {
  confirmText: "确认",
  cancelText: "取消",
  title: "系统提示",
  content: "是否确认执行该操作",
};

export default class InfoWindow {
  constructor(
    dom,
    options = {
      enableCloseOnClick: false,
      offset: new BMap.Size(135, 40),
      width: 1,
      height: 1,
    },
  ) {
    this._infoWindow = this._init(dom, options);
    this._eventHandlers = new Map();
  }

  /**
   * 初始化信息窗口
   * @param {HTMLElement} dom DOM元素
   * @param {Object} options 配置选项
   * @returns {BMap.InfoWindow} 百度地图信息窗口实例
   */
  _init(dom, options = {}) {
    let _dom = dom || this._createDom(options);
    this._dom = _dom;
    return new BMap.InfoWindow(_dom, options);
  }

  /**
   * 创建默认的DOM结构
   * @param {Object} options 配置选项
   * @returns {HTMLElement} DOM元素
   */
  _createDom(options = {}) {
    const config = { ...DEFAULT_CONFIG, ...options };
    
    const wrapper = document.createElement("div");
    wrapper.className = "map-info-window";
    
    const template = `
      <div class="info-window-content">
        <div class="info-window-header">
          <div class="info-window-title">${config.title}</div>
          <button class="info-window-close" type="button">×</button>
        </div>
        <div class="info-window-body">
          <div class="info-window-text">${config.content}</div>
        </div>
        <div class="info-window-footer">
          <button class="info-window-btn info-window-confirm" type="button">
            ${config.confirmText}
          </button>
          <button class="info-window-btn info-window-cancel" type="button">
            ${config.cancelText}
          </button>
        </div>
      </div>
    `;
    
    wrapper.innerHTML = template;
    
    // 添加基础样式
    this._addStyles();
    
    // 获取事件元素
    this._eventDoms = {
      close: wrapper.querySelector(".info-window-close"),
      confirm: wrapper.querySelector(".info-window-confirm"),
      cancel: wrapper.querySelector(".info-window-cancel"),
    };
    
    this._textDoms = {
      title: wrapper.querySelector(".info-window-title"),
      content: wrapper.querySelector(".info-window-text"),
    };
    
    return wrapper;
  }

  /**
   * 添加基础样式
   */
  _addStyles() {
    if (document.getElementById("map-info-window-styles")) return;
    
    const style = document.createElement("style");
    style.id = "map-info-window-styles";
    style.textContent = `
      .map-info-window {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        min-width: 200px;
        max-width: 400px;
      }
      
      .info-window-content {
        padding: 0;
      }
      
      .info-window-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
      }
      
      .info-window-title {
        font-weight: 500;
        font-size: 16px;
        margin: 0;
      }
      
      .info-window-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
      }
      
      .info-window-close:hover {
        color: #333;
      }
      
      .info-window-body {
        padding: 16px;
      }
      
      .info-window-text {
        margin: 0;
      }
      
      .info-window-footer {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      }
      
      .info-window-btn {
        padding: 4px 15px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background: #fff;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
      }
      
      .info-window-btn:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
      
      .info-window-confirm {
        background: #1890ff;
        border-color: #1890ff;
        color: #fff;
      }
      
      .info-window-confirm:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * 添加事件监听器
   * @param {string} type 事件类型 (confirm, cancel, close)
   * @param {Function} handler 事件处理函数
   */
  addEventListener(type, handler) {
    if (!type || !handler) return;
    
    // 移除之前的事件监听器
    this.removeEventListener(type);
    
    const element = this._eventDoms[type === "close" ? "close" : type];
    if (!element) return;
    
    // 为cancel类型同时绑定close按钮
    if (type === "cancel") {
      const closeElement = this._eventDoms.close;
      if (closeElement) {
        closeElement.addEventListener("click", handler);
        this._eventHandlers.set("close", handler);
      }
    }
    
    element.addEventListener("click", handler);
    this._eventHandlers.set(type, handler);
  }

  /**
   * 移除事件监听器
   * @param {string} type 事件类型
   */
  removeEventListener(type) {
    if (!type) return;
    
    const handler = this._eventHandlers.get(type);
    if (!handler) return;
    
    const element = this._eventDoms[type === "close" ? "close" : type];
    if (element) {
      element.removeEventListener("click", handler);
    }
    
    // 为cancel类型同时移除close按钮事件
    if (type === "cancel") {
      const closeElement = this._eventDoms.close;
      const closeHandler = this._eventHandlers.get("close");
      if (closeElement && closeHandler) {
        closeElement.removeEventListener("click", closeHandler);
        this._eventHandlers.delete("close");
      }
    }
    
    this._eventHandlers.delete(type);
  }

  /**
   * 更新标题
   * @param {string} text 标题文本
   */
  updateTitle(text) {
    if (this._textDoms.title) {
      this._textDoms.title.textContent = text;
    }
  }

  /**
   * 更新内容
   * @param {string} text 内容文本
   */
  updateContent(text) {
    if (this._textDoms.content) {
      this._textDoms.content.innerHTML = text;
    }
  }

  /**
   * 获取百度地图信息窗口实例
   * @returns {BMap.InfoWindow} 百度地图信息窗口实例
   */
  getInfoWindow() {
    return this._infoWindow;
  }

  /**
   * 销毁信息窗口
   */
  destroy() {
    // 清理所有事件监听器
    for (const type of this._eventHandlers.keys()) {
      this.removeEventListener(type);
    }
    
    this._eventHandlers.clear();
    this._infoWindow = null;
    this._dom = null;
    this._eventDoms = null;
    this._textDoms = null;
  }
}
