import BaseCell from "./BaseCell";

/**
 * 增强的折线类
 * 支持动画效果、箭头、样式配置等功能
 */
export default class Polyline extends BaseCell {
  constructor(layer, param) {
    super(layer, param);

    // 基础属性
    this.id = param.id || this._generateId();
    this.name = param.name || "未命名折线";
    this.editable = param.editable || false;
    this.pointList = param.pointList || [];
    this.showArrow = param.showArrow || false;
    this.animated = param.animated || false;

    // 扩展数据
    this.extData = param.extData || {};
    this.data = param.data || {};

    // 创建折线
    this._cell = new BMap.Polyline(
      this.pointList.map((p) => new BMap.Point(p.lng, p.lat)),
      param.options || this._getDefaultOptions(),
    );

    // 添加箭头
    if (this.showArrow) {
      this._addArrows();
    }

    // 添加动画
    if (this.animated) {
      this._addAnimation();
    }

    this.initEvent();
    this._initEnhancedEvents();
  }

  /**
   * 生成唯一ID
   */
  _generateId() {
    return 'polyline_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取默认样式选项
   */
  _getDefaultOptions() {
    return {
      strokeColor: "#1890ff",
      strokeWeight: 3,
      strokeOpacity: 0.8,
      strokeStyle: "solid"
    };
  }

  /**
   * 添加箭头
   */
  _addArrows() {
    if (typeof BMap.IconSequence !== 'undefined') {
      const arrow = new BMap.IconSequence(
        new BMap.Symbol(BMap.SymbolShapeType.SHAPE_BACKWARD_OPEN_ARROW, {
          scale: 0.8,
          strokeColor: this._cell.getStrokeColor() || "#1890ff",
          strokeWeight: 2
        }),
        '100%',
        '30px'
      );

      this._cell.setIcons([arrow]);
    }
  }

  /**
   * 添加动画效果
   */
  _addAnimation() {
    // 这里可以实现折线的动画效果
    // 例如：渐现动画、流动动画等
    this._animatePolyline();
  }

  /**
   * 折线动画实现
   */
  _animatePolyline() {
    if (this.pointList.length < 2) return;

    // 创建动画效果：逐段显示折线
    const originalPath = [...this.pointList];
    this._cell.setPath([]); // 先清空路径

    let currentIndex = 0;
    const animateStep = () => {
      if (currentIndex < originalPath.length) {
        const currentPath = originalPath.slice(0, currentIndex + 1);
        this._cell.setPath(currentPath.map(p => new BMap.Point(p.lng, p.lat)));
        currentIndex++;
        setTimeout(animateStep, 200); // 每200ms显示一个点
      }
    };

    animateStep();
  }

  /**
   * 初始化增强事件
   */
  _initEnhancedEvents() {
    // 右键菜单事件
    this._cell.addEventListener("rightclick", (e) => {
      this._onRightClick(e);
    });

    // 双击编辑事件
    this._cell.addEventListener("dblclick", (e) => {
      this._onDoubleClick(e);
    });
  }

  /**
   * 右键点击事件处理
   */
  _onRightClick(e) {
    if (this.onRightClick) {
      this.onRightClick({
        polyline: this,
        position: e.point,
        event: e
      });
    }
  }

  /**
   * 双击事件处理
   */
  _onDoubleClick(e) {
    if (this.editable && this.onDoubleClick) {
      this.onDoubleClick({
        polyline: this,
        position: e.point,
        event: e
      });
    }
  }

  /**
   * 显示折线
   */
  show() {
    this._cell.show();
  }

  /**
   * 隐藏折线
   */
  hide() {
    this._cell.hide();
  }

  /**
   * 设置样式
   */
  setStyle(options) {
    const styleMap = {
      strokeColor: 'setStrokeColor',
      strokeWeight: 'setStrokeWeight',
      strokeOpacity: 'setStrokeOpacity',
      strokeStyle: 'setStrokeStyle'
    };

    Object.keys(options).forEach(key => {
      const method = styleMap[key];
      if (method && this._cell[method]) {
        this._cell[method](options[key]);
      }
    });

    // 如果有箭头，更新箭头颜色
    if (this.showArrow && options.strokeColor) {
      this._updateArrowColor(options.strokeColor);
    }
  }

  /**
   * 更新箭头颜色
   */
  _updateArrowColor(color) {
    if (typeof BMap.IconSequence !== 'undefined') {
      const arrow = new BMap.IconSequence(
        new BMap.Symbol(BMap.SymbolShapeType.SHAPE_BACKWARD_OPEN_ARROW, {
          scale: 0.8,
          strokeColor: color,
          strokeWeight: 2
        }),
        '100%',
        '30px'
      );

      this._cell.setIcons([arrow]);
    }
  }

  /**
   * 获取路径点
   */
  getPath() {
    return this._cell.getPath();
  }

  /**
   * 设置路径点
   */
  setPath(pointList) {
    this.pointList = pointList;
    const bmapPoints = pointList.map(p => new BMap.Point(p.lng, p.lat));
    this._cell.setPath(bmapPoints);
  }

  /**
   * 添加点
   */
  addPoint(lng, lat, index) {
    const point = { lng, lat };

    if (typeof index === 'number') {
      this.pointList.splice(index, 0, point);
    } else {
      this.pointList.push(point);
    }

    this.setPath(this.pointList);
  }

  /**
   * 删除点
   */
  removePoint(index) {
    if (index >= 0 && index < this.pointList.length) {
      this.pointList.splice(index, 1);
      this.setPath(this.pointList);
    }
  }

  /**
   * 更新点位置
   */
  updatePoint(index, lng, lat) {
    if (index >= 0 && index < this.pointList.length) {
      this.pointList[index] = { lng, lat };
      this.setPath(this.pointList);
    }
  }

  /**
   * 计算折线长度（米）
   */
  getLength() {
    const path = this.getPath();
    if (path.length < 2) return 0;

    let length = 0;
    for (let i = 0; i < path.length - 1; i++) {
      length += this._getDistance(path[i], path[i + 1]);
    }

    return length;
  }

  /**
   * 计算两点间距离（米）
   */
  _getDistance(point1, point2) {
    if (typeof BMapLib !== 'undefined' && BMapLib.GeoUtils) {
      return BMapLib.GeoUtils.getDistance(point1, point2);
    }

    // 简单的距离计算
    const R = 6371000; // 地球半径（米）
    const lat1 = point1.lat * Math.PI / 180;
    const lat2 = point2.lat * Math.PI / 180;
    const deltaLat = (point2.lat - point1.lat) * Math.PI / 180;
    const deltaLng = (point2.lng - point1.lng) * Math.PI / 180;

    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * 启用/禁用箭头
   */
  toggleArrow(show = !this.showArrow) {
    this.showArrow = show;
    if (show) {
      this._addArrows();
    } else {
      this._cell.setIcons([]);
    }
  }

  /**
   * 重新播放动画
   */
  replayAnimation() {
    if (this.animated) {
      this._animatePolyline();
    }
  }

  /**
   * 获取折线实例
   */
  getPolyline() {
    return this._cell;
  }

  /**
   * 导出配置
   */
  exportConfig() {
    return {
      id: this.id,
      name: this.name,
      pointList: this.pointList,
      editable: this.editable,
      showArrow: this.showArrow,
      animated: this.animated,
      extData: this.extData,
      data: this.data,
      options: {
        strokeColor: this._cell.getStrokeColor(),
        strokeWeight: this._cell.getStrokeWeight(),
        strokeOpacity: this._cell.getStrokeOpacity(),
        strokeStyle: this._cell.getStrokeStyle()
      }
    };
  }

  /**
   * 销毁折线
   */
  destroy() {
    // 清理事件监听器
    this._cell.removeEventListener("rightclick");
    this._cell.removeEventListener("dblclick");

    this._cell = null;
    this.pointList = null;
    this.extData = null;
    this.data = null;
  }
}
