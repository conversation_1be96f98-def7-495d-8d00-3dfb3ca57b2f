<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI组件系统测试</title>
    <link rel="stylesheet" href="./components/components.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .demo-item {
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .demo-item h4 {
            margin: 0 0 15px 0;
            color: #666;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .controls button:hover {
            background: #40a9ff;
        }
        
        .controls button.danger {
            background: #ff4d4f;
        }
        
        .controls button.success {
            background: #52c41a;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #fff7e6;
            border-radius: 6px;
            border-left: 4px solid #fa8c16;
        }
        
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f6ffed;
            border-radius: 6px;
            border-left: 4px solid #52c41a;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .toolbar-demo {
            position: relative;
            height: 200px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .layer-panel-demo {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .time-slider-demo {
            padding: 20px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 UI组件系统测试</h1>
        
        <div class="controls">
            <h3>全局控制</h3>
            <button onclick="switchTheme('light')">浅色主题</button>
            <button onclick="switchTheme('dark')">深色主题</button>
            <button onclick="toggleGlobalDisabled()">切换禁用状态</button>
            <button onclick="showComponentStats()">组件统计</button>
            <button onclick="destroyAllComponents()" class="danger">销毁所有组件</button>
            <button onclick="recreateAllComponents()" class="success">重新创建组件</button>
        </div>
        
        <div class="demo-section">
            <h3>颜色选择器 (ColorPicker)</h3>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>基础颜色选择器</h4>
                    <div id="colorPicker1"></div>
                </div>
                <div class="demo-item">
                    <h4>小尺寸颜色选择器</h4>
                    <div id="colorPicker2"></div>
                </div>
                <div class="demo-item">
                    <h4>无预设颜色选择器</h4>
                    <div id="colorPicker3"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>图标选择器 (IconSelector)</h3>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>基础图标选择器</h4>
                    <div id="iconSelector1"></div>
                </div>
                <div class="demo-item">
                    <h4>大尺寸图标选择器</h4>
                    <div id="iconSelector2"></div>
                </div>
                <div class="demo-item">
                    <h4>无上传功能图标选择器</h4>
                    <div id="iconSelector3"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>工具栏组件 (Toolbar)</h3>
            <div class="toolbar-demo">
                <div id="toolbar1"></div>
            </div>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>垂直工具栏</h4>
                    <div id="toolbar2" style="height: 200px;"></div>
                </div>
                <div class="demo-item">
                    <h4>小尺寸工具栏</h4>
                    <div id="toolbar3"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>图层控制面板 (LayerPanel)</h3>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>基础图层面板</h4>
                    <div class="layer-panel-demo">
                        <div id="layerPanel1"></div>
                    </div>
                </div>
                <div class="demo-item">
                    <h4>可折叠图层面板</h4>
                    <div class="layer-panel-demo">
                        <div id="layerPanel2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>时间滑块组件 (TimeSlider)</h3>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>基础时间滑块</h4>
                    <div class="time-slider-demo">
                        <div id="timeSlider1"></div>
                    </div>
                </div>
                <div class="demo-item">
                    <h4>自动播放时间滑块</h4>
                    <div class="time-slider-demo">
                        <div id="timeSlider2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status" id="status">
            <h3>状态信息</h3>
            <p>等待操作...</p>
        </div>
        
        <div class="log" id="log">
            <h3>操作日志</h3>
        </div>
    </div>

    <script type="module">
        import { 
            componentManager, 
            ColorPicker, 
            IconSelector, 
            LayerPanel, 
            Toolbar, 
            TimeSlider 
        } from './components/index.js';

        let components = new Map();
        let currentTheme = 'light';
        let globalDisabled = false;

        // 日志函数
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logEntry.style.color = type === 'error' ? '#ff4d4f' : type === 'success' ? '#52c41a' : '#333';
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // 更新状态
        function updateStatus(message) {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            status.innerHTML = `
                <h3>状态信息</h3>
                <p><strong>[${timestamp}]</strong> ${message}</p>
            `;
            addLog(message);
        }

        // 创建所有组件
        function createAllComponents() {
            try {
                // 颜色选择器
                const colorPicker1 = componentManager.create('colorPicker', '#colorPicker1', {
                    defaultColor: '#1890ff',
                    onChange: (color) => {
                        updateStatus(`颜色选择器1选择了颜色: ${color}`);
                    }
                });
                componentManager.register('colorPicker1', colorPicker1);

                const colorPicker2 = componentManager.create('colorPicker', '#colorPicker2', {
                    size: 'small',
                    defaultColor: '#52c41a',
                    onChange: (color) => {
                        updateStatus(`颜色选择器2选择了颜色: ${color}`);
                    }
                });
                componentManager.register('colorPicker2', colorPicker2);

                const colorPicker3 = componentManager.create('colorPicker', '#colorPicker3', {
                    showPresets: false,
                    defaultColor: '#fa8c16',
                    onChange: (color) => {
                        updateStatus(`颜色选择器3选择了颜色: ${color}`);
                    }
                });
                componentManager.register('colorPicker3', colorPicker3);

                // 图标选择器
                const iconSelector1 = componentManager.create('iconSelector', '#iconSelector1', {
                    onChange: (icon) => {
                        updateStatus(`图标选择器1选择了图标: ${icon ? icon.id : '无'}`);
                    }
                });
                componentManager.register('iconSelector1', iconSelector1);

                const iconSelector2 = componentManager.create('iconSelector', '#iconSelector2', {
                    size: 'large',
                    onChange: (icon) => {
                        updateStatus(`图标选择器2选择了图标: ${icon ? icon.id : '无'}`);
                    }
                });
                componentManager.register('iconSelector2', iconSelector2);

                const iconSelector3 = componentManager.create('iconSelector', '#iconSelector3', {
                    showUpload: false,
                    onChange: (icon) => {
                        updateStatus(`图标选择器3选择了图标: ${icon ? icon.id : '无'}`);
                    }
                });
                componentManager.register('iconSelector3', iconSelector3);

                // 工具栏
                const toolbar1 = componentManager.create('toolbar', '#toolbar1', {
                    position: 'top-left',
                    onToolClick: (toolId, tool, activeTool) => {
                        updateStatus(`工具栏1点击了工具: ${toolId}, 当前激活: ${activeTool || '无'}`);
                    }
                });
                componentManager.register('toolbar1', toolbar1);

                const toolbar2 = componentManager.create('toolbar', '#toolbar2', {
                    orientation: 'vertical',
                    position: 'top-left',
                    onToolClick: (toolId, tool, activeTool) => {
                        updateStatus(`工具栏2点击了工具: ${toolId}, 当前激活: ${activeTool || '无'}`);
                    }
                });
                componentManager.register('toolbar2', toolbar2);

                const toolbar3 = componentManager.create('toolbar', '#toolbar3', {
                    size: 'small',
                    position: 'top-left',
                    onToolClick: (toolId, tool, activeTool) => {
                        updateStatus(`工具栏3点击了工具: ${toolId}, 当前激活: ${activeTool || '无'}`);
                    }
                });
                componentManager.register('toolbar3', toolbar3);

                // 图层面板
                const layerPanel1 = componentManager.create('layerPanel', '#layerPanel1', {
                    onLayerChange: (action, layerId, value) => {
                        updateStatus(`图层面板1操作: ${action}, 图层: ${layerId}, 值: ${value}`);
                    }
                });
                componentManager.register('layerPanel1', layerPanel1);

                const layerPanel2 = componentManager.create('layerPanel', '#layerPanel2', {
                    collapsible: true,
                    defaultCollapsed: true,
                    onLayerChange: (action, layerId, value) => {
                        updateStatus(`图层面板2操作: ${action}, 图层: ${layerId}, 值: ${value}`);
                    }
                });
                componentManager.register('layerPanel2', layerPanel2);

                // 时间滑块
                const timeSlider1 = componentManager.create('timeSlider', '#timeSlider1', {
                    onChange: (time) => {
                        updateStatus(`时间滑块1时间变化: ${time.toLocaleString()}`);
                    },
                    onPlay: (time) => {
                        updateStatus(`时间滑块1开始播放: ${time.toLocaleString()}`);
                    },
                    onPause: (time) => {
                        updateStatus(`时间滑块1暂停播放: ${time.toLocaleString()}`);
                    }
                });
                componentManager.register('timeSlider1', timeSlider1);

                const timeSlider2 = componentManager.create('timeSlider', '#timeSlider2', {
                    autoPlay: true,
                    playSpeed: 500,
                    onChange: (time) => {
                        updateStatus(`时间滑块2时间变化: ${time.toLocaleString()}`);
                    }
                });
                componentManager.register('timeSlider2', timeSlider2);

                updateStatus('所有组件创建完成');
            } catch (error) {
                updateStatus(`组件创建失败: ${error.message}`);
                addLog(`错误详情: ${error.stack}`, 'error');
            }
        }

        // 全局函数
        window.switchTheme = function(theme) {
            currentTheme = theme;
            componentManager.setTheme(theme);
            document.documentElement.setAttribute('data-theme', theme);
            updateStatus(`切换到${theme === 'light' ? '浅色' : '深色'}主题`);
        };

        window.toggleGlobalDisabled = function() {
            globalDisabled = !globalDisabled;
            componentManager.updateGlobalConfig({ disabled: globalDisabled });
            updateStatus(`全局${globalDisabled ? '禁用' : '启用'}组件`);
        };

        window.showComponentStats = function() {
            const stats = componentManager.getStats();
            updateStatus(`组件统计: 总数${stats.total}, 当前主题${stats.currentTheme}`);
            addLog(`详细统计: ${JSON.stringify(stats, null, 2)}`, 'info');
        };

        window.destroyAllComponents = function() {
            componentManager.destroyAll();
            updateStatus('所有组件已销毁');
        };

        window.recreateAllComponents = function() {
            createAllComponents();
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('UI组件系统测试页面已加载');
            createAllComponents();
        });
    </script>
</body>
</html>
