<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能优化系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .control-group {
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .control-group h4 {
            margin: 0 0 15px 0;
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 5px;
        }
        
        .control-group button {
            margin: 5px;
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .control-group button:hover {
            background: #40a9ff;
        }
        
        .control-group button.success {
            background: #52c41a;
        }
        
        .control-group button.warning {
            background: #fa8c16;
        }
        
        .control-group button.danger {
            background: #ff4d4f;
        }
        
        .control-group button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .status-panel {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #1890ff;
        }
        
        .metric-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 12px;
        }
        
        .log-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .log-header {
            padding: 15px;
            background: #f0f9ff;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
        }
        
        .log-content {
            height: 300px;
            overflow-y: auto;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-entry.debug { background: #f0f0f0; }
        .log-entry.info { background: #e6f7ff; }
        .log-entry.warn { background: #fff7e6; }
        .log-entry.error { background: #fff2f0; }
        .log-entry.fatal { background: #ffebee; }
        
        .virtual-scroll-demo {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            background: white;
        }
        
        .virtual-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .virtual-item:nth-child(even) {
            background: #fafafa;
        }
        
        .lazy-image {
            width: 100px;
            height: 100px;
            margin: 5px;
            border-radius: 4px;
            transition: opacity 0.3s;
        }
        
        .lazy-image.loaded {
            opacity: 1;
        }
        
        .lazy-image.error {
            background: #ffebee;
            border: 2px solid #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 性能优化系统测试</h1>
        
        <div class="controls">
            <div class="control-group">
                <h4>性能监控</h4>
                <button onclick="startPerformanceMonitoring()">开始监控</button>
                <button onclick="stopPerformanceMonitoring()">停止监控</button>
                <button onclick="recordCustomMetric()">记录自定义指标</button>
                <button onclick="getPerformanceReport()">获取性能报告</button>
            </div>
            
            <div class="control-group">
                <h4>组件缓存</h4>
                <button onclick="testComponentCache()">测试组件缓存</button>
                <button onclick="getCacheStats()">缓存统计</button>
                <button onclick="clearComponentCache()">清空缓存</button>
                <button onclick="stressCacheTest()">压力测试</button>
            </div>
            
            <div class="control-group">
                <h4>懒加载测试</h4>
                <button onclick="testModuleLazyLoad()">模块懒加载</button>
                <button onclick="testScriptLazyLoad()">脚本懒加载</button>
                <button onclick="testImageLazyLoad()">图片懒加载</button>
                <button onclick="testPreload()">预加载测试</button>
            </div>
            
            <div class="control-group">
                <h4>资源管理</h4>
                <button onclick="testResourceManager()">资源管理测试</button>
                <button onclick="getResourceStats()">资源统计</button>
                <button onclick="checkResourceLeaks()">检查泄漏</button>
                <button onclick="cleanupResources()">清理资源</button>
            </div>
            
            <div class="control-group">
                <h4>错误处理</h4>
                <button onclick="testErrorHandling()">错误处理测试</button>
                <button onclick="triggerError()">触发错误</button>
                <button onclick="getErrorStats()">错误统计</button>
                <button onclick="clearErrors()">清空错误</button>
            </div>
            
            <div class="control-group">
                <h4>虚拟滚动</h4>
                <button onclick="initVirtualScroll()">初始化虚拟滚动</button>
                <button onclick="addVirtualItems()">添加数据项</button>
                <button onclick="scrollToTop()">滚动到顶部</button>
                <button onclick="scrollToBottom()">滚动到底部</button>
            </div>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <h4>性能指标</h4>
                <div class="metric-value" id="performanceMetric">0</div>
                <div class="metric-label">总指标数</div>
            </div>
            
            <div class="metric-card">
                <h4>缓存命中率</h4>
                <div class="metric-value" id="cacheHitRate">0%</div>
                <div class="metric-label">缓存效率</div>
            </div>
            
            <div class="metric-card">
                <h4>内存使用</h4>
                <div class="metric-value" id="memoryUsage">0MB</div>
                <div class="metric-label">JS堆内存</div>
            </div>
            
            <div class="metric-card">
                <h4>错误计数</h4>
                <div class="metric-value" id="errorCount">0</div>
                <div class="metric-label">总错误数</div>
            </div>
        </div>
        
        <div class="status-panel">
            <h3>系统状态</h3>
            <div id="systemStatus">等待操作...</div>
        </div>
        
        <div class="virtual-scroll-demo" id="virtualScrollDemo" style="display: none;">
            <div id="virtualScrollContent"></div>
        </div>
        
        <div id="lazyImageContainer" style="display: none;">
            <h3>懒加载图片测试</h3>
            <div id="imageGrid"></div>
        </div>
        
        <div class="log-panel">
            <div class="log-header">
                系统日志
                <button onclick="clearLogs()" style="float: right; background: #ff4d4f; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">清空日志</button>
            </div>
            <div class="log-content" id="logContent"></div>
        </div>
    </div>

    <script type="module">
        // 导入性能优化模块
        import { 
            performanceMonitor, 
            componentCache, 
            debounce, 
            throttle, 
            VirtualScrollManager,
            ImageLazyLoader 
        } from './utils/PerformanceOptimizer.js';
        
        import { LazyLoader } from './utils/LazyLoader.js';
        import { globalResourceManager } from './utils/ResourceManager.js';
        import { errorHandler } from './utils/ErrorHandler.js';

        let virtualScrollManager = null;
        let imageLazyLoader = null;
        let testComponentCount = 0;
        let cacheHits = 0;
        let cacheMisses = 0;

        // 日志系统
        function addLog(message, level = 'info') {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logContent').innerHTML = '';
        }

        // 更新指标显示
        function updateMetrics() {
            try {
                // 性能指标
                const performanceReport = performanceMonitor.getPerformanceReport();
                document.getElementById('performanceMetric').textContent = 
                    Object.keys(performanceReport.metrics || {}).length;

                // 缓存命中率
                const total = cacheHits + cacheMisses;
                const hitRate = total > 0 ? Math.round((cacheHits / total) * 100) : 0;
                document.getElementById('cacheHitRate').textContent = `${hitRate}%`;

                // 内存使用
                if (performance.memory) {
                    const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                    document.getElementById('memoryUsage').textContent = `${memoryMB}MB`;
                }

                // 错误计数
                const errorStats = errorHandler.getErrorStats();
                document.getElementById('errorCount').textContent = errorStats.total || 0;
            } catch (error) {
                console.error('Failed to update metrics:', error);
            }
        }

        // 更新系统状态
        function updateStatus(message) {
            document.getElementById('systemStatus').innerHTML = `
                <strong>[${new Date().toLocaleTimeString()}]</strong> ${message}
            `;
            addLog(message);
        }

        // 性能监控测试
        window.startPerformanceMonitoring = function() {
            performanceMonitor.start();
            updateStatus('性能监控已启动');
        };

        window.stopPerformanceMonitoring = function() {
            performanceMonitor.stop();
            updateStatus('性能监控已停止');
        };

        window.recordCustomMetric = function() {
            const value = Math.random() * 100;
            performanceMonitor.recordMetric('test.custom', value, { source: 'manual' });
            updateStatus(`记录自定义指标: ${value.toFixed(2)}`);
        };

        window.getPerformanceReport = function() {
            const report = performanceMonitor.getPerformanceReport();
            addLog(`性能报告: ${JSON.stringify(report, null, 2)}`, 'info');
            updateStatus('性能报告已生成');
        };

        // 组件缓存测试
        window.testComponentCache = function() {
            const componentId = `component_${++testComponentCount}`;
            const component = { id: componentId, data: `Test data ${testComponentCount}` };
            
            // 先尝试从缓存获取
            const cached = componentCache.get(componentId);
            if (cached) {
                cacheHits++;
                updateStatus(`缓存命中: ${componentId}`);
            } else {
                cacheMisses++;
                componentCache.set(componentId, component);
                updateStatus(`缓存未命中，已缓存: ${componentId}`);
            }
        };

        window.getCacheStats = function() {
            const stats = componentCache.getStats();
            addLog(`缓存统计: ${JSON.stringify(stats, null, 2)}`, 'info');
            updateStatus('缓存统计已获取');
        };

        window.clearComponentCache = function() {
            componentCache.clear();
            cacheHits = 0;
            cacheMisses = 0;
            updateStatus('组件缓存已清空');
        };

        window.stressCacheTest = function() {
            updateStatus('开始缓存压力测试...');
            for (let i = 0; i < 100; i++) {
                setTimeout(() => {
                    window.testComponentCache();
                    if (i === 99) {
                        updateStatus('缓存压力测试完成');
                    }
                }, i * 10);
            }
        };

        // 懒加载测试
        window.testModuleLazyLoad = async function() {
            try {
                updateStatus('开始模块懒加载测试...');
                // 这里可以测试动态导入
                const module = await LazyLoader.module('./utils/event-bus.js');
                updateStatus('模块懒加载成功');
                addLog(`加载的模块: ${Object.keys(module)}`, 'success');
            } catch (error) {
                updateStatus('模块懒加载失败');
                addLog(`错误: ${error.message}`, 'error');
            }
        };

        window.testScriptLazyLoad = async function() {
            try {
                updateStatus('开始脚本懒加载测试...');
                await LazyLoader.script('https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js');
                updateStatus('脚本懒加载成功');
                addLog('Lodash 库已加载', 'success');
            } catch (error) {
                updateStatus('脚本懒加载失败');
                addLog(`错误: ${error.message}`, 'error');
            }
        };

        window.testImageLazyLoad = function() {
            const container = document.getElementById('lazyImageContainer');
            const imageGrid = document.getElementById('imageGrid');
            
            container.style.display = 'block';
            imageGrid.innerHTML = '';
            
            if (!imageLazyLoader) {
                imageLazyLoader = new ImageLazyLoader();
            }
            
            // 创建测试图片
            for (let i = 1; i <= 20; i++) {
                const img = document.createElement('img');
                img.className = 'lazy-image';
                img.dataset.src = `https://picsum.photos/100/100?random=${i}`;
                img.alt = `Test image ${i}`;
                
                imageLazyLoader.observe(img);
                imageGrid.appendChild(img);
            }
            
            updateStatus('图片懒加载测试已初始化');
        };

        window.testPreload = async function() {
            try {
                updateStatus('开始预加载测试...');
                const resources = [
                    { type: 'script', path: 'https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js' }
                ];
                
                await LazyLoader.preload(resources);
                updateStatus('预加载测试完成');
            } catch (error) {
                updateStatus('预加载测试失败');
                addLog(`错误: ${error.message}`, 'error');
            }
        };

        // 资源管理测试
        window.testResourceManager = function() {
            const resourceId = `test_resource_${Date.now()}`;
            const resource = { name: 'Test Resource', data: 'Some data' };
            
            globalResourceManager.register(resourceId, resource, (res) => {
                addLog(`清理资源: ${res.name}`, 'info');
            });
            
            updateStatus(`资源已注册: ${resourceId}`);
        };

        window.getResourceStats = function() {
            const stats = globalResourceManager.getStats();
            addLog(`资源统计: ${JSON.stringify(stats, null, 2)}`, 'info');
            updateStatus('资源统计已获取');
        };

        window.checkResourceLeaks = function() {
            const leakCheck = globalResourceManager.checkLeaks();
            addLog(`泄漏检查: ${JSON.stringify(leakCheck, null, 2)}`, leakCheck.hasLeaks ? 'warn' : 'info');
            updateStatus(leakCheck.hasLeaks ? '发现潜在泄漏' : '未发现泄漏');
        };

        window.cleanupResources = function() {
            globalResourceManager.clearAll();
            updateStatus('所有资源已清理');
        };

        // 错误处理测试
        window.testErrorHandling = function() {
            errorHandler.info('这是一条信息日志');
            errorHandler.warn('这是一条警告日志');
            errorHandler.error('这是一条错误日志');
            updateStatus('错误处理测试完成');
        };

        window.triggerError = function() {
            try {
                throw new Error('这是一个测试错误');
            } catch (error) {
                errorHandler.error(error, { source: 'manual_trigger' });
                updateStatus('已触发测试错误');
            }
        };

        window.getErrorStats = function() {
            const stats = errorHandler.getErrorStats();
            addLog(`错误统计: ${JSON.stringify(stats, null, 2)}`, 'info');
            updateStatus('错误统计已获取');
        };

        window.clearErrors = function() {
            errorHandler.clearErrors();
            updateStatus('错误记录已清空');
        };

        // 虚拟滚动测试
        window.initVirtualScroll = function() {
            const container = document.getElementById('virtualScrollDemo');
            container.style.display = 'block';
            
            virtualScrollManager = new VirtualScrollManager({
                itemHeight: 50,
                containerHeight: 400,
                items: []
            });
            
            updateStatus('虚拟滚动已初始化');
        };

        window.addVirtualItems = function() {
            if (!virtualScrollManager) {
                updateStatus('请先初始化虚拟滚动');
                return;
            }
            
            const items = [];
            for (let i = 0; i < 10000; i++) {
                items.push({ id: i, text: `Item ${i}`, value: Math.random() });
            }
            
            virtualScrollManager.setItems(items);
            renderVirtualItems();
            updateStatus('已添加10000个虚拟项目');
        };

        function renderVirtualItems() {
            if (!virtualScrollManager) return;
            
            const content = document.getElementById('virtualScrollContent');
            const visibleItems = virtualScrollManager.getVisibleItems();
            
            content.innerHTML = '';
            content.style.height = `${virtualScrollManager.totalHeight}px`;
            
            visibleItems.forEach(({ item, index, top }) => {
                const div = document.createElement('div');
                div.className = 'virtual-item';
                div.style.position = 'absolute';
                div.style.top = `${top}px`;
                div.style.width = '100%';
                div.style.height = `${virtualScrollManager.itemHeight}px`;
                div.innerHTML = `#${index}: ${item.text} (${item.value.toFixed(3)})`;
                content.appendChild(div);
            });
        }

        window.scrollToTop = function() {
            if (virtualScrollManager) {
                virtualScrollManager.updateScrollTop(0);
                renderVirtualItems();
                updateStatus('已滚动到顶部');
            }
        };

        window.scrollToBottom = function() {
            if (virtualScrollManager) {
                virtualScrollManager.updateScrollTop(virtualScrollManager.totalHeight);
                renderVirtualItems();
                updateStatus('已滚动到底部');
            }
        };

        // 定期更新指标
        setInterval(updateMetrics, 1000);

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('性能优化系统测试页面已加载');
            
            // 启动性能监控
            performanceMonitor.start();
            
            // 添加自定义日志记录器
            errorHandler.addLogger((errorInfo) => {
                addLog(`[${errorInfo.level.toUpperCase()}] ${errorInfo.message}`, errorInfo.level);
            });
            
            updateMetrics();
        });
    </script>
</body>
</html>
