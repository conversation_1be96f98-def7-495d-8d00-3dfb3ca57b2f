<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强绘图工具测试</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js"></script>
    <link rel="stylesheet" href="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .control-group {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        
        .control-group h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .control-group button {
            margin: 5px;
            padding: 6px 12px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .control-group button:hover {
            background: #40a9ff;
        }
        
        .control-group button.active {
            background: #52c41a;
        }
        
        .control-group button.danger {
            background: #ff4d4f;
        }
        
        .control-group button.danger:hover {
            background: #ff7875;
        }
        
        .control-group button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        #mapContainer {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f6ffed;
            border-radius: 8px;
            border-left: 4px solid #52c41a;
        }
        
        .keyboard-shortcuts {
            margin-top: 20px;
            padding: 15px;
            background: #fff7e6;
            border-radius: 8px;
            border-left: 4px solid #fa8c16;
        }
        
        .keyboard-shortcuts h4 {
            margin: 0 0 10px 0;
        }
        
        .keyboard-shortcuts ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>增强绘图工具测试</h1>
        
        <div class="controls">
            <div class="control-group">
                <h4>绘制工具</h4>
                <button onclick="startDrawing('marker')">绘制标记点</button>
                <button onclick="startDrawing('polygon')">绘制多边形</button>
                <button onclick="startDrawing('polyline')">绘制折线</button>
                <button onclick="startDrawing('circle')">绘制圆形</button>
                <button onclick="startDrawing('rectangle')">绘制矩形</button>
                <button onclick="stopDrawing()" class="danger">停止绘制</button>
            </div>
            
            <div class="control-group">
                <h4>编辑功能</h4>
                <button onclick="enterEditMode()" id="editBtn">进入编辑模式</button>
                <button onclick="exitEditMode()">退出编辑模式</button>
                <button onclick="deleteCurrentOverlay()" class="danger">删除当前覆盖物</button>
                <button onclick="clearAllOverlays()" class="danger">清空所有</button>
            </div>
            
            <div class="control-group">
                <h4>撤销重做</h4>
                <button onclick="undo()" id="undoBtn">撤销 (Ctrl+Z)</button>
                <button onclick="redo()" id="redoBtn">重做 (Ctrl+Y)</button>
                <button onclick="clearHistory()">清空历史</button>
                <button onclick="showHistoryInfo()">历史信息</button>
            </div>
            
            <div class="control-group">
                <h4>辅助功能</h4>
                <button onclick="toggleSnap()" id="snapBtn">吸附功能</button>
                <button onclick="toggleMeasurement()" id="measureBtn">测量显示</button>
                <button onclick="toggleGrid()" id="gridBtn">网格显示</button>
                <button onclick="updateDrawingStyle()">更新样式</button>
            </div>
            
            <div class="control-group">
                <h4>数据操作</h4>
                <button onclick="exportData('json')">导出JSON</button>
                <button onclick="exportData('geojson')">导出GeoJSON</button>
                <button onclick="showStatistics()">显示统计</button>
                <button onclick="showAllData()">显示所有数据</button>
            </div>
        </div>
        
        <div id="mapContainer"></div>
        
        <div class="keyboard-shortcuts">
            <h4>键盘快捷键</h4>
            <ul>
                <li><strong>Ctrl+Z</strong> - 撤销上一步操作</li>
                <li><strong>Ctrl+Y</strong> 或 <strong>Ctrl+Shift+Z</strong> - 重做操作</li>
                <li><strong>Escape</strong> - 退出编辑模式</li>
                <li><strong>Delete</strong> - 删除选中的覆盖物</li>
            </ul>
        </div>
        
        <div class="status" id="status">
            <h3>状态信息</h3>
            <p>等待操作...</p>
        </div>
        
        <div class="info-panel" id="infoPanel" style="display: none;">
            <h3>详细信息</h3>
            <div id="infoContent"></div>
        </div>
    </div>

    <script type="module">
        let map = null;
        let drawingTool = null;
        
        // 初始化地图
        function initMap() {
            map = new BMap.Map('mapContainer');
            map.centerAndZoom(new BMap.Point(116.404, 39.915), 11);
            map.enableScrollWheelZoom(true);
            
            // 创建增强绘图工具
            drawingTool = new DrawingTool(map, {
                enableEdit: true,
                enableUndo: true,
                enableSnap: false,
                maxHistorySize: 50,
                showMeasurement: true,
                drawCallback: onDrawComplete
            });
            
            updateStatus('地图和绘图工具初始化完成');
            updateButtons();
        }
        
        // 绘制完成回调
        function onDrawComplete(type, data) {
            updateStatus(`绘制完成: ${type}`);
            showInfo('绘制结果', JSON.stringify(data, null, 2));
            updateButtons();
        }
        
        // 更新状态显示
        function updateStatus(message) {
            const statusElement = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusElement.innerHTML = `
                <h3>状态信息</h3>
                <p><strong>[${timestamp}]</strong> ${message}</p>
            `;
        }
        
        // 显示信息面板
        function showInfo(title, content) {
            const infoPanel = document.getElementById('infoPanel');
            const infoContent = document.getElementById('infoContent');
            infoContent.innerHTML = `<h4>${title}</h4><pre>${content}</pre>`;
            infoPanel.style.display = 'block';
        }
        
        // 隐藏信息面板
        function hideInfo() {
            document.getElementById('infoPanel').style.display = 'none';
        }
        
        // 更新按钮状态
        function updateButtons() {
            if (!drawingTool) return;
            
            const historyInfo = drawingTool.getHistoryInfo();
            const statistics = drawingTool.getStatistics();
            
            // 更新撤销重做按钮
            document.getElementById('undoBtn').disabled = !historyInfo.canUndo;
            document.getElementById('redoBtn').disabled = !historyInfo.canRedo;
            
            // 更新编辑按钮
            document.getElementById('editBtn').textContent = 
                statistics.isEditing ? '编辑模式中' : '进入编辑模式';
            document.getElementById('editBtn').className = 
                statistics.isEditing ? 'active' : '';
            
            // 更新辅助功能按钮
            document.getElementById('snapBtn').className = 
                statistics.features.enableSnap ? 'active' : '';
            document.getElementById('measureBtn').className = 
                statistics.features.showMeasurement ? 'active' : '';
            document.getElementById('gridBtn').className = 
                statistics.features.showGrid ? 'active' : '';
        }
        
        // 绘制功能
        window.startDrawing = function(type) {
            if (!drawingTool) return;
            
            const drawingModes = {
                marker: BMAP_DRAWING_MARKER,
                polygon: BMAP_DRAWING_POLYGON,
                polyline: BMAP_DRAWING_POLYLINE,
                circle: BMAP_DRAWING_CIRCLE,
                rectangle: BMAP_DRAWING_RECTANGLE
            };
            
            if (drawingTool._drawingManager) {
                drawingTool._drawingManager.setDrawingMode(drawingModes[type]);
                drawingTool._drawingManager.open();
                updateStatus(`开始绘制: ${type}`);
            }
        };
        
        window.stopDrawing = function() {
            if (drawingTool && drawingTool._drawingManager) {
                drawingTool._drawingManager.close();
                updateStatus('停止绘制');
            }
        };
        
        // 编辑功能
        window.enterEditMode = function() {
            if (!drawingTool) return;
            
            const success = drawingTool.enterEditMode(drawingTool.drawType);
            if (success) {
                updateStatus('进入编辑模式');
            } else {
                updateStatus('无法进入编辑模式（没有可编辑的覆盖物）');
            }
            updateButtons();
        };
        
        window.exitEditMode = function() {
            if (!drawingTool) return;
            
            drawingTool.exitEditMode();
            updateStatus('退出编辑模式');
            updateButtons();
        };
        
        window.deleteCurrentOverlay = function() {
            if (!drawingTool) return;
            
            const success = drawingTool.deleteEditingOverlay();
            if (success) {
                updateStatus('删除当前覆盖物');
            } else {
                updateStatus('没有可删除的覆盖物');
            }
            updateButtons();
        };
        
        window.clearAllOverlays = function() {
            if (!drawingTool) return;
            
            drawingTool.emptyOverlay();
            updateStatus('清空所有覆盖物');
            updateButtons();
            hideInfo();
        };
        
        // 撤销重做功能
        window.undo = function() {
            if (!drawingTool) return;
            
            const success = drawingTool.undo();
            if (success) {
                updateStatus('撤销操作');
            } else {
                updateStatus('无法撤销');
            }
            updateButtons();
        };
        
        window.redo = function() {
            if (!drawingTool) return;
            
            const success = drawingTool.redo();
            if (success) {
                updateStatus('重做操作');
            } else {
                updateStatus('无法重做');
            }
            updateButtons();
        };
        
        window.clearHistory = function() {
            if (!drawingTool) return;
            
            drawingTool.clearHistory();
            updateStatus('清空历史记录');
            updateButtons();
        };
        
        window.showHistoryInfo = function() {
            if (!drawingTool) return;
            
            const historyInfo = drawingTool.getHistoryInfo();
            showInfo('历史记录信息', JSON.stringify(historyInfo, null, 2));
        };
        
        // 辅助功能
        window.toggleSnap = function() {
            if (!drawingTool) return;
            
            const currentState = drawingTool.enableSnap;
            drawingTool.setSnapEnabled(!currentState);
            updateStatus(`吸附功能: ${!currentState ? '开启' : '关闭'}`);
            updateButtons();
        };
        
        window.toggleMeasurement = function() {
            if (!drawingTool) return;
            
            const currentState = drawingTool.showMeasurement;
            drawingTool.setMeasurementEnabled(!currentState);
            updateStatus(`测量显示: ${!currentState ? '开启' : '关闭'}`);
            updateButtons();
        };
        
        window.toggleGrid = function() {
            if (!drawingTool) return;
            
            const currentState = drawingTool.showGrid;
            drawingTool.setGridEnabled(!currentState);
            updateStatus(`网格显示: ${!currentState ? '开启' : '关闭'}`);
            updateButtons();
        };
        
        window.updateDrawingStyle = function() {
            if (!drawingTool) return;
            
            const newStyle = {
                strokeColor: "#ff4d4f",
                fillColor: "#ff7875",
                strokeWeight: 3,
                strokeOpacity: 0.9,
                fillOpacity: 0.4
            };
            
            drawingTool.updateStyle(newStyle);
            updateStatus('更新绘制样式');
        };
        
        // 数据操作
        window.exportData = function(format) {
            if (!drawingTool) return;
            
            try {
                const data = drawingTool.exportOverlays(format);
                showInfo(`导出数据 (${format.toUpperCase()})`, data);
                updateStatus(`导出${format.toUpperCase()}格式数据`);
            } catch (error) {
                updateStatus(`导出失败: ${error.message}`);
            }
        };
        
        window.showStatistics = function() {
            if (!drawingTool) return;
            
            const statistics = drawingTool.getStatistics();
            showInfo('绘图工具统计', JSON.stringify(statistics, null, 2));
        };
        
        window.showAllData = function() {
            if (!drawingTool) return;
            
            const allData = drawingTool.getAllOverlays();
            showInfo('所有覆盖物数据', JSON.stringify(allData, null, 2));
        };
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof BMap !== 'undefined') {
                initMap();
            } else {
                setTimeout(() => {
                    if (typeof BMap !== 'undefined') {
                        initMap();
                    } else {
                        updateStatus('错误：百度地图API加载失败');
                    }
                }, 2000);
            }
        });
    </script>
</body>
</html>
