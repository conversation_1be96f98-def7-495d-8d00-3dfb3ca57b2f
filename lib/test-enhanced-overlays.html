<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强覆盖物功能测试</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_API_KEY"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .control-group {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        
        .control-group h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .control-group button {
            margin: 5px;
            padding: 6px 12px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .control-group button:hover {
            background: #40a9ff;
        }
        
        .control-group button.danger {
            background: #ff4d4f;
        }
        
        .control-group button.danger:hover {
            background: #ff7875;
        }
        
        #mapContainer {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f6ffed;
            border-radius: 8px;
            border-left: 4px solid #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>增强覆盖物功能测试</h1>
        
        <div class="controls">
            <div class="control-group">
                <h4>标记点测试</h4>
                <button onclick="testEnhancedMarker()">创建增强标记点</button>
                <button onclick="testDraggableMarker()">创建可拖拽标记点</button>
                <button onclick="testMarkerAnimation()">标记点动画</button>
                <button onclick="updateMarkerLabel()">更新标签</button>
                <button onclick="clearMarkers()" class="danger">清除标记点</button>
            </div>
            
            <div class="control-group">
                <h4>多边形测试</h4>
                <button onclick="testEnhancedPolygon()">创建增强多边形</button>
                <button onclick="testEditablePolygon()">创建可编辑多边形</button>
                <button onclick="calculatePolygonArea()">计算面积</button>
                <button onclick="testPolygonContains()">点在多边形内测试</button>
                <button onclick="clearPolygons()" class="danger">清除多边形</button>
            </div>
            
            <div class="control-group">
                <h4>折线测试</h4>
                <button onclick="testEnhancedPolyline()">创建增强折线</button>
                <button onclick="testAnimatedPolyline()">创建动画折线</button>
                <button onclick="testArrowPolyline()">创建箭头折线</button>
                <button onclick="calculatePolylineLength()">计算长度</button>
                <button onclick="clearPolylines()" class="danger">清除折线</button>
            </div>
        </div>
        
        <div id="mapContainer"></div>
        
        <div class="status" id="status">
            <h3>测试状态</h3>
            <p>等待测试...</p>
        </div>
        
        <div class="info-panel" id="infoPanel" style="display: none;">
            <h3>覆盖物信息</h3>
            <div id="infoContent"></div>
        </div>
    </div>

    <script type="module">
        let map = null;
        let markers = [];
        let polygons = [];
        let polylines = [];
        
        // 初始化地图
        function initMap() {
            map = new BMap.Map('mapContainer');
            map.centerAndZoom(new BMap.Point(116.404, 39.915), 11);
            map.enableScrollWheelZoom(true);
            updateStatus('地图初始化完成');
        }
        
        // 更新状态显示
        function updateStatus(message) {
            const statusElement = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusElement.innerHTML = `
                <h3>测试状态</h3>
                <p><strong>[${timestamp}]</strong> ${message}</p>
            `;
        }
        
        // 显示信息面板
        function showInfo(title, content) {
            const infoPanel = document.getElementById('infoPanel');
            const infoContent = document.getElementById('infoContent');
            infoContent.innerHTML = `<h4>${title}</h4><pre>${content}</pre>`;
            infoPanel.style.display = 'block';
        }
        
        // 测试增强标记点
        window.testEnhancedMarker = function() {
            const center = map.getCenter();
            const marker = new BMap.Marker(center, {
                icon: new BMap.Icon(
                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTQiIGZpbGw9IiMxODkwZmYiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxjaXJjbGUgY3g9IjE2IiBjeT0iMTYiIHI9IjYiIGZpbGw9IiNmZmYiLz4KPC9zdmc+',
                    new BMap.Size(32, 32)
                )
            });
            
            // 添加标签
            const label = new BMap.Label('增强标记点', {
                offset: new BMap.Size(20, -10)
            });
            label.setStyle({
                color: '#333',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                border: '1px solid #ccc',
                borderRadius: '4px',
                padding: '4px 8px',
                fontSize: '12px'
            });
            marker.setLabel(label);
            
            // 添加事件
            marker.addEventListener('click', function(e) {
                updateStatus('点击了增强标记点');
                showInfo('标记点信息', `
位置: ${e.point.lng.toFixed(6)}, ${e.point.lat.toFixed(6)}
类型: 增强标记点
功能: 支持自定义图标和标签
                `);
            });
            
            map.addOverlay(marker);
            markers.push(marker);
            updateStatus('创建了增强标记点');
        };
        
        // 测试可拖拽标记点
        window.testDraggableMarker = function() {
            const center = map.getCenter();
            const offset = new BMap.Point(center.lng + 0.01, center.lat + 0.01);
            
            const marker = new BMap.Marker(offset);
            marker.enableDragging();
            
            marker.addEventListener('dragend', function(e) {
                updateStatus(`标记点拖拽到: ${e.point.lng.toFixed(6)}, ${e.point.lat.toFixed(6)}`);
            });
            
            map.addOverlay(marker);
            markers.push(marker);
            updateStatus('创建了可拖拽标记点');
        };
        
        // 测试标记点动画
        window.testMarkerAnimation = function() {
            const center = map.getCenter();
            const offset = new BMap.Point(center.lng - 0.01, center.lat + 0.01);
            
            const marker = new BMap.Marker(offset);
            marker.setAnimation(BMAP_ANIMATION_BOUNCE);
            
            map.addOverlay(marker);
            markers.push(marker);
            updateStatus('创建了弹跳动画标记点');
        };
        
        // 更新标记点标签
        window.updateMarkerLabel = function() {
            if (markers.length === 0) {
                updateStatus('没有标记点可以更新');
                return;
            }
            
            const marker = markers[0];
            const newLabel = new BMap.Label('更新后的标签', {
                offset: new BMap.Size(20, -10)
            });
            newLabel.setStyle({
                color: '#ff4d4f',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                border: '1px solid #ff4d4f',
                borderRadius: '4px',
                padding: '4px 8px',
                fontSize: '12px'
            });
            marker.setLabel(newLabel);
            updateStatus('更新了第一个标记点的标签');
        };
        
        // 清除标记点
        window.clearMarkers = function() {
            markers.forEach(marker => map.removeOverlay(marker));
            markers = [];
            updateStatus('清除了所有标记点');
        };
        
        // 测试增强多边形
        window.testEnhancedPolygon = function() {
            const center = map.getCenter();
            const points = [
                new BMap.Point(center.lng - 0.01, center.lat - 0.01),
                new BMap.Point(center.lng + 0.01, center.lat - 0.01),
                new BMap.Point(center.lng + 0.01, center.lat + 0.01),
                new BMap.Point(center.lng - 0.01, center.lat + 0.01)
            ];
            
            const polygon = new BMap.Polygon(points, {
                strokeColor: '#1890ff',
                fillColor: '#bae7ff',
                strokeWeight: 2,
                strokeOpacity: 0.8,
                fillOpacity: 0.3
            });
            
            polygon.addEventListener('click', function(e) {
                updateStatus('点击了多边形');
                showInfo('多边形信息', `
点击位置: ${e.point.lng.toFixed(6)}, ${e.point.lat.toFixed(6)}
顶点数量: ${points.length}
类型: 增强多边形
                `);
            });
            
            map.addOverlay(polygon);
            polygons.push(polygon);
            updateStatus('创建了增强多边形');
        };
        
        // 页面加载完成后初始化地图
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof BMap !== 'undefined') {
                initMap();
            } else {
                setTimeout(() => {
                    if (typeof BMap !== 'undefined') {
                        initMap();
                    } else {
                        updateStatus('错误：百度地图API加载失败');
                    }
                }, 2000);
            }
        });
        
        // 其他测试函数的简化实现
        window.testEditablePolygon = () => updateStatus('可编辑多边形功能需要完整实现');
        window.calculatePolygonArea = () => updateStatus('面积计算功能需要完整实现');
        window.testPolygonContains = () => updateStatus('点在多边形内测试需要完整实现');
        window.clearPolygons = () => {
            polygons.forEach(polygon => map.removeOverlay(polygon));
            polygons = [];
            updateStatus('清除了所有多边形');
        };
        
        window.testEnhancedPolyline = () => updateStatus('增强折线功能需要完整实现');
        window.testAnimatedPolyline = () => updateStatus('动画折线功能需要完整实现');
        window.testArrowPolyline = () => updateStatus('箭头折线功能需要完整实现');
        window.calculatePolylineLength = () => updateStatus('长度计算功能需要完整实现');
        window.clearPolylines = () => {
            polylines.forEach(polyline => map.removeOverlay(polyline));
            polylines = [];
            updateStatus('清除了所有折线');
        };
    </script>
</body>
</html>
