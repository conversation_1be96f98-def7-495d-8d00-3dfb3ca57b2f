/**
 * 图层控制面板组件
 * 提供图层管理功能，包括显示/隐藏、排序、样式配置等
 */

import eventBus from '../utils/event-bus.js';

export default class LayerPanel {
  constructor(container, options = {}) {
    this.container = typeof container === 'string' ? document.querySelector(container) : container;
    this.options = {
      layerSystem: options.layerSystem || null,
      showVisibilityToggle: options.showVisibilityToggle !== false,
      showOpacityControl: options.showOpacityControl !== false,
      showStyleConfig: options.showStyleConfig !== false,
      showLayerOrder: options.showLayerOrder !== false,
      collapsible: options.collapsible !== false,
      defaultCollapsed: options.defaultCollapsed || false,
      maxHeight: options.maxHeight || '400px',
      onLayerChange: options.onLayerChange || null,
      ...options
    };

    this.layers = [];
    this.isCollapsed = this.options.defaultCollapsed;
    this.draggedLayer = null;

    this.init();
  }

  /**
   * 初始化组件
   */
  init() {
    this.createStructure();
    this.bindEvents();
    this.loadLayers();
  }

  /**
   * 创建DOM结构
   */
  createStructure() {
    this.container.className = 'layer-panel';
    
    this.container.innerHTML = `
      <div class="layer-panel-header">
        <div class="layer-panel-title">
          <span class="layer-panel-title-text">图层管理</span>
          ${this.options.collapsible ? '<button class="layer-panel-collapse-btn">−</button>' : ''}
        </div>
        <div class="layer-panel-actions">
          <button class="layer-panel-btn layer-panel-btn-add" title="添加图层">+</button>
          <button class="layer-panel-btn layer-panel-btn-refresh" title="刷新图层">⟳</button>
        </div>
      </div>
      <div class="layer-panel-content ${this.isCollapsed ? 'collapsed' : ''}">
        <div class="layer-panel-list" style="max-height: ${this.options.maxHeight}">
          <div class="layer-panel-empty">暂无图层</div>
        </div>
      </div>
    `;

    // 获取DOM元素引用
    this.header = this.container.querySelector('.layer-panel-header');
    this.content = this.container.querySelector('.layer-panel-content');
    this.list = this.container.querySelector('.layer-panel-list');
    this.collapseBtn = this.container.querySelector('.layer-panel-collapse-btn');
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 折叠/展开事件
    if (this.collapseBtn) {
      this.collapseBtn.addEventListener('click', () => {
        this.toggleCollapse();
      });
    }

    // 按钮事件
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('layer-panel-btn-add')) {
        this.showAddLayerDialog();
      } else if (e.target.classList.contains('layer-panel-btn-refresh')) {
        this.refreshLayers();
      }
    });

    // 图层项事件
    this.list.addEventListener('click', (e) => {
      const layerItem = e.target.closest('.layer-panel-item');
      if (!layerItem) return;

      const layerId = layerItem.dataset.layerId;

      if (e.target.classList.contains('layer-visibility-toggle')) {
        this.toggleLayerVisibility(layerId);
      } else if (e.target.classList.contains('layer-delete-btn')) {
        this.deleteLayer(layerId);
      } else if (e.target.classList.contains('layer-style-btn')) {
        this.showStyleConfig(layerId);
      } else if (e.target.classList.contains('layer-zoom-btn')) {
        this.zoomToLayer(layerId);
      }
    });

    // 透明度滑块事件
    this.list.addEventListener('input', (e) => {
      if (e.target.classList.contains('layer-opacity-slider')) {
        const layerId = e.target.closest('.layer-panel-item').dataset.layerId;
        const opacity = parseFloat(e.target.value);
        this.setLayerOpacity(layerId, opacity);
      }
    });

    // 拖拽排序事件
    if (this.options.showLayerOrder) {
      this.bindDragEvents();
    }

    // 监听图层系统事件
    if (this.options.layerSystem) {
      eventBus.on('layer:added', (data) => this.onLayerAdded(data));
      eventBus.on('layer:removed', (data) => this.onLayerRemoved(data));
      eventBus.on('layer:updated', (data) => this.onLayerUpdated(data));
    }
  }

  /**
   * 绑定拖拽事件
   */
  bindDragEvents() {
    this.list.addEventListener('dragstart', (e) => {
      const layerItem = e.target.closest('.layer-panel-item');
      if (layerItem) {
        this.draggedLayer = layerItem;
        e.dataTransfer.effectAllowed = 'move';
        layerItem.classList.add('dragging');
      }
    });

    this.list.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
      
      const layerItem = e.target.closest('.layer-panel-item');
      if (layerItem && layerItem !== this.draggedLayer) {
        this.showDropIndicator(layerItem, e.clientY);
      }
    });

    this.list.addEventListener('drop', (e) => {
      e.preventDefault();
      const targetItem = e.target.closest('.layer-panel-item');
      
      if (targetItem && this.draggedLayer && targetItem !== this.draggedLayer) {
        this.reorderLayers(this.draggedLayer, targetItem);
      }
      
      this.clearDragState();
    });

    this.list.addEventListener('dragend', () => {
      this.clearDragState();
    });
  }

  /**
   * 加载图层
   */
  loadLayers() {
    if (this.options.layerSystem) {
      this.layers = this.options.layerSystem.getAllLayers();
    }
    this.renderLayers();
  }

  /**
   * 渲染图层列表
   */
  renderLayers() {
    if (this.layers.length === 0) {
      this.list.innerHTML = '<div class="layer-panel-empty">暂无图层</div>';
      return;
    }

    const layersHTML = this.layers.map(layer => this.createLayerItemHTML(layer)).join('');
    this.list.innerHTML = layersHTML;
  }

  /**
   * 创建图层项HTML
   */
  createLayerItemHTML(layer) {
    const isVisible = layer.visible !== false;
    const opacity = layer.opacity || 1;
    
    return `
      <div class="layer-panel-item" 
           data-layer-id="${layer.id}" 
           ${this.options.showLayerOrder ? 'draggable="true"' : ''}>
        <div class="layer-panel-item-header">
          <div class="layer-panel-item-info">
            ${this.options.showLayerOrder ? '<div class="layer-drag-handle">⋮⋮</div>' : ''}
            ${this.options.showVisibilityToggle ? 
              `<button class="layer-visibility-toggle ${isVisible ? 'visible' : 'hidden'}" 
                       title="${isVisible ? '隐藏图层' : '显示图层'}">
                 ${isVisible ? '👁' : '👁‍🗨'}
               </button>` : ''}
            <div class="layer-info">
              <div class="layer-name">${layer.name || layer.id}</div>
              <div class="layer-type">${layer.type || 'unknown'}</div>
            </div>
          </div>
          <div class="layer-panel-item-actions">
            ${this.options.showStyleConfig ? '<button class="layer-style-btn" title="样式配置">🎨</button>' : ''}
            <button class="layer-zoom-btn" title="缩放到图层">🔍</button>
            <button class="layer-delete-btn" title="删除图层">🗑</button>
          </div>
        </div>
        ${this.options.showOpacityControl ? this.createOpacityControlHTML(layer) : ''}
        <div class="layer-panel-item-stats">
          <span class="layer-stat">要素: ${layer.featureCount || 0}</span>
          <span class="layer-stat">状态: ${isVisible ? '显示' : '隐藏'}</span>
        </div>
      </div>
    `;
  }

  /**
   * 创建透明度控制HTML
   */
  createOpacityControlHTML(layer) {
    const opacity = layer.opacity || 1;
    return `
      <div class="layer-opacity-control">
        <label class="layer-opacity-label">透明度: ${Math.round(opacity * 100)}%</label>
        <input type="range" 
               class="layer-opacity-slider" 
               min="0" 
               max="1" 
               step="0.1" 
               value="${opacity}">
      </div>
    `;
  }

  /**
   * 切换折叠状态
   */
  toggleCollapse() {
    this.isCollapsed = !this.isCollapsed;
    this.content.classList.toggle('collapsed', this.isCollapsed);
    this.collapseBtn.textContent = this.isCollapsed ? '+' : '−';
    
    eventBus.emit('layerPanel:collapse', { 
      panel: this, 
      collapsed: this.isCollapsed 
    });
  }

  /**
   * 切换图层可见性
   */
  toggleLayerVisibility(layerId) {
    if (this.options.layerSystem) {
      const layer = this.options.layerSystem.getLayer(layerId);
      if (layer) {
        const newVisibility = !layer.visible;
        this.options.layerSystem.setLayerVisibility(layerId, newVisibility);
        this.updateLayerItem(layerId);
        
        if (this.options.onLayerChange) {
          this.options.onLayerChange('visibility', layerId, newVisibility);
        }
      }
    }
  }

  /**
   * 设置图层透明度
   */
  setLayerOpacity(layerId, opacity) {
    if (this.options.layerSystem) {
      this.options.layerSystem.setLayerOpacity(layerId, opacity);
      this.updateOpacityDisplay(layerId, opacity);
      
      if (this.options.onLayerChange) {
        this.options.onLayerChange('opacity', layerId, opacity);
      }
    }
  }

  /**
   * 更新透明度显示
   */
  updateOpacityDisplay(layerId, opacity) {
    const layerItem = this.list.querySelector(`[data-layer-id="${layerId}"]`);
    if (layerItem) {
      const label = layerItem.querySelector('.layer-opacity-label');
      if (label) {
        label.textContent = `透明度: ${Math.round(opacity * 100)}%`;
      }
    }
  }

  /**
   * 删除图层
   */
  deleteLayer(layerId) {
    if (confirm('确定要删除这个图层吗？')) {
      if (this.options.layerSystem) {
        this.options.layerSystem.removeLayer(layerId);
      }
      
      if (this.options.onLayerChange) {
        this.options.onLayerChange('delete', layerId);
      }
    }
  }

  /**
   * 显示样式配置
   */
  showStyleConfig(layerId) {
    eventBus.emit('layerPanel:showStyleConfig', { 
      panel: this, 
      layerId 
    });
  }

  /**
   * 缩放到图层
   */
  zoomToLayer(layerId) {
    if (this.options.layerSystem) {
      this.options.layerSystem.zoomToLayer(layerId);
    }
    
    eventBus.emit('layerPanel:zoomToLayer', { 
      panel: this, 
      layerId 
    });
  }

  /**
   * 显示添加图层对话框
   */
  showAddLayerDialog() {
    eventBus.emit('layerPanel:showAddDialog', { panel: this });
  }

  /**
   * 刷新图层
   */
  refreshLayers() {
    this.loadLayers();
    eventBus.emit('layerPanel:refresh', { panel: this });
  }

  /**
   * 重新排序图层
   */
  reorderLayers(draggedItem, targetItem) {
    const draggedId = draggedItem.dataset.layerId;
    const targetId = targetItem.dataset.layerId;
    
    if (this.options.layerSystem) {
      this.options.layerSystem.reorderLayer(draggedId, targetId);
    }
    
    this.loadLayers();
    
    if (this.options.onLayerChange) {
      this.options.onLayerChange('reorder', draggedId, targetId);
    }
  }

  /**
   * 显示拖拽指示器
   */
  showDropIndicator(targetItem, clientY) {
    const rect = targetItem.getBoundingClientRect();
    const isAbove = clientY < rect.top + rect.height / 2;
    
    // 移除之前的指示器
    this.list.querySelectorAll('.drop-indicator').forEach(el => el.remove());
    
    // 添加新的指示器
    const indicator = document.createElement('div');
    indicator.className = 'drop-indicator';
    
    if (isAbove) {
      targetItem.parentNode.insertBefore(indicator, targetItem);
    } else {
      targetItem.parentNode.insertBefore(indicator, targetItem.nextSibling);
    }
  }

  /**
   * 清除拖拽状态
   */
  clearDragState() {
    if (this.draggedLayer) {
      this.draggedLayer.classList.remove('dragging');
      this.draggedLayer = null;
    }
    
    this.list.querySelectorAll('.drop-indicator').forEach(el => el.remove());
  }

  /**
   * 更新图层项
   */
  updateLayerItem(layerId) {
    const layer = this.layers.find(l => l.id === layerId);
    if (layer) {
      const layerItem = this.list.querySelector(`[data-layer-id="${layerId}"]`);
      if (layerItem) {
        layerItem.outerHTML = this.createLayerItemHTML(layer);
      }
    }
  }

  /**
   * 图层添加事件处理
   */
  onLayerAdded(data) {
    this.loadLayers();
  }

  /**
   * 图层移除事件处理
   */
  onLayerRemoved(data) {
    this.loadLayers();
  }

  /**
   * 图层更新事件处理
   */
  onLayerUpdated(data) {
    this.updateLayerItem(data.layerId);
  }

  /**
   * 获取所有图层
   */
  getLayers() {
    return this.layers;
  }

  /**
   * 设置图层系统
   */
  setLayerSystem(layerSystem) {
    this.options.layerSystem = layerSystem;
    this.loadLayers();
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.container.innerHTML = '';
    this.container.className = '';
    
    // 移除事件监听
    eventBus.off('layer:added', this.onLayerAdded);
    eventBus.off('layer:removed', this.onLayerRemoved);
    eventBus.off('layer:updated', this.onLayerUpdated);
  }
}
