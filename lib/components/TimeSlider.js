/**
 * 时间滑块组件
 * 提供时间轴控制功能，支持时间范围选择、播放控制等
 */

import eventBus from '../utils/event-bus.js';

export default class TimeSlider {
  constructor(container, options = {}) {
    this.container = typeof container === 'string' ? document.querySelector(container) : container;
    this.options = {
      startTime: options.startTime || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
      endTime: options.endTime || new Date(), // 现在
      currentTime: options.currentTime || null,
      step: options.step || 60 * 60 * 1000, // 1小时步长
      autoPlay: options.autoPlay || false,
      playSpeed: options.playSpeed || 1000, // 播放速度（毫秒）
      showPlayControls: options.showPlayControls !== false,
      showTimeDisplay: options.showTimeDisplay !== false,
      showSpeedControl: options.showSpeedControl !== false,
      timeFormat: options.timeFormat || 'YYYY-MM-DD HH:mm',
      disabled: options.disabled || false,
      onChange: options.onChange || null,
      onPlay: options.onPlay || null,
      onPause: options.onPause || null,
      ...options
    };

    this.currentTime = this.options.currentTime || this.options.startTime;
    this.isPlaying = false;
    this.playTimer = null;
    this.isDragging = false;

    this.init();
  }

  /**
   * 初始化组件
   */
  init() {
    this.createStructure();
    this.bindEvents();
    this.updateDisplay();
    
    if (this.options.autoPlay) {
      this.play();
    }
  }

  /**
   * 创建DOM结构
   */
  createStructure() {
    this.container.className = `time-slider ${this.options.disabled ? 'disabled' : ''}`;
    
    this.container.innerHTML = `
      <div class="time-slider-header">
        ${this.options.showPlayControls ? this.createPlayControlsHTML() : ''}
        ${this.options.showTimeDisplay ? this.createTimeDisplayHTML() : ''}
      </div>
      <div class="time-slider-track-container">
        <div class="time-slider-track">
          <div class="time-slider-progress"></div>
          <div class="time-slider-handle"></div>
        </div>
        <div class="time-slider-ticks">
          ${this.createTicksHTML()}
        </div>
      </div>
      ${this.options.showSpeedControl ? this.createSpeedControlHTML() : ''}
    `;

    // 获取DOM元素引用
    this.track = this.container.querySelector('.time-slider-track');
    this.progress = this.container.querySelector('.time-slider-progress');
    this.handle = this.container.querySelector('.time-slider-handle');
    this.playBtn = this.container.querySelector('.time-slider-play-btn');
    this.timeDisplay = this.container.querySelector('.time-slider-time-display');
    this.speedControl = this.container.querySelector('.time-slider-speed-control');
  }

  /**
   * 创建播放控制HTML
   */
  createPlayControlsHTML() {
    return `
      <div class="time-slider-play-controls">
        <button class="time-slider-btn time-slider-prev-btn" title="上一步">⏮</button>
        <button class="time-slider-btn time-slider-play-btn" title="播放/暂停">▶</button>
        <button class="time-slider-btn time-slider-next-btn" title="下一步">⏭</button>
        <button class="time-slider-btn time-slider-reset-btn" title="重置">⏹</button>
      </div>
    `;
  }

  /**
   * 创建时间显示HTML
   */
  createTimeDisplayHTML() {
    return `
      <div class="time-slider-time-display">
        <div class="time-slider-current-time">${this.formatTime(this.currentTime)}</div>
        <div class="time-slider-time-range">
          ${this.formatTime(this.options.startTime)} - ${this.formatTime(this.options.endTime)}
        </div>
      </div>
    `;
  }

  /**
   * 创建刻度HTML
   */
  createTicksHTML() {
    const totalDuration = this.options.endTime.getTime() - this.options.startTime.getTime();
    const tickCount = Math.min(10, Math.floor(totalDuration / (24 * 60 * 60 * 1000))); // 最多10个刻度
    
    let ticksHTML = '';
    for (let i = 0; i <= tickCount; i++) {
      const tickTime = new Date(this.options.startTime.getTime() + (totalDuration * i / tickCount));
      const position = (i / tickCount) * 100;
      
      ticksHTML += `
        <div class="time-slider-tick" style="left: ${position}%">
          <div class="time-slider-tick-mark"></div>
          <div class="time-slider-tick-label">${this.formatTime(tickTime, 'MM-DD')}</div>
        </div>
      `;
    }
    
    return ticksHTML;
  }

  /**
   * 创建速度控制HTML
   */
  createSpeedControlHTML() {
    return `
      <div class="time-slider-speed-control">
        <label>播放速度:</label>
        <select class="time-slider-speed-select">
          <option value="500">2x</option>
          <option value="1000" selected>1x</option>
          <option value="2000">0.5x</option>
          <option value="5000">0.2x</option>
        </select>
      </div>
    `;
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (this.options.disabled) return;

    // 播放控制事件
    if (this.options.showPlayControls) {
      this.container.addEventListener('click', (e) => {
        if (e.target.classList.contains('time-slider-play-btn')) {
          this.togglePlay();
        } else if (e.target.classList.contains('time-slider-prev-btn')) {
          this.stepBackward();
        } else if (e.target.classList.contains('time-slider-next-btn')) {
          this.stepForward();
        } else if (e.target.classList.contains('time-slider-reset-btn')) {
          this.reset();
        }
      });
    }

    // 滑块拖拽事件
    this.handle.addEventListener('mousedown', (e) => {
      this.startDrag(e);
    });

    // 轨道点击事件
    this.track.addEventListener('click', (e) => {
      if (!this.isDragging) {
        this.handleTrackClick(e);
      }
    });

    // 速度控制事件
    if (this.speedControl) {
      this.speedControl.addEventListener('change', (e) => {
        this.setPlaySpeed(parseInt(e.target.value));
      });
    }

    // 键盘事件
    document.addEventListener('keydown', (e) => {
      if (this.container.contains(document.activeElement)) {
        this.handleKeyboard(e);
      }
    });
  }

  /**
   * 开始拖拽
   */
  startDrag(e) {
    e.preventDefault();
    this.isDragging = true;
    
    const handleMouseMove = (e) => {
      this.updateTimeFromPosition(e.clientX);
    };
    
    const handleMouseUp = () => {
      this.isDragging = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }

  /**
   * 处理轨道点击
   */
  handleTrackClick(e) {
    this.updateTimeFromPosition(e.clientX);
  }

  /**
   * 根据位置更新时间
   */
  updateTimeFromPosition(clientX) {
    const rect = this.track.getBoundingClientRect();
    const position = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    
    const totalDuration = this.options.endTime.getTime() - this.options.startTime.getTime();
    const newTime = new Date(this.options.startTime.getTime() + totalDuration * position);
    
    this.setCurrentTime(newTime);
  }

  /**
   * 处理键盘事件
   */
  handleKeyboard(e) {
    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        this.stepBackward();
        break;
      case 'ArrowRight':
        e.preventDefault();
        this.stepForward();
        break;
      case ' ':
        e.preventDefault();
        this.togglePlay();
        break;
      case 'Home':
        e.preventDefault();
        this.reset();
        break;
      case 'End':
        e.preventDefault();
        this.setCurrentTime(this.options.endTime);
        break;
    }
  }

  /**
   * 设置当前时间
   */
  setCurrentTime(time) {
    // 确保时间在有效范围内
    time = new Date(Math.max(
      this.options.startTime.getTime(),
      Math.min(this.options.endTime.getTime(), time.getTime())
    ));

    this.currentTime = time;
    this.updateDisplay();

    // 触发回调
    if (this.options.onChange) {
      this.options.onChange(this.currentTime);
    }

    // 触发事件
    eventBus.emit('timeSlider:timeChange', {
      slider: this,
      currentTime: this.currentTime
    });
  }

  /**
   * 更新显示
   */
  updateDisplay() {
    const totalDuration = this.options.endTime.getTime() - this.options.startTime.getTime();
    const currentDuration = this.currentTime.getTime() - this.options.startTime.getTime();
    const position = currentDuration / totalDuration;

    // 更新进度条和滑块位置
    this.progress.style.width = `${position * 100}%`;
    this.handle.style.left = `${position * 100}%`;

    // 更新时间显示
    if (this.timeDisplay) {
      const currentTimeElement = this.timeDisplay.querySelector('.time-slider-current-time');
      if (currentTimeElement) {
        currentTimeElement.textContent = this.formatTime(this.currentTime);
      }
    }

    // 更新播放按钮
    if (this.playBtn) {
      this.playBtn.textContent = this.isPlaying ? '⏸' : '▶';
      this.playBtn.title = this.isPlaying ? '暂停' : '播放';
    }
  }

  /**
   * 格式化时间
   */
  formatTime(time, format = null) {
    format = format || this.options.timeFormat;
    
    const year = time.getFullYear();
    const month = String(time.getMonth() + 1).padStart(2, '0');
    const day = String(time.getDate()).padStart(2, '0');
    const hour = String(time.getHours()).padStart(2, '0');
    const minute = String(time.getMinutes()).padStart(2, '0');
    const second = String(time.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second);
  }

  /**
   * 播放
   */
  play() {
    if (this.isPlaying) return;
    
    this.isPlaying = true;
    this.updateDisplay();
    
    this.playTimer = setInterval(() => {
      const nextTime = new Date(this.currentTime.getTime() + this.options.step);
      
      if (nextTime.getTime() >= this.options.endTime.getTime()) {
        this.setCurrentTime(this.options.endTime);
        this.pause();
      } else {
        this.setCurrentTime(nextTime);
      }
    }, this.options.playSpeed);

    if (this.options.onPlay) {
      this.options.onPlay(this.currentTime);
    }

    eventBus.emit('timeSlider:play', {
      slider: this,
      currentTime: this.currentTime
    });
  }

  /**
   * 暂停
   */
  pause() {
    if (!this.isPlaying) return;
    
    this.isPlaying = false;
    this.updateDisplay();
    
    if (this.playTimer) {
      clearInterval(this.playTimer);
      this.playTimer = null;
    }

    if (this.options.onPause) {
      this.options.onPause(this.currentTime);
    }

    eventBus.emit('timeSlider:pause', {
      slider: this,
      currentTime: this.currentTime
    });
  }

  /**
   * 切换播放状态
   */
  togglePlay() {
    if (this.isPlaying) {
      this.pause();
    } else {
      this.play();
    }
  }

  /**
   * 向前一步
   */
  stepForward() {
    const nextTime = new Date(this.currentTime.getTime() + this.options.step);
    this.setCurrentTime(nextTime);
  }

  /**
   * 向后一步
   */
  stepBackward() {
    const prevTime = new Date(this.currentTime.getTime() - this.options.step);
    this.setCurrentTime(prevTime);
  }

  /**
   * 重置到开始时间
   */
  reset() {
    this.pause();
    this.setCurrentTime(this.options.startTime);
  }

  /**
   * 设置播放速度
   */
  setPlaySpeed(speed) {
    this.options.playSpeed = speed;
    
    if (this.isPlaying) {
      this.pause();
      this.play();
    }
  }

  /**
   * 设置时间范围
   */
  setTimeRange(startTime, endTime) {
    this.options.startTime = startTime;
    this.options.endTime = endTime;
    
    // 确保当前时间在新范围内
    if (this.currentTime.getTime() < startTime.getTime()) {
      this.setCurrentTime(startTime);
    } else if (this.currentTime.getTime() > endTime.getTime()) {
      this.setCurrentTime(endTime);
    }
    
    // 重新创建刻度
    const ticksContainer = this.container.querySelector('.time-slider-ticks');
    if (ticksContainer) {
      ticksContainer.innerHTML = this.createTicksHTML();
    }
    
    // 更新时间范围显示
    const timeRangeElement = this.container.querySelector('.time-slider-time-range');
    if (timeRangeElement) {
      timeRangeElement.textContent = `${this.formatTime(startTime)} - ${this.formatTime(endTime)}`;
    }
    
    this.updateDisplay();
  }

  /**
   * 获取当前时间
   */
  getCurrentTime() {
    return this.currentTime;
  }

  /**
   * 获取时间范围
   */
  getTimeRange() {
    return {
      startTime: this.options.startTime,
      endTime: this.options.endTime
    };
  }

  /**
   * 设置禁用状态
   */
  setDisabled(disabled) {
    this.options.disabled = disabled;
    this.container.classList.toggle('disabled', disabled);
    
    if (disabled && this.isPlaying) {
      this.pause();
    }
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.pause();
    this.container.innerHTML = '';
    this.container.className = '';
  }
}
