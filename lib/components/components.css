/**
 * UI组件样式文件
 * 提供所有组件的基础样式和主题支持
 */

/* CSS变量定义 */
:root {
  /* 浅色主题 */
  --component-primary: #1890ff;
  --component-secondary: #f0f0f0;
  --component-background: #ffffff;
  --component-text: #333333;
  --component-border: #d9d9d9;
  --component-hover: #40a9ff;
  --component-active: #096dd9;
  --component-disabled: #d9d9d9;
  
  /* 尺寸 */
  --component-border-radius: 4px;
  --component-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --component-transition: all 0.3s ease;
}

/* 深色主题 */
[data-theme="dark"] {
  --component-primary: #177ddc;
  --component-secondary: #434343;
  --component-background: #1f1f1f;
  --component-text: #ffffff;
  --component-border: #434343;
  --component-hover: #40a9ff;
  --component-active: #096dd9;
  --component-disabled: #434343;
}

/* 通用样式 */
.component-btn {
  padding: 6px 12px;
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  background: var(--component-background);
  color: var(--component-text);
  cursor: pointer;
  transition: var(--component-transition);
  font-size: 14px;
}

.component-btn:hover {
  background: var(--component-hover);
  color: white;
}

.component-btn:active {
  background: var(--component-active);
}

.component-btn:disabled {
  background: var(--component-disabled);
  cursor: not-allowed;
  opacity: 0.6;
}

/* 颜色选择器样式 */
.color-picker {
  position: relative;
  display: inline-block;
}

.color-picker-trigger {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  background: var(--component-background);
  cursor: pointer;
  transition: var(--component-transition);
}

.color-picker-trigger:hover {
  border-color: var(--component-primary);
}

.color-picker-color {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid var(--component-border);
  margin-right: 8px;
}

.color-picker-arrow {
  font-size: 12px;
  color: var(--component-text);
}

.color-picker-panel {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  margin-top: 4px;
  background: var(--component-background);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  box-shadow: var(--component-box-shadow);
  min-width: 280px;
}

.color-picker-content {
  padding: 16px;
}

.color-picker-presets-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--component-text);
}

.color-picker-presets-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 4px;
  margin-bottom: 16px;
}

.color-picker-preset-item {
  width: 24px;
  height: 24px;
  border-radius: 2px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: var(--component-transition);
}

.color-picker-preset-item:hover {
  border-color: var(--component-primary);
}

.color-picker-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
}

.color-picker-btn {
  @extend .component-btn;
}

/* 图标选择器样式 */
.icon-selector {
  position: relative;
  display: inline-block;
}

.icon-selector-trigger {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  background: var(--component-background);
  cursor: pointer;
  transition: var(--component-transition);
  min-width: 120px;
}

.icon-selector-trigger:hover {
  border-color: var(--component-primary);
}

.icon-selector-preview {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.icon-selector-placeholder {
  color: var(--component-disabled);
  font-size: 14px;
}

.icon-selector-panel {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  margin-top: 4px;
  background: var(--component-background);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  box-shadow: var(--component-box-shadow);
  min-width: 320px;
}

.icon-selector-tabs {
  display: flex;
  border-bottom: 1px solid var(--component-border);
}

.icon-selector-tab {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: var(--component-transition);
  color: var(--component-text);
}

.icon-selector-tab:hover {
  background: var(--component-secondary);
}

.icon-selector-tab.active {
  border-bottom-color: var(--component-primary);
  color: var(--component-primary);
}

.icon-selector-tab-panel {
  display: none;
  padding: 16px;
}

.icon-selector-tab-panel.active {
  display: block;
}

.icon-selector-preset-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
}

.icon-selector-item {
  position: relative;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: var(--component-border-radius);
  cursor: pointer;
  transition: var(--component-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-selector-item:hover {
  border-color: var(--component-primary);
  background: var(--component-secondary);
}

.icon-selector-item.selected {
  border-color: var(--component-primary);
  background: var(--component-primary);
  color: white;
}

/* 图层面板样式 */
.layer-panel {
  background: var(--component-background);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  box-shadow: var(--component-box-shadow);
  min-width: 280px;
}

.layer-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--component-border);
  background: var(--component-secondary);
}

.layer-panel-title-text {
  font-weight: bold;
  color: var(--component-text);
}

.layer-panel-actions {
  display: flex;
  gap: 4px;
}

.layer-panel-btn {
  @extend .component-btn;
  padding: 4px 8px;
  font-size: 12px;
}

.layer-panel-content {
  max-height: 400px;
  overflow: hidden;
  transition: var(--component-transition);
}

.layer-panel-content.collapsed {
  max-height: 0;
}

.layer-panel-list {
  overflow-y: auto;
}

.layer-panel-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--component-border);
  transition: var(--component-transition);
}

.layer-panel-item:hover {
  background: var(--component-secondary);
}

.layer-panel-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.layer-panel-item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.layer-name {
  font-weight: bold;
  color: var(--component-text);
}

.layer-type {
  font-size: 12px;
  color: var(--component-disabled);
}

/* 工具栏样式 */
.toolbar {
  background: var(--component-background);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  box-shadow: var(--component-box-shadow);
  z-index: 1000;
}

.toolbar-horizontal .toolbar-tools {
  display: flex;
  align-items: center;
}

.toolbar-vertical .toolbar-tools {
  display: flex;
  flex-direction: column;
}

.toolbar-tool {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: var(--component-transition);
  border: 2px solid transparent;
}

.toolbar-tool:hover {
  background: var(--component-secondary);
}

.toolbar-tool.active {
  background: var(--component-primary);
  color: white;
}

.toolbar-tool.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-tool-icon {
  margin-right: 6px;
}

.toolbar-separator {
  width: 1px;
  height: 20px;
  background: var(--component-border);
  margin: 0 4px;
}

.toolbar-vertical .toolbar-separator {
  width: 20px;
  height: 1px;
  margin: 4px 0;
}

/* 时间滑块样式 */
.time-slider {
  background: var(--component-background);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  padding: 16px;
  min-width: 400px;
}

.time-slider-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.time-slider-play-controls {
  display: flex;
  gap: 4px;
}

.time-slider-btn {
  @extend .component-btn;
  padding: 6px 10px;
  font-size: 12px;
}

.time-slider-track-container {
  position: relative;
  margin-bottom: 16px;
}

.time-slider-track {
  position: relative;
  height: 6px;
  background: var(--component-secondary);
  border-radius: 3px;
  cursor: pointer;
}

.time-slider-progress {
  height: 100%;
  background: var(--component-primary);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.time-slider-handle {
  position: absolute;
  top: -5px;
  width: 16px;
  height: 16px;
  background: var(--component-primary);
  border: 2px solid white;
  border-radius: 50%;
  cursor: grab;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: left 0.1s ease;
}

.time-slider-handle:active {
  cursor: grabbing;
}

.time-slider-ticks {
  position: relative;
  height: 20px;
  margin-top: 8px;
}

.time-slider-tick {
  position: absolute;
  text-align: center;
}

.time-slider-tick-mark {
  width: 1px;
  height: 6px;
  background: var(--component-border);
  margin: 0 auto;
}

.time-slider-tick-label {
  font-size: 10px;
  color: var(--component-disabled);
  margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .color-picker-panel,
  .icon-selector-panel {
    min-width: 240px;
  }
  
  .layer-panel {
    min-width: 240px;
  }
  
  .time-slider {
    min-width: 300px;
  }
  
  .toolbar-horizontal .toolbar-tool-name {
    display: none;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.color-picker-panel,
.icon-selector-panel {
  animation: fadeIn 0.2s ease;
}

/* 拖拽效果 */
.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.drop-indicator {
  height: 2px;
  background: var(--component-primary);
  margin: 2px 0;
  border-radius: 1px;
}
