/**
 * UI组件系统主入口
 * 统一导出所有UI组件，提供组件管理功能
 */

import ColorPicker from './ColorPicker.js';
import IconSelector from './IconSelector.js';
import LayerPanel from './LayerPanel.js';
import Toolbar from './Toolbar.js';
import TimeSlider from './TimeSlider.js';
import eventBus from '../utils/event-bus.js';

/**
 * 组件管理器
 * 提供组件的统一管理、主题切换、事件协调等功能
 */
export class ComponentManager {
  constructor() {
    this.components = new Map();
    this.themes = new Map();
    this.currentTheme = 'light';
    this.globalConfig = {
      theme: 'light',
      size: 'normal',
      disabled: false
    };

    this.initDefaultThemes();
    this.bindGlobalEvents();
  }

  /**
   * 初始化默认主题
   */
  initDefaultThemes() {
    // 浅色主题
    this.themes.set('light', {
      name: '浅色主题',
      colors: {
        primary: '#1890ff',
        secondary: '#f0f0f0',
        background: '#ffffff',
        text: '#333333',
        border: '#d9d9d9',
        hover: '#40a9ff',
        active: '#096dd9',
        disabled: '#d9d9d9'
      }
    });

    // 深色主题
    this.themes.set('dark', {
      name: '深色主题',
      colors: {
        primary: '#177ddc',
        secondary: '#434343',
        background: '#1f1f1f',
        text: '#ffffff',
        border: '#434343',
        hover: '#40a9ff',
        active: '#096dd9',
        disabled: '#434343'
      }
    });
  }

  /**
   * 绑定全局事件
   */
  bindGlobalEvents() {
    // 监听主题切换事件
    eventBus.on('theme:change', (data) => {
      this.setTheme(data.theme);
    });

    // 监听全局配置变更事件
    eventBus.on('config:change', (data) => {
      this.updateGlobalConfig(data.config);
    });
  }

  /**
   * 注册组件
   * @param {String} id 组件ID
   * @param {Object} component 组件实例
   */
  register(id, component) {
    if (this.components.has(id)) {
      console.warn(`Component ${id} already exists, replacing...`);
      this.unregister(id);
    }

    this.components.set(id, component);
    
    // 应用当前主题和配置
    this.applyThemeToComponent(component);
    this.applyConfigToComponent(component);

    eventBus.emit('component:registered', { id, component });
  }

  /**
   * 注销组件
   * @param {String} id 组件ID
   */
  unregister(id) {
    const component = this.components.get(id);
    if (component) {
      if (component.destroy) {
        component.destroy();
      }
      this.components.delete(id);
      eventBus.emit('component:unregistered', { id, component });
    }
  }

  /**
   * 获取组件
   * @param {String} id 组件ID
   * @returns {Object} 组件实例
   */
  get(id) {
    return this.components.get(id);
  }

  /**
   * 获取所有组件
   * @returns {Map} 组件映射
   */
  getAll() {
    return this.components;
  }

  /**
   * 设置主题
   * @param {String} themeName 主题名称
   */
  setTheme(themeName) {
    if (!this.themes.has(themeName)) {
      console.warn(`Theme ${themeName} not found`);
      return;
    }

    this.currentTheme = themeName;
    this.globalConfig.theme = themeName;

    // 应用主题到所有组件
    for (const component of this.components.values()) {
      this.applyThemeToComponent(component);
    }

    // 更新CSS变量
    this.updateCSSVariables();

    eventBus.emit('theme:changed', { 
      theme: themeName, 
      themeData: this.themes.get(themeName) 
    });
  }

  /**
   * 添加自定义主题
   * @param {String} name 主题名称
   * @param {Object} themeData 主题数据
   */
  addTheme(name, themeData) {
    this.themes.set(name, themeData);
    eventBus.emit('theme:added', { name, themeData });
  }

  /**
   * 更新全局配置
   * @param {Object} config 配置对象
   */
  updateGlobalConfig(config) {
    this.globalConfig = { ...this.globalConfig, ...config };

    // 应用配置到所有组件
    for (const component of this.components.values()) {
      this.applyConfigToComponent(component);
    }

    eventBus.emit('config:updated', { config: this.globalConfig });
  }

  /**
   * 应用主题到组件
   * @param {Object} component 组件实例
   * @private
   */
  applyThemeToComponent(component) {
    if (component.setTheme) {
      component.setTheme(this.currentTheme);
    }
  }

  /**
   * 应用配置到组件
   * @param {Object} component 组件实例
   * @private
   */
  applyConfigToComponent(component) {
    if (component.setDisabled && this.globalConfig.disabled !== undefined) {
      component.setDisabled(this.globalConfig.disabled);
    }
  }

  /**
   * 更新CSS变量
   * @private
   */
  updateCSSVariables() {
    const theme = this.themes.get(this.currentTheme);
    if (!theme) return;

    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--component-${key}`, value);
    });
  }

  /**
   * 创建组件
   * @param {String} type 组件类型
   * @param {String|Element} container 容器
   * @param {Object} options 选项
   * @returns {Object} 组件实例
   */
  create(type, container, options = {}) {
    // 合并全局配置
    const mergedOptions = {
      ...this.globalConfig,
      ...options
    };

    let component;
    switch (type) {
      case 'colorPicker':
        component = new ColorPicker(container, mergedOptions);
        break;
      case 'iconSelector':
        component = new IconSelector(container, mergedOptions);
        break;
      case 'layerPanel':
        component = new LayerPanel(container, mergedOptions);
        break;
      case 'toolbar':
        component = new Toolbar(container, mergedOptions);
        break;
      case 'timeSlider':
        component = new TimeSlider(container, mergedOptions);
        break;
      default:
        throw new Error(`Unknown component type: ${type}`);
    }

    return component;
  }

  /**
   * 批量创建组件
   * @param {Array} componentConfigs 组件配置数组
   * @returns {Map} 创建的组件映射
   */
  createBatch(componentConfigs) {
    const createdComponents = new Map();

    componentConfigs.forEach(config => {
      const { id, type, container, options } = config;
      try {
        const component = this.create(type, container, options);
        this.register(id, component);
        createdComponents.set(id, component);
      } catch (error) {
        console.error(`Failed to create component ${id}:`, error);
      }
    });

    return createdComponents;
  }

  /**
   * 销毁所有组件
   */
  destroyAll() {
    for (const [id] of this.components) {
      this.unregister(id);
    }
  }

  /**
   * 获取组件统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      total: this.components.size,
      byType: {},
      themes: Array.from(this.themes.keys()),
      currentTheme: this.currentTheme,
      globalConfig: this.globalConfig
    };

    // 按类型统计
    for (const component of this.components.values()) {
      const type = component.constructor.name;
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    }

    return stats;
  }
}

// 创建全局组件管理器实例
export const componentManager = new ComponentManager();

// 导出所有组件类
export {
  ColorPicker,
  IconSelector,
  LayerPanel,
  Toolbar,
  TimeSlider
};

// 默认导出
export default {
  ComponentManager,
  componentManager,
  ColorPicker,
  IconSelector,
  LayerPanel,
  Toolbar,
  TimeSlider
};
