/**
 * 图标选择器组件
 * 提供图标选择功能，支持预设图标和自定义图标
 */

import eventBus from '../utils/event-bus.js';

export default class IconSelector {
  constructor(container, options = {}) {
    this.container = typeof container === 'string' ? document.querySelector(container) : container;
    this.options = {
      iconSpritUrl: options.iconSpritUrl || '/static/icons/us_mk_icon.png',
      selectedIcon: options.selectedIcon || null,
      size: options.size || 'normal', // normal, small, large
      disabled: options.disabled || false,
      showUpload: options.showUpload !== false,
      maxCustomIcons: options.maxCustomIcons || 10,
      onChange: options.onChange || null,
      ...options
    };

    // 预设图标配置
    this.iconConfig = [
      [
        { w: 12, h: 21, l: 0, t: 0 },
        { w: 12, h: 21, l: 23, t: 0 },
        { w: 12, h: 21, l: 46, t: 0 },
        { w: 12, h: 21, l: 69, t: 0 },
        { w: 12, h: 21, l: 92, t: 0 },
        { w: 12, h: 21, l: 115, t: 0 }
      ],
      [
        { w: 19, h: 25, l: 0, t: 21 },
        { w: 19, h: 25, l: 23, t: 21 },
        { w: 19, h: 25, l: 46, t: 21 },
        { w: 19, h: 25, l: 69, t: 21 },
        { w: 19, h: 25, l: 92, t: 21 },
        { w: 19, h: 25, l: 115, t: 21 }
      ],
      [
        { w: 18, h: 22, l: 0, t: 44 },
        { w: 18, h: 22, l: 23, t: 44 },
        { w: 18, h: 22, l: 46, t: 44 },
        { w: 18, h: 22, l: 69, t: 44 },
        { w: 18, h: 22, l: 92, t: 44 },
        { w: 18, h: 22, l: 115, t: 44 }
      ]
    ];

    this.selectedIcon = this.options.selectedIcon;
    this.customIcons = [];
    this.isOpen = false;

    this.init();
  }

  /**
   * 初始化组件
   */
  init() {
    this.createStructure();
    this.bindEvents();
    this.updateDisplay();
  }

  /**
   * 创建DOM结构
   */
  createStructure() {
    this.container.className = `icon-selector icon-selector-${this.options.size}`;
    
    this.container.innerHTML = `
      <div class="icon-selector-trigger" ${this.options.disabled ? 'disabled' : ''}>
        <div class="icon-selector-preview">
          ${this.selectedIcon ? this.createIconHTML(this.selectedIcon) : '<span class="icon-selector-placeholder">选择图标</span>'}
        </div>
        <div class="icon-selector-arrow">▼</div>
      </div>
      <div class="icon-selector-panel" style="display: none;">
        <div class="icon-selector-content">
          <div class="icon-selector-tabs">
            <div class="icon-selector-tab active" data-tab="preset">预设图标</div>
            ${this.options.showUpload ? '<div class="icon-selector-tab" data-tab="custom">自定义图标</div>' : ''}
          </div>
          <div class="icon-selector-tab-content">
            <div class="icon-selector-tab-panel active" data-panel="preset">
              ${this.createPresetIconsHTML()}
            </div>
            ${this.options.showUpload ? this.createCustomIconsHTML() : ''}
          </div>
          <div class="icon-selector-actions">
            <button class="icon-selector-btn icon-selector-btn-confirm">确定</button>
            <button class="icon-selector-btn icon-selector-btn-cancel">取消</button>
          </div>
        </div>
      </div>
    `;

    // 获取DOM元素引用
    this.trigger = this.container.querySelector('.icon-selector-trigger');
    this.preview = this.container.querySelector('.icon-selector-preview');
    this.panel = this.container.querySelector('.icon-selector-panel');
    this.tabs = this.container.querySelectorAll('.icon-selector-tab');
    this.tabPanels = this.container.querySelectorAll('.icon-selector-tab-panel');
  }

  /**
   * 创建预设图标HTML
   */
  createPresetIconsHTML() {
    let html = '<div class="icon-selector-preset-grid">';
    
    this.iconConfig.forEach((row, rowIndex) => {
      html += `<div class="icon-selector-row" data-row="${rowIndex}">`;
      row.forEach((icon, colIndex) => {
        const iconId = `preset_${rowIndex}_${colIndex}`;
        const isSelected = this.selectedIcon && this.selectedIcon.id === iconId;
        html += `
          <div class="icon-selector-item ${isSelected ? 'selected' : ''}" 
               data-icon-id="${iconId}" 
               data-icon-type="preset">
            <span class="icon-selector-icon" 
                  style="width: ${icon.w}px; height: ${icon.h}px; 
                         background-image: url(${this.options.iconSpritUrl}); 
                         background-position: -${icon.l}px -${icon.t}px;"></span>
          </div>
        `;
      });
      html += '</div>';
    });
    
    html += '</div>';
    return html;
  }

  /**
   * 创建自定义图标HTML
   */
  createCustomIconsHTML() {
    return `
      <div class="icon-selector-tab-panel" data-panel="custom">
        <div class="icon-selector-upload-area">
          <input type="file" class="icon-selector-file-input" accept="image/*" multiple>
          <div class="icon-selector-upload-hint">
            <div class="icon-selector-upload-icon">📁</div>
            <div class="icon-selector-upload-text">点击上传图标或拖拽到此处</div>
            <div class="icon-selector-upload-desc">支持 PNG、JPG、SVG 格式，最多 ${this.options.maxCustomIcons} 个</div>
          </div>
        </div>
        <div class="icon-selector-custom-grid">
          ${this.createCustomIconsListHTML()}
        </div>
      </div>
    `;
  }

  /**
   * 创建自定义图标列表HTML
   */
  createCustomIconsListHTML() {
    return this.customIcons.map((icon, index) => {
      const isSelected = this.selectedIcon && this.selectedIcon.id === icon.id;
      return `
        <div class="icon-selector-item ${isSelected ? 'selected' : ''}" 
             data-icon-id="${icon.id}" 
             data-icon-type="custom">
          <img class="icon-selector-custom-icon" src="${icon.url}" alt="Custom icon">
          <button class="icon-selector-delete-btn" data-index="${index}">×</button>
        </div>
      `;
    }).join('');
  }

  /**
   * 创建图标HTML
   */
  createIconHTML(icon) {
    if (icon.type === 'preset') {
      const [rowIndex, colIndex] = icon.id.split('_').slice(1).map(Number);
      const iconData = this.iconConfig[rowIndex][colIndex];
      return `
        <span class="icon-selector-icon" 
              style="width: ${iconData.w}px; height: ${iconData.h}px; 
                     background-image: url(${this.options.iconSpritUrl}); 
                     background-position: -${iconData.l}px -${iconData.t}px;"></span>
      `;
    } else {
      return `<img class="icon-selector-custom-icon" src="${icon.url}" alt="Custom icon">`;
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (this.options.disabled) return;

    // 触发器点击事件
    this.trigger.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggle();
    });

    // 标签切换事件
    this.tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        this.switchTab(tab.dataset.tab);
      });
    });

    // 图标选择事件
    this.container.addEventListener('click', (e) => {
      if (e.target.closest('.icon-selector-item')) {
        const item = e.target.closest('.icon-selector-item');
        this.selectIcon(item);
      }
    });

    // 文件上传事件
    const fileInput = this.container.querySelector('.icon-selector-file-input');
    if (fileInput) {
      fileInput.addEventListener('change', (e) => {
        this.handleFileUpload(e.target.files);
      });

      // 拖拽上传
      const uploadArea = this.container.querySelector('.icon-selector-upload-area');
      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
      });

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        this.handleFileUpload(e.dataTransfer.files);
      });

      uploadArea.addEventListener('click', () => {
        fileInput.click();
      });
    }

    // 删除自定义图标事件
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('icon-selector-delete-btn')) {
        e.stopPropagation();
        const index = parseInt(e.target.dataset.index);
        this.deleteCustomIcon(index);
      }
    });

    // 按钮事件
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('icon-selector-btn-confirm')) {
        this.confirm();
      } else if (e.target.classList.contains('icon-selector-btn-cancel')) {
        this.cancel();
      }
    });

    // 点击外部关闭
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target)) {
        this.close();
      }
    });
  }

  /**
   * 切换标签
   */
  switchTab(tabName) {
    this.tabs.forEach(tab => {
      tab.classList.toggle('active', tab.dataset.tab === tabName);
    });

    this.tabPanels.forEach(panel => {
      panel.classList.toggle('active', panel.dataset.panel === tabName);
    });
  }

  /**
   * 选择图标
   */
  selectIcon(item) {
    // 移除之前的选中状态
    this.container.querySelectorAll('.icon-selector-item.selected').forEach(el => {
      el.classList.remove('selected');
    });

    // 添加新的选中状态
    item.classList.add('selected');

    const iconId = item.dataset.iconId;
    const iconType = item.dataset.iconType;

    if (iconType === 'preset') {
      this.selectedIcon = { id: iconId, type: 'preset' };
    } else {
      const customIcon = this.customIcons.find(icon => icon.id === iconId);
      this.selectedIcon = customIcon;
    }

    this.updateDisplay();
  }

  /**
   * 处理文件上传
   */
  handleFileUpload(files) {
    if (this.customIcons.length >= this.options.maxCustomIcons) {
      alert(`最多只能上传 ${this.options.maxCustomIcons} 个自定义图标`);
      return;
    }

    Array.from(files).forEach(file => {
      if (this.customIcons.length >= this.options.maxCustomIcons) return;

      if (!file.type.startsWith('image/')) {
        alert('只支持图片文件');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const iconId = `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const customIcon = {
          id: iconId,
          type: 'custom',
          url: e.target.result,
          name: file.name,
          size: file.size
        };

        this.customIcons.push(customIcon);
        this.updateCustomIconsList();
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * 删除自定义图标
   */
  deleteCustomIcon(index) {
    const deletedIcon = this.customIcons[index];
    this.customIcons.splice(index, 1);

    // 如果删除的是当前选中的图标，清除选中状态
    if (this.selectedIcon && this.selectedIcon.id === deletedIcon.id) {
      this.selectedIcon = null;
      this.updateDisplay();
    }

    this.updateCustomIconsList();
  }

  /**
   * 更新自定义图标列表
   */
  updateCustomIconsList() {
    const customGrid = this.container.querySelector('.icon-selector-custom-grid');
    if (customGrid) {
      customGrid.innerHTML = this.createCustomIconsListHTML();
    }
  }

  /**
   * 更新显示
   */
  updateDisplay() {
    if (this.selectedIcon) {
      this.preview.innerHTML = this.createIconHTML(this.selectedIcon);
    } else {
      this.preview.innerHTML = '<span class="icon-selector-placeholder">选择图标</span>';
    }
  }

  /**
   * 打开面板
   */
  open() {
    if (this.options.disabled) return;
    
    this.isOpen = true;
    this.panel.style.display = 'block';
    this.container.classList.add('icon-selector-open');
    
    eventBus.emit('iconSelector:open', { selector: this });
  }

  /**
   * 关闭面板
   */
  close() {
    this.isOpen = false;
    this.panel.style.display = 'none';
    this.container.classList.remove('icon-selector-open');
    
    eventBus.emit('iconSelector:close', { selector: this });
  }

  /**
   * 切换面板
   */
  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  /**
   * 确认选择
   */
  confirm() {
    this.close();
    if (this.options.onChange) {
      this.options.onChange(this.selectedIcon);
    }
    eventBus.emit('iconSelector:change', { 
      selector: this, 
      icon: this.selectedIcon 
    });
  }

  /**
   * 取消选择
   */
  cancel() {
    this.close();
  }

  /**
   * 获取选中的图标
   */
  getSelectedIcon() {
    return this.selectedIcon;
  }

  /**
   * 设置选中的图标
   */
  setSelectedIcon(icon) {
    this.selectedIcon = icon;
    this.updateDisplay();
  }

  /**
   * 设置禁用状态
   */
  setDisabled(disabled) {
    this.options.disabled = disabled;
    if (disabled) {
      this.trigger.setAttribute('disabled', '');
      this.container.classList.add('icon-selector-disabled');
    } else {
      this.trigger.removeAttribute('disabled');
      this.container.classList.remove('icon-selector-disabled');
    }
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.close();
    this.container.innerHTML = '';
    this.container.className = '';
  }
}
