/**
 * 颜色选择器组件
 * 提供颜色选择功能，支持预设颜色和自定义颜色
 */

import eventBus from '../utils/event-bus.js';

export default class ColorPicker {
  constructor(container, options = {}) {
    this.container = typeof container === 'string' ? document.querySelector(container) : container;
    this.options = {
      defaultColor: options.defaultColor || '#1890ff',
      presetColors: options.presetColors || [
        '#ff4d4f', '#fa541c', '#fa8c16', '#faad14', '#fadb14',
        '#a0d911', '#52c41a', '#13c2c2', '#1890ff', '#2f54eb',
        '#722ed1', '#eb2f96', '#f5222d', '#fa8c16', '#fadb14'
      ],
      showPresets: options.showPresets !== false,
      showInput: options.showInput !== false,
      showClear: options.showClear !== false,
      size: options.size || 'normal', // normal, small, large
      disabled: options.disabled || false,
      onChange: options.onChange || null,
      onClear: options.onClear || null,
      ...options
    };

    this.currentColor = this.options.defaultColor;
    this.isOpen = false;

    this.init();
  }

  /**
   * 初始化组件
   */
  init() {
    this.createStructure();
    this.bindEvents();
    this.updateDisplay();
  }

  /**
   * 创建DOM结构
   */
  createStructure() {
    this.container.className = `color-picker color-picker-${this.options.size}`;
    
    this.container.innerHTML = `
      <div class="color-picker-trigger" ${this.options.disabled ? 'disabled' : ''}>
        <div class="color-picker-color" style="background-color: ${this.currentColor}"></div>
        <div class="color-picker-arrow">▼</div>
      </div>
      <div class="color-picker-panel" style="display: none;">
        <div class="color-picker-content">
          ${this.options.showPresets ? this.createPresetsHTML() : ''}
          <div class="color-picker-custom">
            <div class="color-picker-saturation">
              <div class="color-picker-saturation-bg"></div>
              <div class="color-picker-saturation-pointer"></div>
            </div>
            <div class="color-picker-hue">
              <div class="color-picker-hue-slider"></div>
            </div>
            ${this.options.showInput ? this.createInputHTML() : ''}
          </div>
          <div class="color-picker-actions">
            <button class="color-picker-btn color-picker-btn-confirm">确定</button>
            ${this.options.showClear ? '<button class="color-picker-btn color-picker-btn-clear">清空</button>' : ''}
            <button class="color-picker-btn color-picker-btn-cancel">取消</button>
          </div>
        </div>
      </div>
    `;

    // 获取DOM元素引用
    this.trigger = this.container.querySelector('.color-picker-trigger');
    this.colorDisplay = this.container.querySelector('.color-picker-color');
    this.panel = this.container.querySelector('.color-picker-panel');
    this.saturationArea = this.container.querySelector('.color-picker-saturation');
    this.saturationPointer = this.container.querySelector('.color-picker-saturation-pointer');
    this.hueSlider = this.container.querySelector('.color-picker-hue-slider');
    this.input = this.container.querySelector('.color-picker-input');
  }

  /**
   * 创建预设颜色HTML
   */
  createPresetsHTML() {
    const presetItems = this.options.presetColors.map(color => 
      `<div class="color-picker-preset-item" data-color="${color}" style="background-color: ${color}"></div>`
    ).join('');

    return `
      <div class="color-picker-presets">
        <div class="color-picker-presets-title">预设颜色</div>
        <div class="color-picker-presets-list">
          ${presetItems}
        </div>
      </div>
    `;
  }

  /**
   * 创建输入框HTML
   */
  createInputHTML() {
    return `
      <div class="color-picker-input-group">
        <label>HEX:</label>
        <input type="text" class="color-picker-input" value="${this.currentColor}" maxlength="7">
      </div>
    `;
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (this.options.disabled) return;

    // 触发器点击事件
    this.trigger.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggle();
    });

    // 预设颜色点击事件
    if (this.options.showPresets) {
      this.container.addEventListener('click', (e) => {
        if (e.target.classList.contains('color-picker-preset-item')) {
          const color = e.target.dataset.color;
          this.setColor(color);
        }
      });
    }

    // 输入框事件
    if (this.input) {
      this.input.addEventListener('input', (e) => {
        const value = e.target.value;
        if (this.isValidHexColor(value)) {
          this.setColor(value);
        }
      });
    }

    // 按钮事件
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('color-picker-btn-confirm')) {
        this.confirm();
      } else if (e.target.classList.contains('color-picker-btn-clear')) {
        this.clear();
      } else if (e.target.classList.contains('color-picker-btn-cancel')) {
        this.cancel();
      }
    });

    // 饱和度区域事件
    if (this.saturationArea) {
      this.saturationArea.addEventListener('mousedown', (e) => {
        this.handleSaturationMouseDown(e);
      });
    }

    // 色相滑块事件
    if (this.hueSlider) {
      this.hueSlider.addEventListener('mousedown', (e) => {
        this.handleHueMouseDown(e);
      });
    }

    // 点击外部关闭
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target)) {
        this.close();
      }
    });
  }

  /**
   * 处理饱和度区域鼠标事件
   */
  handleSaturationMouseDown(e) {
    e.preventDefault();
    const rect = this.saturationArea.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    this.updateSaturation(x, y);
    
    const handleMouseMove = (e) => {
      const x = Math.max(0, Math.min(rect.width, e.clientX - rect.left));
      const y = Math.max(0, Math.min(rect.height, e.clientY - rect.top));
      this.updateSaturation(x, y);
    };
    
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }

  /**
   * 处理色相滑块鼠标事件
   */
  handleHueMouseDown(e) {
    e.preventDefault();
    const rect = this.hueSlider.getBoundingClientRect();
    const x = e.clientX - rect.left;
    
    this.updateHue(x);
    
    const handleMouseMove = (e) => {
      const x = Math.max(0, Math.min(rect.width, e.clientX - rect.left));
      this.updateHue(x);
    };
    
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }

  /**
   * 更新饱和度
   */
  updateSaturation(x, y) {
    const rect = this.saturationArea.getBoundingClientRect();
    const saturation = (x / rect.width) * 100;
    const brightness = 100 - (y / rect.height) * 100;
    
    this.saturationPointer.style.left = `${x}px`;
    this.saturationPointer.style.top = `${y}px`;
    
    // 这里可以根据HSB值计算新的颜色
    // 简化实现，直接使用当前颜色
  }

  /**
   * 更新色相
   */
  updateHue(x) {
    const rect = this.hueSlider.getBoundingClientRect();
    const hue = (x / rect.width) * 360;
    
    // 这里可以根据色相值计算新的颜色
    // 简化实现，直接使用当前颜色
  }

  /**
   * 设置颜色
   */
  setColor(color) {
    if (!this.isValidHexColor(color)) return;
    
    this.currentColor = color;
    this.updateDisplay();
  }

  /**
   * 更新显示
   */
  updateDisplay() {
    this.colorDisplay.style.backgroundColor = this.currentColor;
    if (this.input) {
      this.input.value = this.currentColor;
    }
  }

  /**
   * 验证十六进制颜色
   */
  isValidHexColor(color) {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  }

  /**
   * 打开面板
   */
  open() {
    if (this.options.disabled) return;
    
    this.isOpen = true;
    this.panel.style.display = 'block';
    this.container.classList.add('color-picker-open');
    
    eventBus.emit('colorPicker:open', { picker: this });
  }

  /**
   * 关闭面板
   */
  close() {
    this.isOpen = false;
    this.panel.style.display = 'none';
    this.container.classList.remove('color-picker-open');
    
    eventBus.emit('colorPicker:close', { picker: this });
  }

  /**
   * 切换面板
   */
  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  /**
   * 确认选择
   */
  confirm() {
    this.close();
    if (this.options.onChange) {
      this.options.onChange(this.currentColor);
    }
    eventBus.emit('colorPicker:change', { 
      picker: this, 
      color: this.currentColor 
    });
  }

  /**
   * 清空颜色
   */
  clear() {
    this.currentColor = '';
    this.updateDisplay();
    this.close();
    
    if (this.options.onClear) {
      this.options.onClear();
    }
    eventBus.emit('colorPicker:clear', { picker: this });
  }

  /**
   * 取消选择
   */
  cancel() {
    this.close();
  }

  /**
   * 获取当前颜色
   */
  getColor() {
    return this.currentColor;
  }

  /**
   * 设置禁用状态
   */
  setDisabled(disabled) {
    this.options.disabled = disabled;
    if (disabled) {
      this.trigger.setAttribute('disabled', '');
      this.container.classList.add('color-picker-disabled');
    } else {
      this.trigger.removeAttribute('disabled');
      this.container.classList.remove('color-picker-disabled');
    }
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.close();
    this.container.innerHTML = '';
    this.container.className = '';
  }
}
