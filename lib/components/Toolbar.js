/**
 * 工具栏组件
 * 提供地图工具栏功能，包括绘制工具、测量工具、导航工具等
 */

import eventBus from '../utils/event-bus.js';

export default class Toolbar {
  constructor(container, options = {}) {
    this.container = typeof container === 'string' ? document.querySelector(container) : container;
    this.options = {
      orientation: options.orientation || 'horizontal', // horizontal, vertical
      position: options.position || 'top-left', // top-left, top-right, bottom-left, bottom-right
      size: options.size || 'normal', // small, normal, large
      theme: options.theme || 'light', // light, dark
      collapsible: options.collapsible !== false,
      defaultCollapsed: options.defaultCollapsed || false,
      tools: options.tools || this.getDefaultTools(),
      onToolClick: options.onToolClick || null,
      ...options
    };

    this.activeTool = null;
    this.isCollapsed = this.options.defaultCollapsed;
    this.toolInstances = new Map();

    this.init();
  }

  /**
   * 获取默认工具配置
   */
  getDefaultTools() {
    return [
      {
        id: 'select',
        name: '选择',
        icon: '👆',
        tooltip: '选择工具',
        type: 'toggle',
        group: 'basic'
      },
      {
        id: 'pan',
        name: '平移',
        icon: '✋',
        tooltip: '平移地图',
        type: 'toggle',
        group: 'basic'
      },
      { type: 'separator' },
      {
        id: 'marker',
        name: '标记',
        icon: '📍',
        tooltip: '添加标记点',
        type: 'toggle',
        group: 'draw'
      },
      {
        id: 'polygon',
        name: '多边形',
        icon: '⬟',
        tooltip: '绘制多边形',
        type: 'toggle',
        group: 'draw'
      },
      {
        id: 'polyline',
        name: '折线',
        icon: '📈',
        tooltip: '绘制折线',
        type: 'toggle',
        group: 'draw'
      },
      {
        id: 'circle',
        name: '圆形',
        icon: '⭕',
        tooltip: '绘制圆形',
        type: 'toggle',
        group: 'draw'
      },
      {
        id: 'rectangle',
        name: '矩形',
        icon: '⬜',
        tooltip: '绘制矩形',
        type: 'toggle',
        group: 'draw'
      },
      { type: 'separator' },
      {
        id: 'measure-distance',
        name: '测距',
        icon: '📏',
        tooltip: '测量距离',
        type: 'toggle',
        group: 'measure'
      },
      {
        id: 'measure-area',
        name: '测面积',
        icon: '📐',
        tooltip: '测量面积',
        type: 'toggle',
        group: 'measure'
      },
      { type: 'separator' },
      {
        id: 'zoom-in',
        name: '放大',
        icon: '🔍+',
        tooltip: '放大地图',
        type: 'button',
        group: 'zoom'
      },
      {
        id: 'zoom-out',
        name: '缩小',
        icon: '🔍-',
        tooltip: '缩小地图',
        type: 'button',
        group: 'zoom'
      },
      {
        id: 'zoom-fit',
        name: '适合',
        icon: '🔍⚏',
        tooltip: '适合窗口',
        type: 'button',
        group: 'zoom'
      },
      { type: 'separator' },
      {
        id: 'clear',
        name: '清空',
        icon: '🗑',
        tooltip: '清空所有',
        type: 'button',
        group: 'edit'
      },
      {
        id: 'undo',
        name: '撤销',
        icon: '↶',
        tooltip: '撤销操作',
        type: 'button',
        group: 'edit'
      },
      {
        id: 'redo',
        name: '重做',
        icon: '↷',
        tooltip: '重做操作',
        type: 'button',
        group: 'edit'
      }
    ];
  }

  /**
   * 初始化组件
   */
  init() {
    this.createStructure();
    this.bindEvents();
    this.updateLayout();
  }

  /**
   * 创建DOM结构
   */
  createStructure() {
    this.container.className = `toolbar toolbar-${this.options.orientation} toolbar-${this.options.size} toolbar-${this.options.theme} toolbar-${this.options.position}`;
    
    this.container.innerHTML = `
      <div class="toolbar-header">
        ${this.options.collapsible ? '<button class="toolbar-collapse-btn">≡</button>' : ''}
        <div class="toolbar-title">工具栏</div>
      </div>
      <div class="toolbar-content ${this.isCollapsed ? 'collapsed' : ''}">
        <div class="toolbar-tools">
          ${this.createToolsHTML()}
        </div>
      </div>
    `;

    // 获取DOM元素引用
    this.header = this.container.querySelector('.toolbar-header');
    this.content = this.container.querySelector('.toolbar-content');
    this.toolsContainer = this.container.querySelector('.toolbar-tools');
    this.collapseBtn = this.container.querySelector('.toolbar-collapse-btn');
  }

  /**
   * 创建工具HTML
   */
  createToolsHTML() {
    return this.options.tools.map(tool => {
      if (tool.type === 'separator') {
        return '<div class="toolbar-separator"></div>';
      }

      const isActive = this.activeTool === tool.id;
      const isDisabled = tool.disabled || false;

      return `
        <div class="toolbar-tool ${tool.type === 'toggle' ? 'toolbar-tool-toggle' : 'toolbar-tool-button'} ${isActive ? 'active' : ''} ${isDisabled ? 'disabled' : ''}"
             data-tool-id="${tool.id}"
             data-tool-type="${tool.type}"
             data-tool-group="${tool.group || ''}"
             title="${tool.tooltip || tool.name}">
          <div class="toolbar-tool-icon">${tool.icon}</div>
          <div class="toolbar-tool-name">${tool.name}</div>
        </div>
      `;
    }).join('');
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 折叠/展开事件
    if (this.collapseBtn) {
      this.collapseBtn.addEventListener('click', () => {
        this.toggleCollapse();
      });
    }

    // 工具点击事件
    this.toolsContainer.addEventListener('click', (e) => {
      const toolElement = e.target.closest('.toolbar-tool');
      if (toolElement && !toolElement.classList.contains('disabled')) {
        const toolId = toolElement.dataset.toolId;
        const toolType = toolElement.dataset.toolType;
        this.handleToolClick(toolId, toolType);
      }
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcut(e);
    });
  }

  /**
   * 处理工具点击
   */
  handleToolClick(toolId, toolType) {
    const tool = this.options.tools.find(t => t.id === toolId);
    if (!tool) return;

    if (toolType === 'toggle') {
      // 切换工具
      if (this.activeTool === toolId) {
        this.deactivateTool();
      } else {
        this.activateTool(toolId);
      }
    } else {
      // 按钮工具
      this.executeTool(toolId);
    }

    // 触发回调
    if (this.options.onToolClick) {
      this.options.onToolClick(toolId, tool, this.activeTool);
    }

    // 触发事件
    eventBus.emit('toolbar:toolClick', {
      toolbar: this,
      toolId,
      tool,
      activeTool: this.activeTool
    });
  }

  /**
   * 激活工具
   */
  activateTool(toolId) {
    // 先取消激活当前工具
    this.deactivateTool();

    // 激活新工具
    this.activeTool = toolId;
    this.updateToolStates();

    eventBus.emit('toolbar:toolActivated', {
      toolbar: this,
      toolId
    });
  }

  /**
   * 取消激活工具
   */
  deactivateTool() {
    if (this.activeTool) {
      const previousTool = this.activeTool;
      this.activeTool = null;
      this.updateToolStates();

      eventBus.emit('toolbar:toolDeactivated', {
        toolbar: this,
        toolId: previousTool
      });
    }
  }

  /**
   * 执行工具
   */
  executeTool(toolId) {
    switch (toolId) {
      case 'zoom-in':
        eventBus.emit('map:zoomIn');
        break;
      case 'zoom-out':
        eventBus.emit('map:zoomOut');
        break;
      case 'zoom-fit':
        eventBus.emit('map:zoomFit');
        break;
      case 'clear':
        if (confirm('确定要清空所有内容吗？')) {
          eventBus.emit('map:clear');
        }
        break;
      case 'undo':
        eventBus.emit('map:undo');
        break;
      case 'redo':
        eventBus.emit('map:redo');
        break;
      default:
        eventBus.emit(`tool:${toolId}`, { toolbar: this });
        break;
    }
  }

  /**
   * 更新工具状态
   */
  updateToolStates() {
    this.toolsContainer.querySelectorAll('.toolbar-tool').forEach(toolElement => {
      const toolId = toolElement.dataset.toolId;
      const isActive = this.activeTool === toolId;
      toolElement.classList.toggle('active', isActive);
    });
  }

  /**
   * 处理键盘快捷键
   */
  handleKeyboardShortcut(e) {
    // Ctrl/Cmd + Z: 撤销
    if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
      e.preventDefault();
      this.executeTool('undo');
    }
    // Ctrl/Cmd + Shift + Z 或 Ctrl/Cmd + Y: 重做
    else if (((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'Z') || 
             ((e.ctrlKey || e.metaKey) && e.key === 'y')) {
      e.preventDefault();
      this.executeTool('redo');
    }
    // Escape: 取消激活工具
    else if (e.key === 'Escape') {
      this.deactivateTool();
    }
    // 数字键快捷键
    else if (e.key >= '1' && e.key <= '9') {
      const toolIndex = parseInt(e.key) - 1;
      const tools = this.options.tools.filter(t => t.type !== 'separator');
      if (tools[toolIndex]) {
        this.handleToolClick(tools[toolIndex].id, tools[toolIndex].type);
      }
    }
  }

  /**
   * 切换折叠状态
   */
  toggleCollapse() {
    this.isCollapsed = !this.isCollapsed;
    this.content.classList.toggle('collapsed', this.isCollapsed);
    this.collapseBtn.textContent = this.isCollapsed ? '≡' : '×';
    
    eventBus.emit('toolbar:collapse', {
      toolbar: this,
      collapsed: this.isCollapsed
    });
  }

  /**
   * 更新布局
   */
  updateLayout() {
    // 根据方向和位置调整样式
    this.container.style.position = 'absolute';
    
    const [vPos, hPos] = this.options.position.split('-');
    
    if (vPos === 'top') {
      this.container.style.top = '10px';
      this.container.style.bottom = 'auto';
    } else {
      this.container.style.bottom = '10px';
      this.container.style.top = 'auto';
    }
    
    if (hPos === 'left') {
      this.container.style.left = '10px';
      this.container.style.right = 'auto';
    } else {
      this.container.style.right = '10px';
      this.container.style.left = 'auto';
    }
  }

  /**
   * 添加工具
   */
  addTool(tool, index = -1) {
    if (index === -1) {
      this.options.tools.push(tool);
    } else {
      this.options.tools.splice(index, 0, tool);
    }
    this.refreshTools();
  }

  /**
   * 移除工具
   */
  removeTool(toolId) {
    const index = this.options.tools.findIndex(t => t.id === toolId);
    if (index > -1) {
      this.options.tools.splice(index, 1);
      if (this.activeTool === toolId) {
        this.deactivateTool();
      }
      this.refreshTools();
    }
  }

  /**
   * 设置工具状态
   */
  setToolEnabled(toolId, enabled) {
    const tool = this.options.tools.find(t => t.id === toolId);
    if (tool) {
      tool.disabled = !enabled;
      const toolElement = this.toolsContainer.querySelector(`[data-tool-id="${toolId}"]`);
      if (toolElement) {
        toolElement.classList.toggle('disabled', !enabled);
      }
    }
  }

  /**
   * 刷新工具
   */
  refreshTools() {
    this.toolsContainer.innerHTML = this.createToolsHTML();
    this.updateToolStates();
  }

  /**
   * 获取激活的工具
   */
  getActiveTool() {
    return this.activeTool;
  }

  /**
   * 获取所有工具
   */
  getTools() {
    return this.options.tools;
  }

  /**
   * 设置主题
   */
  setTheme(theme) {
    this.container.classList.remove(`toolbar-${this.options.theme}`);
    this.options.theme = theme;
    this.container.classList.add(`toolbar-${theme}`);
  }

  /**
   * 设置位置
   */
  setPosition(position) {
    this.container.classList.remove(`toolbar-${this.options.position}`);
    this.options.position = position;
    this.container.classList.add(`toolbar-${position}`);
    this.updateLayout();
  }

  /**
   * 销毁组件
   */
  destroy() {
    this.deactivateTool();
    this.container.innerHTML = '';
    this.container.className = '';
    this.container.style.cssText = '';
    this.toolInstances.clear();
  }
}
