/**
 * Jest 测试配置
 */

module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',

  // 根目录
  rootDir: '.',

  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/tests/**/*.spec.js',
    '<rootDir>/src/**/__tests__/**/*.js',
    '<rootDir>/src/**/*.test.js',
    '<rootDir>/src/**/*.spec.js'
  ],

  // 模块文件扩展名
  moduleFileExtensions: ['js', 'json', 'vue'],

  // 模块名映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  },

  // 转换器
  transform: {
    '^.+\\.vue$': '@vue/vue2-jest',
    '^.+\\.js$': 'babel-jest'
  },

  // 转换忽略模式
  transformIgnorePatterns: [
    'node_modules/(?!(@daas|lodash-es)/)'
  ],

  // 收集覆盖率的文件
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!src/router/**',
    '!src/**/*.config.js',
    '!src/**/*.d.ts'
  ],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },

  // 覆盖率报告格式
  coverageReporters: ['text', 'lcov', 'html'],

  // 覆盖率输出目录
  coverageDirectory: '<rootDir>/coverage',

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // 全局变量
  globals: {
    'vue-jest': {
      babelConfig: {
        presets: ['@babel/preset-env']
      }
    }
  },

  // 清除模拟
  clearMocks: true,

  // 详细输出
  verbose: true,

  // 测试超时
  testTimeout: 10000
};
