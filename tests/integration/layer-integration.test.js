/**
 * Layer 集成测试
 * 测试 Layer 类与其各个管理器的集成工作
 */

import Layer from '@/model/layer/index.js';
import DataSourceFactory from '@/model/datasource/DataSourceFactory.js';

describe('Layer Integration Tests', () => {
  let layer;
  let mockMap;

  beforeEach(() => {
    mockMap = createMockMap();
    layer = new Layer(mockMap, {
      id: 'integration-test-layer',
      isStatic: true
    });
  });

  afterEach(() => {
    if (layer) {
      layer.destroy();
    }
    DataSourceFactory.destroyAll();
  });

  describe('完整的图层生命周期', () => {
    it('应该能够完成从初始化到销毁的完整流程', async () => {
      // 1. 初始化图层
      const layerConfig = {
        type: 'marker',
        displayName: '集成测试图层',
        localData: [
          { id: 1, name: '北京', lng: 116.404, lat: 39.915 },
          { id: 2, name: '上海', lng: 121.473, lat: 31.230 },
          { id: 3, name: '广州', lng: 113.264, lat: 23.129 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ],
        styleConfig: {
          iconType: 'default',
          default: {
            color: '#ff0000',
            size: 32
          }
        }
      };

      await layer.init(layerConfig);
      expect(layer.type).toBe('marker');
      expect(layer.layerConfig.displayName).toBe('集成测试图层');

      // 2. 加载数据
      await layer.loadData();
      const data = layer.getResourceData();
      expect(data).toHaveLength(3);
      expect(data[0].name).toBe('北京');

      // 3. 验证数据源集成
      expect(layer.dataManager.dataSource).toBeDefined();
      expect(layer.dataManager.dataSource.type).toBe('static');
      expect(layer.dataManager.dataSource.isLoaded()).toBe(true);

      // 4. 验证样式管理
      const markerConfig = layer.markerConfig;
      expect(markerConfig.iconType).toBe('default');
      expect(markerConfig.default.color).toBe('#ff0000');

      // 5. 验证渲染器
      const covers = layer.getAllCover();
      expect(Array.isArray(covers)).toBe(true);

      // 6. 测试显示/隐藏切换
      const initialHide = layer.hide;
      layer.changeShow();
      expect(layer.hide).toBe(!initialHide);

      // 7. 销毁图层
      layer.destroy();
      expect(layer.dataManager).toBeNull();
      expect(layer.styleManager).toBeNull();
      expect(layer.eventManager).toBeNull();
      expect(layer.renderer).toBeNull();
    });
  });

  describe('数据源与图层的集成', () => {
    it('应该能够在静态数据源和动态数据源之间切换', async () => {
      // 初始化为静态数据源
      const staticConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '静态数据', lng: 116.404, lat: 39.915 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ]
      };

      await layer.init(staticConfig);
      await layer.loadData();
      
      expect(layer.dataManager.isStatic).toBe(true);
      expect(layer.dataManager.dataSource.type).toBe('static');
      
      const staticData = layer.getResourceData();
      expect(staticData).toHaveLength(1);
      expect(staticData[0].name).toBe('静态数据');

      // 切换到动态数据源
      layer.destroy();
      layer = new Layer(mockMap, {
        id: 'dynamic-test-layer',
        isStatic: false
      });

      const dynamicConfig = {
        type: 'marker',
        resourceId: 'test-resource',
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ]
      };

      // 模拟动态数据源的响应
      const mockResourceStore = layer.dataManager.resourceStore;
      mockResourceStore.dispatch = jest.fn().mockResolvedValue({
        resourceName: '动态资源',
        columns: [
          { name: 'id', displayName: 'ID' },
          { name: 'name', displayName: '名称' },
          { name: 'lng', displayName: '经度' },
          { name: 'lat', displayName: '纬度' }
        ]
      });
      mockResourceStore.getters = {
        'data/resourceDataList': [
          { id: 1, name: '动态数据', lng: 121.473, lat: 31.230 }
        ],
        'data/pageParam': { pageIndex: 1, limit: 100, recordTotal: 1 },
        'resourceInfo': {
          resourceName: '动态资源',
          columns: [
            { name: 'id', displayName: 'ID' },
            { name: 'name', displayName: '名称' },
            { name: 'lng', displayName: '经度' },
            { name: 'lat', displayName: '纬度' }
          ]
        }
      };

      await layer.init(dynamicConfig);
      await layer.loadData();
      
      expect(layer.dataManager.isStatic).toBe(false);
      expect(layer.dataManager.dataSource.type).toBe('dynamic');
      
      const dynamicData = layer.getResourceData();
      expect(dynamicData).toHaveLength(1);
      expect(dynamicData[0].name).toBe('动态数据');
    });
  });

  describe('样式管理与渲染的集成', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '测试点', lng: 116.404, lat: 39.915 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ],
        styleConfig: {
          iconType: 'default',
          default: {
            color: '#ff0000',
            size: 32
          }
        }
      };
      await layer.init(layerConfig);
      await layer.loadData();
    });

    it('应该能够更新样式并反映到渲染中', () => {
      // 更新样式配置
      const newStyleConfig = {
        iconType: 'custom',
        custom: {
          color: '#00ff00',
          size: 24
        }
      };

      layer.updateMarkerConfig('custom', newStyleConfig.custom);
      
      expect(layer.markerConfig.iconType).toBe('custom');
      expect(layer.markerConfig.custom.color).toBe('#00ff00');
      expect(layer.markerConfig.custom.size).toBe(24);
    });

    it('应该能够重置样式配置', () => {
      // 先修改样式
      layer.updateMarkerConfig('custom', { color: '#00ff00', size: 24 });
      
      // 然后重置
      const resetConfig = layer.resetMarkerConfig();
      
      expect(resetConfig.iconType).toBe('default');
      expect(resetConfig.default.color).toBe('#ff0000');
    });
  });

  describe('事件管理与数据更新的集成', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '原始点', lng: 116.404, lat: 39.915 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ]
      };
      await layer.init(layerConfig);
      await layer.loadData();
    });

    it('应该能够更新数据并触发相应的事件处理', () => {
      const updateData = {
        id: 1,
        name: '更新后的点',
        lng: 121.473,
        lat: 31.230
      };

      layer.updateData(updateData, true);
      
      // 验证数据已更新
      const data = layer.getResourceData();
      const updatedItem = data.find(item => item.id === 1);
      expect(updatedItem.name).toBe('更新后的点');
    });

    it('应该能够处理覆盖物的删除', () => {
      // 模拟删除操作
      layer.delOverlay('marker', 1);
      
      // 验证覆盖物已从相应数组中移除
      const markers = layer.markers;
      const deletedMarker = markers.find(marker => marker.id === 1);
      expect(deletedMarker).toBeUndefined();
    });
  });

  describe('时间范围过滤集成', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '点1', lng: 116.404, lat: 39.915, time: '2023-01-01' },
          { id: 2, name: '点2', lng: 121.473, lat: 31.230, time: '2023-01-02' },
          { id: 3, name: '点3', lng: 113.264, lat: 23.129, time: '2023-01-03' }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' },
          { name: 'time', mapColumnName: 'time' }
        ]
      };
      await layer.init(layerConfig);
      await layer.loadData();
    });

    it('应该能够获取时间范围并进行过滤', () => {
      const timeRange = layer.getTimeRange();
      expect(timeRange).toHaveLength(3);
      expect(timeRange).toContain('2023-01-01');
      expect(timeRange).toContain('2023-01-02');
      expect(timeRange).toContain('2023-01-03');

      // 测试时间范围过滤
      layer.filterCoverByTimeRange(['2023-01-01', '2023-01-02']);
      
      // 验证过滤效果（这里需要根据实际的过滤逻辑来验证）
      expect(layer.dataManager.hasTimeRange).toBe(true);
    });
  });

  describe('错误处理集成', () => {
    it('应该能够处理初始化错误', async () => {
      const invalidConfig = {
        // 缺少必需的 type 字段
        displayName: '无效配置'
      };

      await expect(layer.init(invalidConfig)).rejects.toThrow();
      expect(layer.layerConfig).toEqual({});
    });

    it('应该能够处理数据加载错误', async () => {
      const layerConfig = {
        type: 'marker',
        localData: []
      };
      
      await layer.init(layerConfig);
      
      // 模拟渲染器加载失败
      layer.renderer.loadLayerData = jest.fn().mockRejectedValue(new Error('渲染失败'));
      
      await expect(layer.loadData()).rejects.toThrow('渲染失败');
    });
  });

  describe('内存管理集成', () => {
    it('应该能够正确清理所有资源', async () => {
      const layerConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '测试点', lng: 116.404, lat: 39.915 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ]
      };

      await layer.init(layerConfig);
      await layer.loadData();

      // 验证资源已创建
      expect(layer.dataManager).toBeDefined();
      expect(layer.styleManager).toBeDefined();
      expect(layer.eventManager).toBeDefined();
      expect(layer.renderer).toBeDefined();
      expect(layer.dataManager.dataSource).toBeDefined();

      // 销毁图层
      layer.destroy();

      // 验证所有资源已清理
      expect(layer.dataManager).toBeNull();
      expect(layer.styleManager).toBeNull();
      expect(layer.eventManager).toBeNull();
      expect(layer.renderer).toBeNull();
      expect(layer.layerConfig).toBeNull();
      expect(layer.Map).toBeNull();
      expect(layer._map).toBeNull();

      // 验证数据源也已销毁
      const dataSourceInstance = DataSourceFactory.getInstance(`${layer.id}_datasource`);
      expect(dataSourceInstance).toBeNull();
    });
  });
});
