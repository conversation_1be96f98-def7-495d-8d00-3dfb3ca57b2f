/**
 * Mixins 单元测试
 */

import { mount, createLocalVue } from '@vue/test-utils';
import { 
  formMixin, 
  uploadMixin, 
  communicationMixin,
  styleMixin 
} from '@/mixins/index.js';

const localVue = createLocalVue();

describe('formMixin', () => {
  let wrapper;

  const TestComponent = {
    mixins: [formMixin],
    template: '<div></div>',
    data() {
      return {
        fieldValidators: {
          name: (value) => {
            if (!value) return '名称不能为空';
            if (value.length < 2) return '名称至少2个字符';
            return true;
          },
          email: (value) => {
            if (!value) return '邮箱不能为空';
            if (!/\S+@\S+\.\S+/.test(value)) return '邮箱格式不正确';
            return true;
          }
        }
      };
    },
    methods: {
      getDefaultFormData() {
        return { name: '', email: '' };
      }
    }
  };

  beforeEach(() => {
    wrapper = mount(TestComponent, {
      localVue,
      propsData: {
        config: { name: '<PERSON>', email: '<EMAIL>' }
      }
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  describe('表单数据管理', () => {
    it('应该正确初始化表单数据', () => {
      expect(wrapper.vm.formData.form.name).toBe('John');
      expect(wrapper.vm.formData.form.email).toBe('<EMAIL>');
    });

    it('应该在配置变化时更新表单数据', async () => {
      await wrapper.setProps({
        config: { name: 'Jane', email: '<EMAIL>' }
      });

      expect(wrapper.vm.formData.form.name).toBe('Jane');
      expect(wrapper.vm.formData.form.email).toBe('<EMAIL>');
    });

    it('应该能够获取表单数据', () => {
      const formData = wrapper.vm.getFormData();
      expect(formData).toEqual({
        name: 'John',
        email: '<EMAIL>'
      });
    });

    it('应该能够设置表单数据', () => {
      wrapper.vm.setFormData({ name: 'Bob', age: 25 });
      expect(wrapper.vm.formData.form.name).toBe('Bob');
      expect(wrapper.vm.formData.form.age).toBe(25);
    });
  });

  describe('字段变化处理', () => {
    it('应该正确处理字段变化', () => {
      wrapper.vm.handleFieldChange('name', 'Alice');
      
      expect(wrapper.vm.formData.form.name).toBe('Alice');
      expect(wrapper.emitted('change')).toBeTruthy();
      expect(wrapper.emitted('change')[0]).toEqual([{ name: 'Alice' }]);
    });

    it('应该在字段变化时清除验证错误', () => {
      // 先设置一个验证错误
      wrapper.vm.validationErrors.name = '名称不能为空';
      
      wrapper.vm.handleFieldChange('name', 'Valid Name');
      
      expect(wrapper.vm.validationErrors.name).toBeUndefined();
    });
  });

  describe('表单验证', () => {
    it('应该能够验证单个字段', () => {
      const result = wrapper.vm.validateField('name', '');
      expect(result).toBe(false);
      expect(wrapper.vm.validationErrors.name).toBe('名称不能为空');
    });

    it('应该在验证通过时返回 true', () => {
      const result = wrapper.vm.validateField('name', 'Valid Name');
      expect(result).toBe(true);
      expect(wrapper.vm.validationErrors.name).toBeUndefined();
    });

    it('应该能够验证整个表单', async () => {
      wrapper.vm.formData.form = { name: '', email: 'invalid-email' };
      
      const isValid = await wrapper.vm.validateForm();
      
      expect(isValid).toBe(false);
      expect(wrapper.vm.validationErrors.name).toBe('名称不能为空');
      expect(wrapper.vm.validationErrors.email).toBe('邮箱格式不正确');
      expect(wrapper.emitted('validate')).toBeTruthy();
    });

    it('应该在表单有效时返回 true', async () => {
      wrapper.vm.formData.form = { name: 'John', email: '<EMAIL>' };
      
      const isValid = await wrapper.vm.validateForm();
      
      expect(isValid).toBe(true);
      expect(Object.keys(wrapper.vm.validationErrors)).toHaveLength(0);
    });
  });

  describe('表单重置', () => {
    it('应该能够重置表单', () => {
      wrapper.vm.formData.form = { name: 'Changed', email: '<EMAIL>' };
      wrapper.vm.validationErrors = { name: 'Some error' };
      
      wrapper.vm.resetForm();
      
      expect(wrapper.vm.formData.form).toEqual({ name: '', email: '' });
      expect(wrapper.vm.validationErrors).toEqual({});
      expect(wrapper.emitted('reset')).toBeTruthy();
    });
  });

  describe('计算属性', () => {
    it('应该正确计算是否有错误', () => {
      expect(wrapper.vm.hasErrors).toBe(false);
      
      wrapper.vm.validationErrors.name = 'Some error';
      expect(wrapper.vm.hasErrors).toBe(true);
    });

    it('应该正确计算表单是否有效', () => {
      expect(wrapper.vm.isValid).toBe(true);
      
      wrapper.vm.validationErrors.name = 'Some error';
      expect(wrapper.vm.isValid).toBe(false);
      
      wrapper.vm.validationErrors = {};
      wrapper.vm.isValidating = true;
      expect(wrapper.vm.isValid).toBe(false);
    });
  });
});

describe('uploadMixin', () => {
  let wrapper;

  const TestComponent = {
    mixins: [uploadMixin],
    template: '<div></div>'
  };

  beforeEach(() => {
    wrapper = mount(TestComponent, {
      localVue,
      propsData: {
        accept: 'image/*',
        maxSize: 1024 * 1024, // 1MB
        multiple: false
      }
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  describe('文件验证', () => {
    it('应该验证文件数量', () => {
      const files = [
        new File(['content1'], 'file1.jpg', { type: 'image/jpeg' }),
        new File(['content2'], 'file2.jpg', { type: 'image/jpeg' })
      ];
      
      const result = wrapper.vm.validateFiles(files);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('只能上传一个文件');
    });

    it('应该验证文件大小', () => {
      const largeContent = 'x'.repeat(2 * 1024 * 1024); // 2MB
      const files = [
        new File([largeContent], 'large.jpg', { type: 'image/jpeg' })
      ];
      
      const result = wrapper.vm.validateFiles(files);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('大小超过限制');
    });

    it('应该验证文件类型', () => {
      const files = [
        new File(['content'], 'document.pdf', { type: 'application/pdf' })
      ];
      
      const result = wrapper.vm.validateFiles(files);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('类型不支持');
    });

    it('应该通过有效文件的验证', () => {
      const files = [
        new File(['content'], 'image.jpg', { type: 'image/jpeg' })
      ];
      
      const result = wrapper.vm.validateFiles(files);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('文件类型检查', () => {
    it('应该接受匹配的 MIME 类型', () => {
      const file = new File(['content'], 'image.jpg', { type: 'image/jpeg' });
      const result = wrapper.vm.isAcceptedFileType(file);
      expect(result).toBe(true);
    });

    it('应该接受匹配的文件扩展名', async () => {
      await wrapper.setProps({ accept: '.jpg,.png' });
      
      const file = new File(['content'], 'image.jpg', { type: 'image/jpeg' });
      const result = wrapper.vm.isAcceptedFileType(file);
      expect(result).toBe(true);
    });

    it('应该拒绝不匹配的文件类型', () => {
      const file = new File(['content'], 'document.pdf', { type: 'application/pdf' });
      const result = wrapper.vm.isAcceptedFileType(file);
      expect(result).toBe(false);
    });

    it('应该接受所有文件类型当 accept 为 *', async () => {
      await wrapper.setProps({ accept: '*' });
      
      const file = new File(['content'], 'any.file', { type: 'application/octet-stream' });
      const result = wrapper.vm.isAcceptedFileType(file);
      expect(result).toBe(true);
    });
  });

  describe('文件大小格式化', () => {
    it('应该正确格式化字节', () => {
      expect(wrapper.vm.formatFileSize(0)).toBe('0 B');
      expect(wrapper.vm.formatFileSize(1024)).toBe('1 KB');
      expect(wrapper.vm.formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(wrapper.vm.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
    });

    it('应该正确格式化小数', () => {
      expect(wrapper.vm.formatFileSize(1536)).toBe('1.5 KB');
      expect(wrapper.vm.formatFileSize(1024 * 1024 * 1.5)).toBe('1.5 MB');
    });
  });

  describe('计算属性', () => {
    it('应该正确计算是否可以上传', () => {
      expect(wrapper.vm.canUpload).toBe(true);
      
      wrapper.vm.uploading = true;
      expect(wrapper.vm.canUpload).toBe(false);
    });
  });
});

describe('communicationMixin', () => {
  let wrapper;

  const TestComponent = {
    mixins: [communicationMixin],
    template: '<div></div>'
  };

  beforeEach(() => {
    wrapper = mount(TestComponent, { localVue });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  describe('事件发送', () => {
    it('应该能够发送事件到父组件', () => {
      wrapper.vm.emitToParent('test-event', { data: 'test' });
      
      expect(wrapper.emitted('test-event')).toBeTruthy();
      expect(wrapper.emitted('test-event')[0]).toEqual([{ data: 'test' }]);
    });

    it('应该能够发送全局事件', () => {
      const globalEmitSpy = jest.spyOn(wrapper.vm.$root, '$emit');
      
      wrapper.vm.emitGlobal('global-event', { data: 'global' });
      
      expect(globalEmitSpy).toHaveBeenCalledWith('global-event', { data: 'global' });
    });

    it('应该能够向子组件广播事件', () => {
      const mockChild = { $emit: jest.fn() };
      wrapper.vm.$children = [mockChild];
      
      wrapper.vm.broadcastToChildren('child-event', { data: 'child' });
      
      expect(mockChild.$emit).toHaveBeenCalledWith('child-event', { data: 'child' });
    });
  });

  describe('事件监听', () => {
    it('应该能够监听全局事件', () => {
      const handler = jest.fn();
      const globalOnSpy = jest.spyOn(wrapper.vm.$root, '$on');
      
      wrapper.vm.listenGlobal('global-event', handler);
      
      expect(globalOnSpy).toHaveBeenCalledWith('global-event', handler);
      expect(wrapper.vm.eventListeners).toHaveLength(1);
    });

    it('应该能够监听父组件事件', () => {
      const handler = jest.fn();
      const mockParent = { $on: jest.fn() };
      wrapper.vm.$parent = mockParent;
      
      wrapper.vm.listenParent('parent-event', handler);
      
      expect(mockParent.$on).toHaveBeenCalledWith('parent-event', handler);
      expect(wrapper.vm.eventListeners).toHaveLength(1);
    });
  });

  describe('子组件方法调用', () => {
    it('应该能够调用子组件方法', () => {
      const mockMethod = jest.fn().mockReturnValue('result');
      const mockChild = { testMethod: mockMethod };
      wrapper.vm.$refs = { testChild: mockChild };
      
      const result = wrapper.vm.callChildMethod('testChild', 'testMethod', 'arg1', 'arg2');
      
      expect(mockMethod).toHaveBeenCalledWith('arg1', 'arg2');
      expect(result).toBe('result');
    });

    it('应该在子组件或方法不存在时给出警告', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      wrapper.vm.$refs = {};
      
      wrapper.vm.callChildMethod('nonexistent', 'method');
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Child component nonexistent or method method not found'
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('Vuex 集成', () => {
    it('应该能够提交 mutation', () => {
      const commitSpy = jest.spyOn(wrapper.vm.$store, 'commit');
      
      wrapper.vm.commitMutation('TEST_MUTATION', { data: 'test' });
      
      expect(commitSpy).toHaveBeenCalledWith('TEST_MUTATION', { data: 'test' });
    });

    it('应该能够分发 action', async () => {
      const dispatchSpy = jest.spyOn(wrapper.vm.$store, 'dispatch');
      
      await wrapper.vm.dispatchAction('TEST_ACTION', { data: 'test' });
      
      expect(dispatchSpy).toHaveBeenCalledWith('TEST_ACTION', { data: 'test' });
    });

    it('应该能够订阅 store 变化', () => {
      const handler = jest.fn();
      const watchSpy = jest.spyOn(wrapper.vm.$store, 'watch').mockReturnValue(() => {});
      
      wrapper.vm.subscribeStore('user.name', handler);
      
      expect(watchSpy).toHaveBeenCalled();
      expect(wrapper.vm.subscriptions).toHaveLength(1);
    });
  });

  describe('资源清理', () => {
    it('应该在组件销毁时清理事件监听器', () => {
      const mockTarget = { $off: jest.fn() };
      const handler = jest.fn();
      
      wrapper.vm.eventListeners.push({
        target: mockTarget,
        event: 'test-event',
        handler
      });
      
      wrapper.vm.cleanupCommunication();
      
      expect(mockTarget.$off).toHaveBeenCalledWith('test-event', handler);
      expect(wrapper.vm.eventListeners).toHaveLength(0);
    });

    it('应该在组件销毁时清理订阅', () => {
      const unsubscribe = jest.fn();
      wrapper.vm.subscriptions.push(unsubscribe);
      
      wrapper.vm.cleanupCommunication();
      
      expect(unsubscribe).toHaveBeenCalled();
      expect(wrapper.vm.subscriptions).toHaveLength(0);
    });
  });
});

describe('styleMixin', () => {
  let wrapper;

  const TestComponent = {
    mixins: [styleMixin],
    template: '<div></div>',
    data() {
      return {
        componentName: 'test-component'
      };
    }
  };

  beforeEach(() => {
    wrapper = mount(TestComponent, {
      localVue,
      propsData: {
        size: 'large',
        theme: 'dark',
        disabled: true
      }
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  describe('基础类名', () => {
    it('应该生成正确的基础类名', () => {
      const classes = wrapper.vm.baseClasses;
      
      expect(classes).toContain('test-component');
      expect(classes).toContain('test-component--large');
      expect(classes).toContain('test-component--dark');
      expect(classes).toEqual(expect.arrayContaining([
        expect.objectContaining({
          'test-component--disabled': true
        })
      ]));
    });
  });

  describe('基础样式', () => {
    it('应该根据尺寸生成正确的样式', () => {
      const styles = wrapper.vm.baseStyles;
      
      expect(styles.fontSize).toBe('16px');
      expect(styles.padding).toBe('12px 16px');
    });

    it('应该根据主题生成正确的样式', () => {
      const styles = wrapper.vm.baseStyles;
      
      expect(styles.backgroundColor).toBe('#1f1f1f');
      expect(styles.color).toBe('#ffffff');
    });

    it('应该根据状态生成正确的样式', () => {
      const styles = wrapper.vm.baseStyles;
      
      expect(styles.opacity).toBe(0.6);
      expect(styles.cursor).toBe('not-allowed');
    });
  });

  describe('BEM 类名生成', () => {
    it('应该生成正确的 BEM 类名', () => {
      expect(wrapper.vm.bem()).toBe('test-component');
      expect(wrapper.vm.bem('header')).toBe('test-component__header');
      expect(wrapper.vm.bem('header', 'active')).toBe('test-component__header--active');
      expect(wrapper.vm.bem(null, 'disabled')).toBe('test-component--disabled');
    });
  });

  describe('类名合并', () => {
    it('应该正确合并不同类型的类名', () => {
      const result = wrapper.vm.mergeClasses(
        'string-class',
        ['array-class1', 'array-class2'],
        { 'object-class1': true, 'object-class2': false }
      );
      
      expect(result).toEqual([
        'string-class',
        'array-class1',
        'array-class2',
        'object-class1'
      ]);
    });
  });
});
