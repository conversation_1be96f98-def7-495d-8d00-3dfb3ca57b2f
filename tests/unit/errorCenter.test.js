/**
 * ErrorCenter 单元测试
 */

import { ErrorCenter, ERROR_TYPES, ERROR_LEVELS } from '@/utils/ErrorCenter.js';

describe('ErrorCenter', () => {
  beforeEach(() => {
    // 清理错误日志和处理器
    ErrorCenter.clearErrorLog();
    ErrorCenter.handlers.clear();
    
    // 重新注册默认处理器
    ErrorCenter.register(ERROR_TYPES.NETWORK_ERROR, (errorInfo) => {
      console.error('Network Error:', errorInfo);
    });
  });

  describe('错误处理器注册', () => {
    it('应该能够注册错误处理器', () => {
      const handler = jest.fn();
      ErrorCenter.register(ERROR_TYPES.VALIDATION_ERROR, handler);
      
      expect(ErrorCenter.handlers.has(ERROR_TYPES.VALIDATION_ERROR)).toBe(true);
      expect(ErrorCenter.handlers.get(ERROR_TYPES.VALIDATION_ERROR)).toBe(handler);
    });

    it('应该在注册非函数处理器时抛出错误', () => {
      expect(() => {
        ErrorCenter.register(ERROR_TYPES.VALIDATION_ERROR, 'not a function');
      }).toThrow('Error handler must be a function');
    });
  });

  describe('错误处理', () => {
    it('应该能够处理 Error 对象', () => {
      const handler = jest.fn();
      ErrorCenter.register(ERROR_TYPES.NETWORK_ERROR, handler);
      
      const error = new Error('Network connection failed');
      ErrorCenter.handle(error, 'test-context');
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Network connection failed',
          type: ERROR_TYPES.NETWORK_ERROR,
          context: 'test-context'
        }),
        'test-context',
        {}
      );
    });

    it('应该能够处理字符串错误', () => {
      const handler = jest.fn();
      ErrorCenter.register(ERROR_TYPES.UNKNOWN_ERROR, handler);
      
      ErrorCenter.handle('Simple error message', 'test-context');
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Simple error message',
          type: ERROR_TYPES.UNKNOWN_ERROR
        }),
        'test-context',
        {}
      );
    });

    it('应该能够处理对象错误', () => {
      const handler = jest.fn();
      ErrorCenter.register(ERROR_TYPES.VALIDATION_ERROR, handler);
      
      const errorObj = {
        type: ERROR_TYPES.VALIDATION_ERROR,
        message: 'Validation failed',
        field: 'email'
      };
      
      ErrorCenter.handle(errorObj, 'test-context');
      
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          type: ERROR_TYPES.VALIDATION_ERROR,
          message: 'Validation failed',
          field: 'email'
        }),
        'test-context',
        {}
      );
    });

    it('应该使用默认处理器处理未注册的错误类型', () => {
      const defaultHandlerSpy = jest.spyOn(ErrorCenter, 'defaultHandler');
      
      const error = new Error('Unknown error');
      ErrorCenter.handle(error, 'test-context');
      
      expect(defaultHandlerSpy).toHaveBeenCalled();
    });

    it('应该在处理器失败时使用默认处理器', () => {
      const faultyHandler = jest.fn().mockImplementation(() => {
        throw new Error('Handler failed');
      });
      ErrorCenter.register(ERROR_TYPES.NETWORK_ERROR, faultyHandler);
      
      const defaultHandlerSpy = jest.spyOn(ErrorCenter, 'defaultHandler');
      
      const error = new Error('Network error');
      ErrorCenter.handle(error, 'test-context');
      
      expect(faultyHandler).toHaveBeenCalled();
      expect(defaultHandlerSpy).toHaveBeenCalled();
    });
  });

  describe('错误类型检测', () => {
    it('应该正确检测网络错误', () => {
      const networkError = new Error('Network connection failed');
      const errorType = ErrorCenter._detectErrorType(networkError);
      expect(errorType).toBe(ERROR_TYPES.NETWORK_ERROR);
    });

    it('应该正确检测 404 错误', () => {
      const notFoundError = new Error('Resource not found');
      notFoundError.status = 404;
      const errorType = ErrorCenter._detectErrorType(notFoundError);
      expect(errorType).toBe(ERROR_TYPES.RESOURCE_NOT_FOUND);
    });

    it('应该正确检测权限错误', () => {
      const permissionError = new Error('Permission denied');
      permissionError.status = 403;
      const errorType = ErrorCenter._detectErrorType(permissionError);
      expect(errorType).toBe(ERROR_TYPES.PERMISSION_DENIED);
    });

    it('应该正确检测验证错误', () => {
      const validationError = new Error('Invalid input data');
      const errorType = ErrorCenter._detectErrorType(validationError);
      expect(errorType).toBe(ERROR_TYPES.VALIDATION_ERROR);
    });

    it('应该正确检测地图错误', () => {
      const mapError = new Error('BMap initialization failed');
      const errorType = ErrorCenter._detectErrorType(mapError);
      expect(errorType).toBe(ERROR_TYPES.MAP_LOAD_ERROR);
    });

    it('应该正确检测解析错误', () => {
      const parseError = new Error('JSON parse error');
      const errorType = ErrorCenter._detectErrorType(parseError);
      expect(errorType).toBe(ERROR_TYPES.DATA_PARSE_ERROR);
    });

    it('应该对未知错误返回默认类型', () => {
      const unknownError = new Error('Some unknown error');
      const errorType = ErrorCenter._detectErrorType(unknownError);
      expect(errorType).toBe(ERROR_TYPES.UNKNOWN_ERROR);
    });
  });

  describe('错误日志管理', () => {
    it('应该记录错误日志', () => {
      const error = new Error('Test error');
      ErrorCenter.handle(error, 'test-context');
      
      const logs = ErrorCenter.getErrorLog();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toEqual(expect.objectContaining({
        message: 'Test error',
        context: 'test-context'
      }));
    });

    it('应该限制错误日志大小', () => {
      // 添加超过最大限制的错误
      for (let i = 0; i < 150; i++) {
        ErrorCenter.handle(new Error(`Error ${i}`), 'test');
      }
      
      const logs = ErrorCenter.getErrorLog();
      expect(logs.length).toBeLessThanOrEqual(ErrorCenter.maxLogSize);
    });

    it('应该能够获取指定数量的错误日志', () => {
      for (let i = 0; i < 20; i++) {
        ErrorCenter.handle(new Error(`Error ${i}`), 'test');
      }
      
      const logs = ErrorCenter.getErrorLog(5);
      expect(logs).toHaveLength(5);
    });

    it('应该能够清除错误日志', () => {
      ErrorCenter.handle(new Error('Test error'), 'test');
      expect(ErrorCenter.getErrorLog()).toHaveLength(1);
      
      ErrorCenter.clearErrorLog();
      expect(ErrorCenter.getErrorLog()).toHaveLength(0);
    });
  });

  describe('错误创建', () => {
    it('应该能够创建标准错误对象', () => {
      const error = ErrorCenter.createError(
        ERROR_TYPES.VALIDATION_ERROR,
        'Field is required',
        { field: 'email' }
      );
      
      expect(error).toEqual(expect.objectContaining({
        type: ERROR_TYPES.VALIDATION_ERROR,
        message: 'Field is required',
        level: ERROR_LEVELS.ERROR,
        context: { field: 'email' },
        timestamp: expect.any(String)
      }));
    });
  });

  describe('函数包装', () => {
    it('应该能够包装异步函数', async () => {
      const asyncFn = jest.fn().mockResolvedValue('success');
      const wrappedFn = ErrorCenter.wrapAsync(asyncFn, 'test-context');
      
      const result = await wrappedFn('arg1', 'arg2');
      
      expect(result).toBe('success');
      expect(asyncFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('应该能够处理异步函数错误', async () => {
      const asyncFn = jest.fn().mockRejectedValue(new Error('Async error'));
      const wrappedFn = ErrorCenter.wrapAsync(asyncFn, 'test-context');
      
      await expect(wrappedFn()).rejects.toThrow('Async error');
      
      const logs = ErrorCenter.getErrorLog();
      expect(logs).toHaveLength(1);
      expect(logs[0].message).toBe('Async error');
    });

    it('应该能够包装同步函数', () => {
      const syncFn = jest.fn().mockReturnValue('success');
      const wrappedFn = ErrorCenter.wrapSync(syncFn, 'test-context');
      
      const result = wrappedFn('arg1', 'arg2');
      
      expect(result).toBe('success');
      expect(syncFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('应该能够处理同步函数错误', () => {
      const syncFn = jest.fn().mockImplementation(() => {
        throw new Error('Sync error');
      });
      const wrappedFn = ErrorCenter.wrapSync(syncFn, 'test-context');
      
      expect(() => wrappedFn()).toThrow('Sync error');
      
      const logs = ErrorCenter.getErrorLog();
      expect(logs).toHaveLength(1);
      expect(logs[0].message).toBe('Sync error');
    });

    it('应该支持不重新抛出错误的选项', async () => {
      const asyncFn = jest.fn().mockRejectedValue(new Error('Async error'));
      const wrappedFn = ErrorCenter.wrapAsync(asyncFn, 'test-context', { rethrow: false });
      
      const result = await wrappedFn();
      expect(result).toBeUndefined();
      
      const logs = ErrorCenter.getErrorLog();
      expect(logs).toHaveLength(1);
    });
  });

  describe('默认处理器', () => {
    beforeEach(() => {
      // 模拟 window.$message
      global.window = {
        $message: {
          error: jest.fn()
        }
      };
    });

    it('应该显示用户友好的错误消息', () => {
      const errorInfo = {
        type: ERROR_TYPES.NETWORK_ERROR,
        message: 'Network failed'
      };
      
      ErrorCenter.defaultHandler(errorInfo, 'test-context');
      
      expect(window.$message.error).toHaveBeenCalledWith('网络连接失败，请检查网络设置');
    });

    it('应该在静默模式下不显示消息', () => {
      const errorInfo = {
        type: ERROR_TYPES.NETWORK_ERROR,
        message: 'Network failed'
      };
      
      ErrorCenter.defaultHandler(errorInfo, 'test-context', { silent: true });
      
      expect(window.$message.error).not.toHaveBeenCalled();
    });

    it('应该在禁用上报时不上报错误', () => {
      const reportSpy = jest.spyOn(ErrorCenter, '_reportError');
      
      const errorInfo = {
        type: ERROR_TYPES.NETWORK_ERROR,
        message: 'Network failed'
      };
      
      ErrorCenter.defaultHandler(errorInfo, 'test-context', { report: false });
      
      expect(reportSpy).not.toHaveBeenCalled();
    });
  });
});
