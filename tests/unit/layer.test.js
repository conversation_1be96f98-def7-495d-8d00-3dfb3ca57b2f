/**
 * Layer 类单元测试
 */

import Layer from '@/model/layer/index.js';
import LayerDataManager from '@/model/layer/LayerDataManager.js';
import LayerStyleManager from '@/model/layer/LayerStyleManager.js';
import LayerEventManager from '@/model/layer/LayerEventManager.js';
import LayerRenderer from '@/model/layer/LayerRenderer.js';

describe('Layer', () => {
  let layer;
  let mockMap;

  beforeEach(() => {
    mockMap = createMockMap();
    layer = new Layer(mockMap, {
      id: 'test-layer',
      isStatic: true
    });
  });

  afterEach(() => {
    if (layer) {
      layer.destroy();
    }
  });

  describe('构造函数', () => {
    it('应该正确初始化 Layer 实例', () => {
      expect(layer.id).toBe('test-layer');
      expect(layer.Map).toBe(mockMap._map);
      expect(layer._map).toBe(mockMap);
      expect(layer.hide).toBe(false);
      expect(layer.isSelected).toBe(false);
      expect(layer.markers).toEqual([]);
      expect(layer.polygons).toEqual([]);
      expect(layer.polylines).toEqual([]);
    });

    it('应该创建所有管理器实例', () => {
      expect(layer.dataManager).toBeInstanceOf(LayerDataManager);
      expect(layer.styleManager).toBeInstanceOf(LayerStyleManager);
      expect(layer.eventManager).toBeInstanceOf(LayerEventManager);
      expect(layer.renderer).toBeInstanceOf(LayerRenderer);
    });

    it('应该使用默认参数', () => {
      const defaultLayer = new Layer(mockMap);
      expect(defaultLayer.id).toBeDefined();
      expect(defaultLayer.hide).toBe(false);
      expect(defaultLayer.isSelected).toBe(false);
      expect(defaultLayer.rightAction).toBe(true);
    });
  });

  describe('初始化', () => {
    it('应该成功初始化图层配置', async () => {
      const layerConfig = {
        type: 'marker',
        displayName: '测试图层',
        localData: [
          { id: 1, name: '点1', lng: 116.404, lat: 39.915 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ]
      };

      await layer.init(layerConfig);

      expect(layer.layerConfig).toBeDefined();
      expect(layer.type).toBe('marker');
    });

    it('应该在配置无效时抛出错误', async () => {
      const invalidConfig = {
        // 缺少必需的 type 字段
        displayName: '无效图层'
      };

      await expect(layer.init(invalidConfig)).rejects.toThrow();
    });
  });

  describe('数据管理', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '点1', lng: 116.404, lat: 39.915 },
          { id: 2, name: '点2', lng: 116.414, lat: 39.925 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ]
      };
      await layer.init(layerConfig);
    });

    it('应该能够加载数据', async () => {
      await layer.loadData();
      const data = layer.getResourceData();
      expect(Array.isArray(data)).toBe(true);
    });

    it('应该能够获取资源信息', () => {
      const resourceInfo = layer.getResourceInfo();
      expect(resourceInfo).toBeDefined();
    });

    it('应该能够获取分页参数', () => {
      const pageParam = layer.getPageParam();
      expect(pageParam).toBeDefined();
      expect(typeof pageParam).toBe('object');
    });
  });

  describe('样式管理', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        styleConfig: {
          iconType: 'default',
          default: {
            color: '#ff0000',
            size: 32
          }
        }
      };
      await layer.init(layerConfig);
    });

    it('应该能够获取 marker 配置', () => {
      const config = layer.markerConfig;
      expect(config).toBeDefined();
      expect(config.iconType).toBe('default');
    });

    it('应该能够设置 marker 配置', () => {
      const newConfig = {
        iconType: 'custom',
        custom: {
          color: '#00ff00',
          size: 24
        }
      };
      layer.markerConfig = newConfig;
      expect(layer.markerConfig.iconType).toBe('custom');
    });

    it('应该能够初始化 marker 配置', () => {
      const config = {
        iconType: 'icon',
        icon: {
          url: 'test.png',
          size: [32, 32]
        }
      };
      layer.initMarkerConifg(config);
      expect(layer.markerConfig.iconType).toBe('icon');
    });
  });

  describe('事件管理', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        localData: [{ id: 1, name: '点1', lng: 116.404, lat: 39.915 }]
      };
      await layer.init(layerConfig);
    });

    it('应该能够切换显示状态', () => {
      const initialHide = layer.hide;
      layer.changeShow();
      expect(layer.hide).toBe(!initialHide);
    });

    it('应该能够打开编辑弹窗', () => {
      const mockOverlay = { id: 1, type: 'marker' };
      layer.openEdit(true, mockOverlay);
      // 这里应该验证相关的 store 状态变化
    });
  });

  describe('渲染管理', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '点1', lng: 116.404, lat: 39.915 },
          { id: 2, name: '点2', lng: 116.414, lat: 39.925 }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' }
        ]
      };
      await layer.init(layerConfig);
    });

    it('应该能够获取所有覆盖物', () => {
      const covers = layer.getAllCover();
      expect(Array.isArray(covers)).toBe(true);
    });

    it('应该能够手动绘制覆盖物', () => {
      const markerData = {
        marker: {
          id: 'manual-1',
          name: '手动点',
          point: { lng: 116.404, lat: 39.915 },
          pointConfig: {}
        }
      };
      
      const result = layer.manualDraw('marker', markerData);
      expect(result).toBeDefined();
    });
  });

  describe('时间范围管理', () => {
    beforeEach(async () => {
      const layerConfig = {
        type: 'marker',
        localData: [
          { id: 1, name: '点1', lng: 116.404, lat: 39.915, time: '2023-01-01' },
          { id: 2, name: '点2', lng: 116.414, lat: 39.925, time: '2023-01-02' }
        ],
        mappingConfig: [
          { name: 'id', mapColumnName: 'id' },
          { name: 'name', mapColumnName: 'name' },
          { name: 'lng', mapColumnName: 'lng' },
          { name: 'lat', mapColumnName: 'lat' },
          { name: 'time', mapColumnName: 'time' }
        ]
      };
      await layer.init(layerConfig);
      await layer.loadData();
    });

    it('应该能够获取时间范围', () => {
      const timeRange = layer.getTimeRange();
      expect(Array.isArray(timeRange)).toBe(true);
    });

    it('应该能够根据时间范围过滤覆盖物', () => {
      const timeRange = ['2023-01-01', '2023-01-01'];
      layer.filterCoverByTimeRange(timeRange);
      // 验证过滤效果
    });
  });

  describe('销毁', () => {
    it('应该能够正确销毁图层', () => {
      const layerId = layer.id;
      layer.destroy();
      
      expect(layer.dataManager).toBeNull();
      expect(layer.styleManager).toBeNull();
      expect(layer.eventManager).toBeNull();
      expect(layer.renderer).toBeNull();
      expect(layer.layerConfig).toBeNull();
      expect(layer.Map).toBeNull();
      expect(layer._map).toBeNull();
    });

    it('应该能够移除图层', () => {
      layer.removeLayer();
      // 验证图层从地图中移除
    });
  });

  describe('错误处理', () => {
    it('应该在初始化失败时正确处理错误', async () => {
      const invalidConfig = null;
      await expect(layer.init(invalidConfig)).rejects.toThrow();
    });

    it('应该在加载数据失败时正确处理错误', async () => {
      // 模拟数据加载失败
      layer.renderer.loadLayerData = jest.fn().mockRejectedValue(new Error('加载失败'));
      
      await expect(layer.loadData()).rejects.toThrow('加载失败');
    });
  });
});
