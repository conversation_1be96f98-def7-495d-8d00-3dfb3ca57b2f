/**
 * 数据源单元测试
 */

import DataSourceFactory, { DATA_SOURCE_TYPES } from '@/model/datasource/DataSourceFactory.js';
import StaticDataSource from '@/model/datasource/StaticDataSource.js';
import DynamicDataSource from '@/model/datasource/DynamicDataSource.js';

describe('DataSourceFactory', () => {
  afterEach(() => {
    DataSourceFactory.destroyAll();
  });

  describe('工厂方法', () => {
    it('应该能够创建静态数据源', () => {
      const dataSource = DataSourceFactory.createStatic({
        id: 'test-static',
        localData: [
          { id: 1, name: '测试数据1' },
          { id: 2, name: '测试数据2' }
        ]
      });

      expect(dataSource).toBeInstanceOf(StaticDataSource);
      expect(dataSource.type).toBe(DATA_SOURCE_TYPES.STATIC);
      expect(dataSource.id).toBe('test-static');
    });

    it('应该能够创建动态数据源', () => {
      const dataSource = DataSourceFactory.createDynamic({
        id: 'test-dynamic',
        resourceId: 'test-resource'
      });

      expect(dataSource).toBeInstanceOf(DynamicDataSource);
      expect(dataSource.type).toBe(DATA_SOURCE_TYPES.DYNAMIC);
      expect(dataSource.id).toBe('test-dynamic');
    });

    it('应该在创建未知类型时抛出错误', () => {
      expect(() => {
        DataSourceFactory.create('unknown-type', {});
      }).toThrow('Unknown data source type: unknown-type');
    });
  });

  describe('实例管理', () => {
    it('应该能够获取数据源实例', () => {
      const dataSource = DataSourceFactory.createStatic({
        id: 'test-instance',
        localData: []
      });

      const retrieved = DataSourceFactory.getInstance('test-instance');
      expect(retrieved).toBe(dataSource);
    });

    it('应该能够销毁数据源实例', () => {
      const dataSource = DataSourceFactory.createStatic({
        id: 'test-destroy',
        localData: []
      });

      DataSourceFactory.destroyInstance('test-destroy');
      const retrieved = DataSourceFactory.getInstance('test-destroy');
      expect(retrieved).toBeNull();
    });

    it('应该能够获取统计信息', () => {
      DataSourceFactory.createStatic({ id: 'static1', localData: [] });
      DataSourceFactory.createStatic({ id: 'static2', localData: [] });
      DataSourceFactory.createDynamic({ id: 'dynamic1', resourceId: 'res1' });

      const stats = DataSourceFactory.getStats();
      expect(stats.totalInstances).toBe(3);
      expect(stats.byType.static).toBe(2);
      expect(stats.byType.dynamic).toBe(1);
    });
  });

  describe('配置验证', () => {
    it('应该验证静态数据源配置', () => {
      const validConfig = {
        localData: [{ id: 1, name: 'test' }]
      };
      const result = DataSourceFactory.validateConfig(DATA_SOURCE_TYPES.STATIC, validConfig);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该验证动态数据源配置', () => {
      const validConfig = {
        resourceId: 'test-resource'
      };
      const result = DataSourceFactory.validateConfig(DATA_SOURCE_TYPES.DYNAMIC, validConfig);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该检测无效配置', () => {
      const invalidConfig = {};
      const result = DataSourceFactory.validateConfig(DATA_SOURCE_TYPES.DYNAMIC, invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});

describe('StaticDataSource', () => {
  let dataSource;

  beforeEach(() => {
    dataSource = new StaticDataSource({
      id: 'test-static',
      localData: [
        { id: 1, name: '数据1', value: 100 },
        { id: 2, name: '数据2', value: 200 },
        { id: 3, name: '数据3', value: 150 }
      ]
    });
  });

  afterEach(() => {
    if (dataSource) {
      dataSource.destroy();
    }
  });

  describe('初始化', () => {
    it('应该正确初始化静态数据源', async () => {
      await dataSource.init();
      
      expect(dataSource.getStatus()).toBe('loaded');
      expect(dataSource.getData()).toHaveLength(3);
      expect(dataSource.getMetadata().totalCount).toBe(3);
    });

    it('应该提取字段信息', async () => {
      await dataSource.init();
      
      const metadata = dataSource.getMetadata();
      expect(metadata.fields).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ name: 'id' }),
          expect.objectContaining({ name: 'name' }),
          expect.objectContaining({ name: 'value' })
        ])
      );
    });
  });

  describe('数据操作', () => {
    beforeEach(async () => {
      await dataSource.init();
    });

    it('应该能够加载数据', async () => {
      const data = await dataSource.loadData();
      expect(data).toHaveLength(3);
      expect(data[0]).toEqual(expect.objectContaining({
        id: 1,
        name: '数据1',
        value: 100
      }));
    });

    it('应该能够添加数据', async () => {
      const newItem = { name: '新数据', value: 300 };
      const result = await dataSource.addData(newItem);
      
      expect(result).toEqual(expect.objectContaining({
        name: '新数据',
        value: 300
      }));
      expect(result.id).toBeDefined();
      expect(dataSource.getData()).toHaveLength(4);
    });

    it('应该能够更新数据', async () => {
      const updates = { name: '更新的数据1', value: 120 };
      const result = await dataSource.updateData(1, updates);
      
      expect(result).toEqual(expect.objectContaining({
        id: 1,
        name: '更新的数据1',
        value: 120
      }));
    });

    it('应该能够删除数据', async () => {
      const result = await dataSource.deleteData(2);
      
      expect(result).toEqual(expect.objectContaining({
        id: 2,
        name: '数据2',
        value: 200
      }));
      expect(dataSource.getData()).toHaveLength(2);
    });

    it('应该在删除不存在的数据时抛出错误', async () => {
      await expect(dataSource.deleteData(999)).rejects.toThrow('Data with id 999 not found');
    });
  });

  describe('数据查询', () => {
    beforeEach(async () => {
      await dataSource.init();
    });

    it('应该能够搜索数据', () => {
      const results = dataSource.searchData({ name: '数据1' });
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('数据1');
    });

    it('应该能够模糊搜索', () => {
      const results = dataSource.searchData({ name: '数据' });
      expect(results).toHaveLength(3);
    });

    it('应该能够分页获取数据', () => {
      const result = dataSource.getPagedData(1, 2);
      
      expect(result.data).toHaveLength(2);
      expect(result.total).toBe(3);
      expect(result.page).toBe(1);
      expect(result.pageSize).toBe(2);
      expect(result.totalPages).toBe(2);
    });

    it('应该能够获取第二页数据', () => {
      const result = dataSource.getPagedData(2, 2);
      
      expect(result.data).toHaveLength(1);
      expect(result.page).toBe(2);
    });
  });

  describe('数据过滤和排序', () => {
    beforeEach(async () => {
      await dataSource.init();
    });

    it('应该能够过滤数据', async () => {
      const data = await dataSource.loadData({
        filter: { value: 200 }
      });
      
      expect(data).toHaveLength(1);
      expect(data[0].value).toBe(200);
    });

    it('应该能够使用操作符过滤', async () => {
      const data = await dataSource.loadData({
        filter: {
          value: { operator: 'gt', value: 150 }
        }
      });
      
      expect(data).toHaveLength(1);
      expect(data[0].value).toBe(200);
    });

    it('应该能够排序数据', async () => {
      const data = await dataSource.loadData({
        sort: { field: 'value', order: 'desc' }
      });
      
      expect(data[0].value).toBe(200);
      expect(data[1].value).toBe(150);
      expect(data[2].value).toBe(100);
    });
  });

  describe('批量操作', () => {
    beforeEach(async () => {
      await dataSource.init();
    });

    it('应该能够执行批量操作', async () => {
      const operations = [
        { type: 'add', data: { name: '批量数据1', value: 400 } },
        { type: 'update', id: 1, data: { value: 110 } },
        { type: 'delete', id: 3 }
      ];
      
      const results = await dataSource.batchOperation(operations);
      
      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);
      expect(results[2].success).toBe(true);
      
      expect(dataSource.getData()).toHaveLength(3); // 原3个 + 1个新增 - 1个删除
    });
  });
});

describe('DynamicDataSource', () => {
  let dataSource;

  beforeEach(() => {
    dataSource = new DynamicDataSource({
      id: 'test-dynamic',
      resourceId: 'test-resource'
    });
  });

  afterEach(() => {
    if (dataSource) {
      dataSource.destroy();
    }
  });

  describe('初始化', () => {
    it('应该正确初始化动态数据源', async () => {
      // 模拟资源信息获取
      dataSource.resourceStore.dispatch = jest.fn().mockResolvedValue({
        resourceName: '测试资源',
        columns: [
          { name: 'id', displayName: 'ID' },
          { name: 'name', displayName: '名称' }
        ]
      });

      await dataSource.init();
      
      expect(dataSource.getStatus()).toBe('loaded');
      expect(dataSource.getMetadata().resourceName).toBe('测试资源');
    });

    it('应该在资源ID缺失时抛出错误', async () => {
      const invalidDataSource = new DynamicDataSource({
        id: 'invalid'
        // 缺少 resourceId
      });

      await expect(invalidDataSource.init()).rejects.toThrow();
    });
  });

  describe('数据加载', () => {
    beforeEach(async () => {
      dataSource.resourceStore.dispatch = jest.fn().mockResolvedValue({
        resourceName: '测试资源',
        columns: []
      });
      await dataSource.init();
    });

    it('应该能够加载数据', async () => {
      const mockData = [
        { id: 1, name: '远程数据1' },
        { id: 2, name: '远程数据2' }
      ];

      dataSource.resourceStore.dispatch = jest.fn().mockResolvedValue();
      dataSource.resourceStore.getters = {
        'data/resourceDataList': mockData
      };

      const data = await dataSource.loadData();
      expect(data).toEqual(mockData);
    });

    it('应该能够使用缓存', async () => {
      const mockData = [{ id: 1, name: '缓存数据' }];
      
      // 第一次加载
      dataSource.resourceStore.dispatch = jest.fn().mockResolvedValue();
      dataSource.resourceStore.getters = {
        'data/resourceDataList': mockData
      };
      
      await dataSource.loadData({ page: 1 });
      
      // 第二次加载相同参数，应该使用缓存
      const dispatchSpy = jest.spyOn(dataSource.resourceStore, 'dispatch');
      await dataSource.loadData({ page: 1 });
      
      // 验证第二次没有调用 API
      expect(dispatchSpy).not.toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    it('应该正确处理网络错误', async () => {
      const networkError = new Error('Network error');
      dataSource.resourceStore.dispatch = jest.fn().mockRejectedValue(networkError);

      await expect(dataSource.init()).rejects.toThrow('Network error');
      expect(dataSource.hasError()).toBe(true);
    });

    it('应该支持重试机制', async () => {
      let callCount = 0;
      dataSource.resourceStore.dispatch = jest.fn()
        .mockImplementation(() => {
          callCount++;
          if (callCount < 3) {
            return Promise.reject(new Error('Temporary error'));
          }
          return Promise.resolve([]);
        });

      dataSource.resourceStore.getters = {
        'data/resourceDataList': []
      };

      // 应该重试并最终成功
      const data = await dataSource.loadData();
      expect(data).toEqual([]);
      expect(callCount).toBe(3);
    });
  });
});
