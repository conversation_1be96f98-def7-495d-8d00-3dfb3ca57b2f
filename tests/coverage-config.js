/**
 * 测试覆盖率配置和监控
 */

/**
 * 覆盖率阈值配置
 */
export const COVERAGE_THRESHOLDS = {
  // 全局阈值
  global: {
    branches: 70,
    functions: 70,
    lines: 70,
    statements: 70
  },
  
  // 核心模块更高的阈值要求
  'src/model/layer/': {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80
  },
  
  'src/model/datasource/': {
    branches: 75,
    functions: 75,
    lines: 75,
    statements: 75
  },
  
  'src/utils/ErrorCenter.js': {
    branches: 85,
    functions: 85,
    lines: 85,
    statements: 85
  },
  
  'src/utils/ResourceManager.js': {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80
  },
  
  // Mixins 的阈值
  'src/mixins/': {
    branches: 75,
    functions: 75,
    lines: 75,
    statements: 75
  }
};

/**
 * 需要排除的文件模式
 */
export const COVERAGE_EXCLUDE_PATTERNS = [
  'src/main.js',
  'src/router/**',
  'src/**/*.config.js',
  'src/**/*.d.ts',
  'src/**/__tests__/**',
  'src/**/*.test.js',
  'src/**/*.spec.js',
  'tests/**',
  'node_modules/**',
  'coverage/**',
  'dist/**'
];

/**
 * 覆盖率报告配置
 */
export const COVERAGE_REPORTERS = [
  'text',           // 控制台文本报告
  'text-summary',   // 控制台摘要报告
  'lcov',          // LCOV 格式（用于 CI/CD）
  'html',          // HTML 报告
  'json',          // JSON 格式
  'cobertura'      // Cobertura XML 格式
];

/**
 * 覆盖率质量门禁
 */
export class CoverageGate {
  /**
   * 检查覆盖率是否达标
   * @param {Object} coverage 覆盖率数据
   * @returns {Object} 检查结果
   */
  static check(coverage) {
    const results = {
      passed: true,
      failures: [],
      warnings: [],
      summary: {}
    };

    // 检查全局覆盖率
    const globalResult = this.checkThreshold(
      coverage.total,
      COVERAGE_THRESHOLDS.global,
      'Global'
    );
    
    if (!globalResult.passed) {
      results.passed = false;
      results.failures.push(...globalResult.failures);
    }
    
    results.warnings.push(...globalResult.warnings);

    // 检查各模块覆盖率
    Object.keys(COVERAGE_THRESHOLDS).forEach(pattern => {
      if (pattern === 'global') return;
      
      const moduleFiles = this.getMatchingFiles(coverage, pattern);
      if (moduleFiles.length > 0) {
        const moduleCoverage = this.calculateModuleCoverage(moduleFiles);
        const moduleResult = this.checkThreshold(
          moduleCoverage,
          COVERAGE_THRESHOLDS[pattern],
          pattern
        );
        
        if (!moduleResult.passed) {
          results.passed = false;
          results.failures.push(...moduleResult.failures);
        }
        
        results.warnings.push(...moduleResult.warnings);
      }
    });

    // 生成摘要
    results.summary = this.generateSummary(coverage);

    return results;
  }

  /**
   * 检查单个阈值
   * @param {Object} coverage 覆盖率数据
   * @param {Object} threshold 阈值配置
   * @param {String} name 模块名称
   * @returns {Object} 检查结果
   */
  static checkThreshold(coverage, threshold, name) {
    const result = {
      passed: true,
      failures: [],
      warnings: []
    };

    const metrics = ['branches', 'functions', 'lines', 'statements'];
    
    metrics.forEach(metric => {
      const actual = coverage[metric]?.pct || 0;
      const required = threshold[metric] || 0;
      
      if (actual < required) {
        result.passed = false;
        result.failures.push(
          `${name}: ${metric} coverage ${actual}% is below threshold ${required}%`
        );
      } else if (actual < required + 10) {
        // 如果覆盖率接近阈值，给出警告
        result.warnings.push(
          `${name}: ${metric} coverage ${actual}% is close to threshold ${required}%`
        );
      }
    });

    return result;
  }

  /**
   * 获取匹配模式的文件
   * @param {Object} coverage 覆盖率数据
   * @param {String} pattern 文件模式
   * @returns {Array} 匹配的文件列表
   */
  static getMatchingFiles(coverage, pattern) {
    const files = Object.keys(coverage);
    return files.filter(file => file.includes(pattern));
  }

  /**
   * 计算模块覆盖率
   * @param {Array} files 文件列表
   * @returns {Object} 模块覆盖率
   */
  static calculateModuleCoverage(files) {
    // 这里需要根据实际的覆盖率数据结构来实现
    // 简化实现，实际应该聚合所有文件的覆盖率
    return {
      branches: { pct: 0 },
      functions: { pct: 0 },
      lines: { pct: 0 },
      statements: { pct: 0 }
    };
  }

  /**
   * 生成覆盖率摘要
   * @param {Object} coverage 覆盖率数据
   * @returns {Object} 摘要信息
   */
  static generateSummary(coverage) {
    const total = coverage.total || {};
    
    return {
      branches: total.branches?.pct || 0,
      functions: total.functions?.pct || 0,
      lines: total.lines?.pct || 0,
      statements: total.statements?.pct || 0,
      totalFiles: Object.keys(coverage).length - 1, // 排除 total
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 生成覆盖率报告
   * @param {Object} coverage 覆盖率数据
   * @returns {String} 报告内容
   */
  static generateReport(coverage) {
    const checkResult = this.check(coverage);
    const summary = checkResult.summary;
    
    let report = '\n=== 测试覆盖率报告 ===\n\n';
    
    // 总体覆盖率
    report += '总体覆盖率:\n';
    report += `  分支覆盖率: ${summary.branches}%\n`;
    report += `  函数覆盖率: ${summary.functions}%\n`;
    report += `  行覆盖率: ${summary.lines}%\n`;
    report += `  语句覆盖率: ${summary.statements}%\n`;
    report += `  总文件数: ${summary.totalFiles}\n\n`;
    
    // 质量门禁结果
    if (checkResult.passed) {
      report += '✅ 覆盖率质量门禁: 通过\n\n';
    } else {
      report += '❌ 覆盖率质量门禁: 失败\n\n';
      
      if (checkResult.failures.length > 0) {
        report += '失败项:\n';
        checkResult.failures.forEach(failure => {
          report += `  - ${failure}\n`;
        });
        report += '\n';
      }
    }
    
    // 警告信息
    if (checkResult.warnings.length > 0) {
      report += '⚠️  警告:\n';
      checkResult.warnings.forEach(warning => {
        report += `  - ${warning}\n`;
      });
      report += '\n';
    }
    
    // 改进建议
    report += this.generateImprovementSuggestions(summary);
    
    report += `报告生成时间: ${summary.timestamp}\n`;
    report += '========================\n';
    
    return report;
  }

  /**
   * 生成改进建议
   * @param {Object} summary 覆盖率摘要
   * @returns {String} 改进建议
   */
  static generateImprovementSuggestions(summary) {
    let suggestions = '📈 改进建议:\n';
    
    const metrics = [
      { key: 'branches', name: '分支覆盖率', value: summary.branches },
      { key: 'functions', name: '函数覆盖率', value: summary.functions },
      { key: 'lines', name: '行覆盖率', value: summary.lines },
      { key: 'statements', name: '语句覆盖率', value: summary.statements }
    ];
    
    const lowCoverage = metrics.filter(m => m.value < 70);
    const mediumCoverage = metrics.filter(m => m.value >= 70 && m.value < 85);
    
    if (lowCoverage.length > 0) {
      suggestions += '  优先改进 (< 70%):\n';
      lowCoverage.forEach(metric => {
        suggestions += `    - 提升${metric.name}: 当前 ${metric.value}%\n`;
      });
    }
    
    if (mediumCoverage.length > 0) {
      suggestions += '  持续改进 (70-85%):\n';
      mediumCoverage.forEach(metric => {
        suggestions += `    - 优化${metric.name}: 当前 ${metric.value}%\n`;
      });
    }
    
    if (lowCoverage.length === 0 && mediumCoverage.length === 0) {
      suggestions += '  🎉 覆盖率表现优秀！继续保持。\n';
    }
    
    suggestions += '\n';
    return suggestions;
  }
}

/**
 * 覆盖率监控钩子
 */
export class CoverageMonitor {
  /**
   * Jest 覆盖率钩子
   * @param {Object} results Jest 测试结果
   */
  static onCoverageComplete(results) {
    if (results.coverageMap) {
      const coverage = results.coverageMap.getCoverageSummary();
      const report = CoverageGate.generateReport(coverage);
      
      console.log(report);
      
      // 保存报告到文件
      const fs = require('fs');
      const path = require('path');
      
      const reportPath = path.join(process.cwd(), 'coverage', 'quality-gate-report.txt');
      fs.writeFileSync(reportPath, report);
      
      // 检查质量门禁
      const checkResult = CoverageGate.check(coverage);
      if (!checkResult.passed) {
        console.error('\n❌ 测试覆盖率未达到要求的阈值！');
        process.exit(1);
      }
    }
  }
}

export default {
  COVERAGE_THRESHOLDS,
  COVERAGE_EXCLUDE_PATTERNS,
  COVERAGE_REPORTERS,
  CoverageGate,
  CoverageMonitor
};
