/**
 * Jest 测试环境设置
 */

import Vue from 'vue';
import { config } from '@vue/test-utils';

// 全局配置
Vue.config.productionTip = false;

// 模拟全局对象
global.console = {
  ...console,
  // 在测试中静默某些日志
  warn: jest.fn(),
  error: jest.fn()
};

// 模拟 window 对象
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost',
    origin: 'http://localhost',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true
});

// 模拟 localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
global.sessionStorage = sessionStorageMock;

// 模拟 fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve('')
  })
);

// 模拟 IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// 模拟 ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// 模拟 MutationObserver
global.MutationObserver = class MutationObserver {
  constructor() {}
  observe() {}
  disconnect() {}
};

// 模拟 requestAnimationFrame
global.requestAnimationFrame = (callback) => {
  setTimeout(callback, 0);
};

global.cancelAnimationFrame = (id) => {
  clearTimeout(id);
};

// 模拟 URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

// 模拟 FileReader
global.FileReader = class FileReader {
  constructor() {
    this.readyState = 0;
    this.result = null;
    this.error = null;
  }
  
  readAsText(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'mock file content';
      if (this.onload) this.onload({ target: this });
    }, 0);
  }
  
  readAsDataURL(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ=';
      if (this.onload) this.onload({ target: this });
    }, 0);
  }
};

// 模拟百度地图 API
global.BMap = {
  Map: class MockMap {
    constructor() {
      this.overlays = [];
    }
    addOverlay(overlay) {
      this.overlays.push(overlay);
    }
    removeOverlay(overlay) {
      const index = this.overlays.indexOf(overlay);
      if (index > -1) {
        this.overlays.splice(index, 1);
      }
    }
    clearOverlays() {
      this.overlays = [];
    }
  },
  
  Marker: class MockMarker {
    constructor(point, options) {
      this.point = point;
      this.options = options;
    }
    setIcon() {}
    setLabel() {}
    show() {}
    hide() {}
  },
  
  Polygon: class MockPolygon {
    constructor(points, options) {
      this.points = points;
      this.options = options;
    }
    setStrokeColor() {}
    setFillColor() {}
    show() {}
    hide() {}
  },
  
  Polyline: class MockPolyline {
    constructor(points, options) {
      this.points = points;
      this.options = options;
    }
    setStrokeColor() {}
    show() {}
    hide() {}
  },
  
  Point: class MockPoint {
    constructor(lng, lat) {
      this.lng = lng;
      this.lat = lat;
    }
  },
  
  Icon: class MockIcon {
    constructor(url, size) {
      this.url = url;
      this.size = size;
    }
  },
  
  Label: class MockLabel {
    constructor(content, options) {
      this.content = content;
      this.options = options;
    }
    setContent() {}
    setStyle() {}
    show() {}
    hide() {}
  }
};

// 模拟 DaaS 组件库
jest.mock('@daas/components', () => ({
  DsDataQuery: {
    install: jest.fn()
  }
}));

jest.mock('@daas/surface-web-lib/resource', () => ({
  createStore: jest.fn(() => ({
    commit: jest.fn(),
    dispatch: jest.fn(() => Promise.resolve({})),
    getters: {
      'data/resourceDataList': [],
      'data/pageParam': { pageIndex: 1, limit: 100, recordTotal: 0 },
      'resourceInfo': { columns: [] }
    }
  }))
}));

// 配置 Vue Test Utils
config.mocks = {
  $store: {
    state: {},
    getters: {},
    commit: jest.fn(),
    dispatch: jest.fn(() => Promise.resolve())
  },
  $router: {
    push: jest.fn(),
    replace: jest.fn(),
    go: jest.fn(),
    back: jest.fn()
  },
  $route: {
    path: '/',
    params: {},
    query: {},
    hash: ''
  },
  $message: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn()
  }
};

// 全局测试工具函数
global.createMockLayer = (options = {}) => {
  return {
    id: 'test-layer',
    type: 'marker',
    markers: [],
    polygons: [],
    polylines: [],
    hide: false,
    isSelected: false,
    ...options
  };
};

global.createMockMap = () => {
  return {
    _map: new global.BMap.Map(),
    addOverlay: jest.fn(),
    removeOverlay: jest.fn(),
    clearOverlays: jest.fn()
  };
};

global.createMockDataSource = (type = 'static', data = []) => {
  return {
    id: 'test-datasource',
    type,
    data,
    status: 'loaded',
    init: jest.fn(() => Promise.resolve()),
    loadData: jest.fn(() => Promise.resolve(data)),
    addData: jest.fn(),
    updateData: jest.fn(),
    deleteData: jest.fn(),
    destroy: jest.fn()
  };
};

// 清理函数
afterEach(() => {
  jest.clearAllMocks();
  
  // 清理 localStorage
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  
  // 清理 sessionStorage
  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
  
  // 清理 fetch
  global.fetch.mockClear();
});
