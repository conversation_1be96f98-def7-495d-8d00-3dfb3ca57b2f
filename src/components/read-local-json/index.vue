<template>
  <a-upload :showUploadList="false" accept="json" :before-upload="handlerUpload">
    <span>上传图层数据</span>
  </a-upload>
</template>

<script>
import FileService from "@/utils/file-service";
import GeoUtil from "@/utils/Geo/GeoUtil";

export default {
  name: "ReadLocalJson",
  inject: ["loadingStart", "loadingDone"],
  data() {
    return {
      fileInfo: [],
    };
  },
  methods: {
    // 上传入口
    async handlerUpload(file) {
      this.loadingStart();
      // 上传文件
      const { id } = await this.uploadMindFile(file);
      // 读取文件
      const fileInfo = await this.getMapFileInfo(id);
      // 删除文件
      await FileService.deleteFile(id);

      this.$emit("fileChange", fileInfo);

      this.loadingDone();

      // 必须返回 false
      return false;
    },
    /**
     * 上传 JSON 文件到云端
     * @param {File} file 上传的 JSON 文件
     */
    async uploadMindFile(file) {
      if (!file) {
        this.$message.warning("请选择文件");
        return;
      }

      let fileName = "测试";
      if (!FileService.endWith(fileName, ".map")) {
        fileName = `${fileName}.map`;
      }

      const formData = new FormData();

      formData.append("file[]", file);
      formData.append("applicationType", "map");
      formData.append("builtIn", false);

      const res = await FileService.uploadFile(formData);
      return res[0];
    },
    /**
     * 通过 id 读取文件信息
     * @param {string | number} id 文件 id
     */
    async getMapFileInfo(id) {
      let res = await FileService.getFileInfo(id);
      res = typeof res !== "string" ? JSON.stringify(res) : res;
      res = res?.replace(/\/\/.+\n/g, "");
      try {
        res = JSON.parse(res);
        try {
          let geoUtil = new GeoUtil();
          res = await geoUtil.toRenderData(res);
        } catch (error) {
          // error
        }
      } catch (error) {
        this.$message.warning("上传的文件有错误，请检查后重试");
        res = [];
      }
      return res;
    },
  },
};
</script>
