<script>
import { defineComponent, ref } from "vue";
import { DsColorPicker } from "@daas/components";
import { watchEffect } from "vue";

export default defineComponent({
  components: { DsColorPicker },
  props: {
    modelValue: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "normal",
    },
  },
  model: {
    prop: "modelValue",
    event: "update:modelValue",
  },
  emits: ["update:modelValue", "change"],
  setup(props, { emit }) {
    const colorPickerRef = ref(null);
    const color = ref(props.modelValue);
    watchEffect(() => {
      color.value = props.modelValue;
    });

    const clearIcon = {
      template: `<svg t="1736214009125" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10081" width="200" height="200"><path d="M64 896h896v64H64zM936.05 425.18L663.45 152a80.52 80.52 0 0 0-56.74-24c-18.2 0-35.93 6.59-49.23 19.93L83.83 623.11c-28 28.07-26.13 75.83 4.11 106.14L190.48 832h450l299.69-300.68c27.98-28.07 26.12-75.83-4.12-106.14zM613.89 768H217l-83.78-84c-6-6.06-6.09-13.75-4.09-15.75L389.9 406.71l292.53 292.52z" p-id="10082"></path></svg>`,
    };

    const handleChange = (val) => {
      emit("update:modelValue", val);
      emit("change", val);
    };

    const handleClear = () => {
      colorPickerRef.value.clearValue();
    };

    return {
      color,
      colorPickerRef,
      clearIcon,
      handleChange,
      handleClear,
    };
  },
});
</script>

<template>
  <div class="color-picker-wrapper">
    <div :class="['color-picker-inner', `color-picker-inner-${size}`]">
      <DsColorPicker ref="colorPickerRef" :value="color" @change="handleChange" />
      <a-icon :component="clearIcon" style="font-size: 16px" @click="handleClear" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.color-picker {
  &-wrapper {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
  }

  &-inner {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
    margin-bottom: 3px;
    width: 100%;
    border: 1px solid var(--ds-border-color-base);
    padding-right: 4px;
    border-radius: var(--ds-border-radius-base);
    column-gap: 3px;

    :deep(.ds-color-picker__trigger) {
      border: 0;
      height: 100%;
      width: 100%;
      display: inline-block;
    }

    &-normal {
      height: 34px;
    }

    &-small {
      height: 26px;
    }
  }
}
</style>
