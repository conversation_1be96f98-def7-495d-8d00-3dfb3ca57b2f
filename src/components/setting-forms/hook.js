import { reactive, watch } from "vue";
import { merge } from "lodash-es";

export function useStyleConfig(baseForm = {}, { props, emit, initCallBack }) {
  // 表单相关数据
  const formData = reactive({
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    form: baseForm,
  });

  watch(
    () => props.config,
    (config) => {
      if (initCallBack) {
        formData.form = initCallBack(formData.form, config);
      } else {
        formData.form = merge(formData.form, config);
      }
    },
    {
      immediate: true,
    },
  );

  function styleChange(key, value) {
    formData.form[key] = value;
    emit("change", { [key]: value });
  }

  return {
    formData,
    styleChange,
  };
}
