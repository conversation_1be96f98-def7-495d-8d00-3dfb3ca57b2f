<script>
import { defineComponent, ref, toRefs } from "vue";
import ColorPicker from "@/components/color-picker/index.vue";
import SelectIcon from "@/components/select-icon/index.vue";
import { useStyleConfig } from "./hook";

export default defineComponent({
  components: { ColorPicker, SelectIcon },
  props: {
    config: {
      type: Object,
      required: true,
      default: {},
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const strokeStyleOptions = ref([
        { label: "实线", value: "solid" },
        { label: "虚线", value: "dashed" },
      ])
    const { formData, styleChange } = useStyleConfig({
        strokeColor: "#ff0000", // 线条颜色
        strokeOpacity: 1, // 线条透明度
        strokeWeight: 1, // 线条宽度
        strokeStyle: "solid", // 线条样式
    }, {
      props, 
      emit
    });

    return {
      ...toRefs(formData),
      strokeStyleOptions,
      styleChange,
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="线条颜色">
      <ColorPicker v-model="form.strokeColor" @change="(value) => styleChange('strokeColor', value)" />
    </a-form-model-item>
    <a-form-model-item label="线条样式">
      <a-select v-model="form.strokeStyle" :options="strokeStyleOptions" @change="(value) => styleChange('strokeStyle', value)" />
    </a-form-model-item>
    <a-form-model-item label="线条宽度">
      <a-input-number v-model="form.strokeWeight" style="width: 100%" :max="10" :min="1" @change="(value) => styleChange('strokeWeight', value)" />
    </a-form-model-item>
    <a-form-model-item label="线条透明度">
      <a-input-number
        v-model="form.strokeOpacity"
        style="width: 100%"
        :max="1"
        :min="0"
        :step="0.1"
        @change="(value) => styleChange('strokeOpacity', value)"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
