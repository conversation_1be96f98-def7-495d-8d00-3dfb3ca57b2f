<script>
import { defineComponent, reactive, toRefs } from "vue";
import ColorPicker from "@/components/color-picker/index.vue";
import { merge } from "lodash-es";
import { watch } from "vue";

export default defineComponent({
  components: { ColorPicker },
  props: {
    config: {
      type: Object,
      required: true,
      default: {},
    },
  },
  emits: ["change"],
  setup(props, { emit }) {
    // 表单相关数据
    const formData = reactive({
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      form: {
        fillColor: "#ff0000", // 填充颜色
        fillOpacity: 0.5, // 填充透明度
        strokeColor: "#ff0000", // 边框颜色
        strokeOpacity: 1, // 边框透明度
        strokeWeight: 1, // 边框宽度
        strokeStyle: "solid", // 边框样式
      },
      strokeStyleOptions: [
        { label: "实线", value: "solid" },
        { label: "虚线", value: "dashed" },
      ],
    });

    watch(
      () => props.config,
      (config) => {
        formData.form = merge(formData.form, config);
      },
      {
        immediate: true,
      },
    );

    function styleChange(key, value) {
      formData.form[key] = value;
      emit("change", { [key]: value });
    }

    return {
      ...toRefs(formData),
      styleChange,
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="填充颜色">
      <ColorPicker v-model="form.fillColor" @change="(color) => styleChange('fillColor', color)" />
    </a-form-model-item>
    <a-form-model-item label="填充透明度">
      <a-input-number
        v-model="form.fillOpacity"
        style="width: 100%"
        :max="1"
        :min="0"
        :step="0.1"
        @change="(value) => styleChange('fillOpacity', value)"
      />
    </a-form-model-item>
    <a-form-model-item label="边框颜色">
      <ColorPicker
        v-model="form.strokeColor"
        @change="(color) => styleChange('strokeColor', color)"
      />
    </a-form-model-item>
    <a-form-model-item label="边框样式">
      <a-select
        v-model="form.strokeStyle"
        :options="strokeStyleOptions"
        @change="(value) => styleChange('strokeStyle', value)"
      />
    </a-form-model-item>
    <a-form-model-item label="边框宽度">
      <a-input-number
        v-model="form.strokeWeight"
        style="width: 100%"
        :max="10"
        :min="1"
        @change="(value) => styleChange('strokeWeight', value)"
      />
    </a-form-model-item>
    <a-form-model-item label="边框透明度">
      <a-input-number
        v-model="form.strokeOpacity"
        style="width: 100%"
        :max="1"
        :min="0"
        :step="0.1"
        @change="(value) => styleChange('strokeOpacity', value)"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
