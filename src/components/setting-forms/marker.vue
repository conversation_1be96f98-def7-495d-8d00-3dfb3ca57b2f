<script>
import { defineComponent, toRefs } from "vue";
import { merge } from "lodash-es";
import { DsUpload } from "@daas/components";
import { ImageSelect } from "@daas/meta-web-lib";
import ColorPicker from "@/components/color-picker/index.vue";
import SelectIcon from "@/components/select-icon/index.vue";
import { getBase64ByUrl, getImageSize } from "@/utils/image-utils";
import { useStyleConfig } from "./hook";

export default defineComponent({
  components: { ColorPicker, SelectIcon, UploadFile: DsUpload.UploadFile, ImageSelect: ImageSelect.Picker },
  props: {
    config: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    // 使用样式配置钩子函数初始化表单数据
    const { formData } = useStyleConfig(
      {
        type: "svg", // 图标类型
        image: undefined, // 图标
        svgPath: undefined, // 图标
        scale: 10, // 图标大小
        fillColor: "#ff0000", // 填充颜色
        fillOpacity: 0.5, // 填充透明度
        strokeColor: "#ff0000", // 边框颜色
        strokeOpacity: 1, // 边框透明度
        strokeWeight: 1, // 边框宽度
      },
      { props, emit, initCallBack: (form, config) => {
          const currentConfig = config[config.iconType];
          return merge(form, currentConfig);
      } },
    );

    // 图片尺寸限制配置
    const imageOptions = {
      maxWidth: 52,
      maxHeight: 52,
    };

    /**
     * 图片变更处理函数
     * @param {string} value - 图片ID
     */
    async function imageChange(value) {
      // 获取上传基础URL
      const uploadBaseUrl = window.globalConfig?.uploadBaseUri || window.globalConfig?.uploadBaseUrl || "/daasMeta/attachment/download";
      const imageUrl = `${uploadBaseUrl}/${value}`;
      
      // 获取图片base64和尺寸信息
      const base64 = await getBase64ByUrl(imageUrl);
      let { width, height } = await getImageSize(base64);

      // 如果图片尺寸超过限制,按比例缩放
      if (width > imageOptions.maxWidth || height > imageOptions.maxHeight) {
        const scale = Math.min(imageOptions.maxWidth / width, imageOptions.maxHeight / height);
        width = width * scale;
        height = height * scale;
      }

      // 触发变更事件,更新图标配置
      emit('change', {
        iconType: "image",
        image: {
          imageId: value,
          imageUrl: imageUrl,
          imageSize: {
            width: width,
            height: height,
          },
          anchorSize: {
            width: width / 2,
            height: height,
          },
          visualWindowSize: {
            width: width,
            height: height,
          },
        }
      })
    }

    /**
     * 样式变更处理函数
     * @param {string} key - 样式属性名
     * @param {any} value - 样式属性值
     */
    function styleChange(key, value) {
      // 更新表单数据
      formData.form[key] = value;
      // 触发变更事件,更新SVG图标配置
      emit("change", {
        iconType: "svg",
        svg: {
          [key]: value,
        },
      });
    }

    return {
      ...toRefs(formData),
      imageChange,
      styleChange,
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="图标类型" prop="layerIcon">
      <a-select v-model="form.type" @change="(type) => styleChange('type', type)">
        <a-select-option value="svg">内置图标</a-select-option>
        <a-select-option value="image">自定义图标</a-select-option>
      </a-select>
    </a-form-model-item>

    <template v-if="form.type === 'svg'">
      <a-form-model-item label="图标" prop="layerIcon">
        <SelectIcon v-model="form.svgPath" @change="(svg) => styleChange('svgPath', svg)" />
      </a-form-model-item>
      <a-form-model-item label="图标大小">
        <a-input-number
          v-model="form.scale"
          style="width: 100%"
          :max="100"
          :min="20"
          @change="(size) => styleChange('scale', size)"
        />
      </a-form-model-item>
      <a-form-model-item label="填充颜色">
        <ColorPicker
          v-model="form.fillColor"
          @change="(color) => styleChange('fillColor', color)"
        />
      </a-form-model-item>
      <a-form-model-item label="填充透明度">
        <a-input-number
          v-model="form.fillOpacity"
          style="width: 100%"
          :max="1"
          :min="0"
          :step="0.1"
        />
      </a-form-model-item>
      <a-form-model-item label="边框颜色">
        <ColorPicker
          v-model="form.strokeColor"
          @change="(color) => styleChange('strokeColor', color)"
        />
      </a-form-model-item>
      <a-form-model-item label="边框宽度">
        <a-input-number
          v-model="form.strokeWeight"
          style="width: 100%"
          :max="10"
          :min="1"
          @change="(weight) => styleChange('strokeWeight', weight)"
        />
      </a-form-model-item>
      <a-form-model-item label="边框透明度">
        <a-input-number
          v-model="form.strokeOpacity"
          style="width: 100%"
          :max="1"
          :min="0"
          :step="0.1"
          @change="(opacity) => styleChange('strokeOpacity', opacity)"
        />
      </a-form-model-item>
    </template>

    <template v-else>
      <a-form-model-item label="图标" prop="image">
        <ImageSelect v-model="form.image" @change="(value) => imageChange(value)" />
      </a-form-model-item>
    </template>
  </a-form-model>
</template>

<style lang="less" scoped></style>
