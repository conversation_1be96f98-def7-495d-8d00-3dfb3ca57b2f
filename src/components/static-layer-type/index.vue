<template>
  <a-modal :visible="isShowSelectLayerTypeModal" :footer="null" @cancel="cancel">
    <h3>静态图层标记类型</h3>
    <div class="types-wrap">
      <div @click="handleCreateLayer('marker')">
        <img src="~@/assets/imgs/marker.png" class="type-wrap" />
        <div class="type-text">点</div>
      </div>
      <div @click="handleCreateLayer('polygon')">
        <img src="~@/assets/imgs/polygon.png" class="type-wrap" />
        <div class="type-text">面</div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "StaticLayerTypeModal",
  computed: {
    ...mapGetters("setting", ["isShowSelectLayerTypeModal"]),
  },
  methods: {
    handleCreateLayer(type) {
      this.$emit("select", type);
      this.cancel();
    },
    cancel() {
      this.$store.commit("setting/SET_SHOWSELECTOR_LAYER_TYPEMODAL", false);
    },
  },
};
</script>

<style lang="less" scoped>
.types-wrap {
  display: flex;
  justify-content: center;

  margin: 32px 0;
  cursor: pointer;

  & > div {
    width: 120px;
    height: 120px;
    border: 1px dashed #eee;
    & + div {
      margin-left: 42px;
    }
  }
  .type-wrap {
    width: 100%;
    height: 100%;
    padding: 8px;
    object-fit: contain;
  }
  .type-text {
    text-align: center;
  }
}
</style>
