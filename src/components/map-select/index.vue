<script>
import { defineComponent, ref } from "vue";
import <PERSON><PERSON> from "./json.js";
import { watchEffect } from "vue";

export default defineComponent({
  props: {
    modelValue: {
      type: String,
      default: "",
    },
  },
  model: {
    prop: "modelValue",
    event: "update:modelValue",
  },
  emits: ["update:modelValue", "change"],
  setup(props, { emit }) {
    const visible = ref(false);
    const selectedValue = ref(props.modelValue);
    watchEffect(() => {
      selectedValue.value = props.modelValue;
    });

    const handleSelect = (value) => {
      selectedValue.value = value;
      visible.value = false;
      emit("update:modelValue", value);
      emit("change", value);
    };

    return {
      visible,
      selectedValue,
      handleSelect,
      options: Json,
    };
  },
});
</script>

<template>
  <div class="map-select">
    <a-popover
      v-model="visible"
      trigger="click"
      placement="topRight"
      overlayClassName="map-select-popover"
    >
      <template #content>
        <div class="map-options">
          <div
            v-for="item in options"
            :key="item.value"
            class="map-option"
            :class="{ active: selectedValue === item.value }"
            @click="handleSelect(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </template>
      <span v-if="selectedValue" class="map-select-value">{{ selectedValue }}</span>
      <span v-else class="map-select-value map-select-value-placeholder">请选择</span>
    </a-popover>
  </div>
</template>

<style lang="less" scoped>
.map-select {
  display: inline-block;
  width: 100%;

  .map-select-value {
    display: inline-block;
    width: 100%;
    min-height: 22px;
    padding: 0 8px;
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    cursor: pointer;

    &:hover {
      border-color: #40a9ff;
    }

    &-placeholder {
      color: #d9d9d9;
    }
  }
}
</style>

<style lang="less">
.map-select-popover {
  .map-options {
    width: 408px;
    max-height: 200px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    column-gap: 4px;
    row-gap: 4px;
    padding: 12px;
  }

  .map-option {
    width: 72px;
    padding: 4px 4px;
    cursor: pointer;
    text-align: center;
    overflow-y: auto;

    &:hover {
      background-color: #f5f5f5;
    }

    &.active {
      color: #1890ff;
      background-color: #e6f7ff;
    }
  }

  .ant-popover-inner-content {
    padding: 0;
  }
}
</style>
