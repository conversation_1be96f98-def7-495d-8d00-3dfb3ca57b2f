// 导入地图配置数据和请求工具
import mapJson from "./json";
import { request } from "@daas/core";

// 将地图配置数据转换为以value为key的对象,方便快速查找
const nameMap = mapJson.reduce((acc, item) => {
  acc[item.value] = item;
  return acc;
}, {});

/**
 * 根据地图名称获取地图配置
 * @param {string} name 地图名称
 * @returns {object} 地图配置对象
 */
export function getMapConfigByName(name) {
  return nameMap[name] || mapJson.find((item) => item.value === name);
}

/**
 * 根据URL获取地图配置数据
 * @param {string} arg URL或地图名称
 * @returns {Promise<object>} 地图配置数据
 */
export async function getMapConfigByUrl(arg) {
  // 如果是静态资源路径,直接请求
  if (/^\/daas\/static/.test(arg)) {
    const res = await request.get(arg);
    return res;
  }

  // 如果是地图名称,先获取配置再请求文件
  const config = getMapConfigByName(arg);
  if (config && config.filePath) {
    return getMapConfigByUrl(config.filePath);
  }

  return null;
}
