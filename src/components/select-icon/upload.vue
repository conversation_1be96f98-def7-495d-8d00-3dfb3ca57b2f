<template>
  <div class="clearfix">
    <a-upload
      list-type="picture-card"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :showUploadList="showUploadList"
      @preview="handlePreview"
      @change="handleChange"
      v-bind="$attrs"
    >
      <div v-if="fileList.length < limit">
        <a-icon type="plus" />
        <div class="ant-upload-text">上传图片</div>
      </div>
    </a-upload>
    <a-modal
      width="100vw"
      :visible="previewVisible"
      :footer="null"
      @cancel="handleCancel"
    >
      <div class="preview-img" :style="previewStyle">
        <img alt="example" style="width: 100%" :src="previewImage" />
      </div>
    </a-modal>
  </div>
</template>

<script>
// import { isUrl } from "../utils";
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
export default {
  name: "UploadFile",
  props: {
    limit: {
      type: Number,
      default: 9,
    },
    previewStyle: {
      default: () => {},
      type: Object,
    },
    /**
     * 是否展示 uploadList, 可设为一个对象，用于单独设定 showPreviewIcon 和 showRemoveIcon
     *
     * @param {Boolean || { showPreviewIcon?: boolean, showRemoveIcon?: boolean }}
     * @default true
     */
    showUploadList: {
      type: [Boolean, Object],
      default: true,
    },
    /**
     * 自定义预览函数
     *
     * @default null
     */
    onCustomPreview: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      previewVisible: false,
      previewImage: "",
      fileList: [],
    };
  },
  methods: {
    initImages(images) {
      this.tmepImages = images;
      this.fileList = images.map((i) => {
        let temp = {
          uid: i,
          name: "image.png",
          status: "done",
        };
        temp.url = i;
        return temp;
      });
    },
    beforeUpload(file) {
      this.fileList = [...this.fileList, file];
      // this.$emit("change", this.fileList);
      return false;
    },
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      // 自定义预览函数
      if (this.onCustomPreview) {
        return await this.onCustomPreview(file);
      }

      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    handleChange({ fileList }) {
      this.fileList = fileList;
      this.$emit("change", fileList);
    },
    async handleUpload() {
      this.uploading = true;
      try {
        const { fileList } = this;
        const formData = new FormData();
        let uploadFlag = false;
        let results = [];

        formData.append("applicationType", "images");
        formData.append("builtIn", false);

        fileList.forEach((file) => {
          if (file.originFileObj) {
            formData.append("file[]", file.originFileObj);
            !uploadFlag && (uploadFlag = true);
          } else {
            results.push(file.uid);
          }
        });

        let res = uploadFlag
          ? await this.$http.post("/daasPortal/file/uploads", formData, {
              headers: {
                "content-type": "multipart/form-data",
              },
            })
          : [];

        res = res.map((i) => i.id);
        this.uploading = false;

        return results.concat(res);
      } catch (error) {
        console.error(`上传文件错误:${error}`);
        this.uploading = false;
        return null;
      }
    },
    clearImages() {
      this.fileList = [];
    },
  },
};
</script>

<style lang="less" scoped>
@import "~ant-design-vue/es/style/themes/default";
& /deep/ .@{ant-prefix}-modal {
  top: 0;
  height: 100vh;
  padding: 0;
  &-content {
    position: initial;
    background: transparent !important;
    box-shadow: none;
  }
}

.preview-img {
  position: absolute;
  top: 40%;
  left: 50%;
  padding: 16px;
  overflow: hidden;
  transform: translate(-50%, -50%);
  img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
