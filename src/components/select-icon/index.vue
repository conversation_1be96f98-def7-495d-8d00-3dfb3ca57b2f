<template>
  <div class="ds-icon-select">
    <!-- 展示 -->
    <a-tooltip
      overlayClassName="ds-icon-options"
      :trigger="['click']"
      v-model="showOptions"
      :overlayStyle="{ width: '400px' }"
      :placement="placement"
      @visibleChange="visibleChange"
    >
      <template slot="title">
        <div class="icon-options">
          <!-- 内置部分 -->
          <div class="icnos-options-wrap" v-for="i in resourceIcons" :key="i.name">
            <IconRender :svg="i.template" size="32px" @clickSvg="clickSvg" />
          </div>
        </div>
      </template>
      <div class="icon-select-content-wrap">
        <div v-if="currIcon" class="curr-icon" @click.stop="showOptionsFn">
          <!-- icon -->
          <IconRender v-if="isSvg" :svg="currIcon" :size="size" :color="color" />
          <!-- image -->
          <img v-else style="width: 100%; height: 100%" :src="imageUrl" />
        </div>
      </div>
    </a-tooltip>
  </div>
</template>

<script>
import { ref, computed, nextTick, watchEffect, defineComponent } from "vue";
import { resourceIcons, selectIcon } from "./svgs";
import IconRender from "./icon-render.vue";
import { isUrl } from "./helper";

export default defineComponent({
  components: {
    IconRender,
  },
  props: {
    size: {
      type: [String, Number],
      default: 32,
    },
    color: {
      type: String,
      default: "",
    },
    placement: {
      type: String,
      default: "right",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: "column",
    },
    svg: {
      type: String,
      default: selectIcon,
    },
  },
  data() {
    return {
      resourceIcons,
    };
  },
  model: {
    prop: "svg",
    event: "update:svg",
  },
  emits: ["update:svg", "change"],
  setup(props, { emit }) {
    // 点击自定义 icon
    const uploadFileRef = ref(null);
    const clickCustomIcon = async () => {
      const _res = await uploadFileRef.value.handleUpload();
      if (_res?.length) {
        clickSvg({
          svg: _res[0],
        });
      }
    };

    // 判断是否svg
    const isSvg = computed(() => {
      const reg = /^<svg([\s\S]*?)/i;
      return reg.test(currIcon.value);
    });
    // 当前地址
    const imageUrl = computed(() => {
      return isSvg.value
        ? currIcon.value
        : isUrl(currIcon.value)
        ? currIcon.value
        : currIcon.value.includes("/daasPortal/file/download/")
        ? currIcon.value
        : `/daasPortal/file/download/${currIcon.value}`;
    });
    // 下拉选项
    const showOptions = ref(false);
    const showOptionsFn = () => {
      if (props.disabled) return;
      showOptions.value = true;
      if (!isSvg.value) {
        nextTick(() => {
          uploadFileRef.value.initImages([imageUrl.value]);
        });
      }
    };
    const visibleChange = () => {
      if (props.disabled) {
        showOptions.value = false;
        return;
      }
      if (!showOptions.value) {
        uploadFileRef.value?.clearImages?.();
      }
    };

    // 当前图标
    const currIcon = ref(null);
    const clickSvg = ({ svg }) => {
      currIcon.value = svg;
      showOptions.value = false;
      emit("update:svg", svg);
      emit("change", svg);
    };
    watchEffect(() => {
      if (props.svg && props.svg !== currIcon.value) {
        currIcon.value = props.svg;
      }
    });

    // 计算字体大小
    const mySize = computed(() =>
      (props.size + "").includes("px") ? props.size : `${props.size}px`,
    );

    return {
      currIcon,
      clickSvg,
      showOptions,
      showOptionsFn,
      visibleChange,
      mySize,
      clickCustomIcon,
      uploadFileRef,
      isSvg,
      imageUrl,
    };
  },
});
</script>

<style lang="less" scoped>
.ds-icon-select {
  @size: v-bind(mySize);
  @padding: 4px;

  display: inline-block;
  .icon-select-content-wrap {
    display: inline-flex;
    flex-direction: v-bind(direction);
    align-items: center;
    justify-content: center;

    .curr-icon {
      flex: none;
      // width: calc(@size + @padding * 2);
      // height: calc(@size + @padding * 2);
      padding: @padding;
      font-size: 0;
      line-height: 1;
      border: 1px solid #eee;
      cursor: pointer;

      :deep(.icon-render) {
        pointer-events: none;
      }
    }

    :deep(.ant-btn) {
      padding: 0;
    }
  }
}
</style>

<style lang="less">
// !这个样式没有写 scoped，修改时必须加强关注是否会影响到其他页面样式
.ds-icon-options {
  .ant-tooltip-inner {
    width: 400px;
    background-color: #fff;
  }
  .icon-options {
    height: 200px;
    overflow-y: auto;
    .icnos-options-wrap {
      display: inline-block;
      margin: 2px;
      padding: 4px;
      font-size: 0px;
      line-height: 1;
      border: 1px solid #eee;
      cursor: pointer;
    }
  }
  .ds-icon-custom-upload {
    display: inline-block;
    width: 32px;
    height: 32px;
    .ant-upload-picture-card-wrapper {
      height: 100%;
    }
    .ant-upload {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      .ant-upload-text {
        display: none;
      }
    }

    .ant-upload-list-picture-card-container,
    .ant-upload-list-picture-card .ant-upload-list-item {
      width: 32px;
      height: 32px;
      margin: 0;
      padding: 0;
      border: none;
    }

    .ant-upload-list-item-info {
      & > span {
        padding: 1px;
      }
      &::before {
        display: none;
      }
    }
  }
}
</style>
