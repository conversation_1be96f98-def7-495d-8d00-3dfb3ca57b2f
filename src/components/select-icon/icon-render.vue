<script>
import { computed, defineComponent } from "vue";
import { isUrl } from "./helper";

export default defineComponent({
  name: "IconRender",
  emits: ["clickSvg"],
  props: {
    size: {
      type: [String, Number],
      default: 18,
    },
    color: {
      type: String,
      default: "#555",
    },
    svg: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
  },
  setup(props, { emit }) {
    // 判断是否svg
    const isSvg = computed(() => {
      const reg = /^<svg([\s\S]*?)/i;
      return reg.test(props.svg);
    });

    // 当前地址
    const icon = computed(() => {
      let _icon = props.svg;
      return isSvg.value
        ? _icon
        : isUrl(_icon)
        ? _icon
        : _icon.includes("/daasPortal/file/download/")
        ? _icon
        : `/daasPortal/file/download/${_icon}`;
    });

    const size = computed(() =>
      (props.size + "").includes("px") ? props.size : `${props.size}px`,
    );
    const color = computed(() => props.color);

    const clickSvg = () => {
      emit("clickSvg", { svg: props.svg, name: props.name });
    };

    return {
      isSvg,
      icon,
      size,
      color,
      clickSvg,
    };
  },
});
</script>

<template>
  <i v-if="isSvg" class="icon-render" @click="clickSvg" v-html="icon"></i>
  <img v-else class="icon-render icon-is-img" :src="icon" />
</template>

<style lang="less" scoped>
.icon-render {
  @size: v-bind(size);
  display: inline-block;
  width: @size;
  height: @size;
  color: v-bind(color);
  font-size: @size;
  line-height: 1;
  & > :deep(svg) {
    width: 1em;
    height: 1em;
    font-size: @size;
    fill: currentColor;
    transform: none;
  }
}
.icon-is-img {
  vertical-align: baseline;
}
</style>
