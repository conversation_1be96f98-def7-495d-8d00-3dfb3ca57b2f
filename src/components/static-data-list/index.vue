<template>
  <!--实例列表-->
  <div class="drawer-container">
    <div class="drawer-title">
      <a-tooltip placement="top">
        <template slot="title">
          <span>{{ title }}</span>
        </template>
        <span> 【{{ title }}】数据 </span>
      </a-tooltip>
      <a-tooltip placement="top">
        <template slot="title">
          <span>关闭</span>
        </template>
        <div class="drawer-close">
          <a-icon type="close" :style="{ fontSize: '18px' }" @click="onClose" />
        </div>
      </a-tooltip>
      <a-tooltip placement="top">
        <template slot="title">
          <span>刷新</span>
        </template>
        <div class="drawer-close">
          <a-icon type="sync" :style="{ fontSize: '18px' }" @click="onReflesh" />
        </div>
      </a-tooltip>
    </div>
    <div class="drawer-serach">
      <a-input-search
        placeholder="搜索实例"
        v-model="searchText"
        allow-clear
        @search="onSearchEntity"
      />
    </div>
    <div class="drawer-content">
      <div class="div-spinning" v-show="spinning">
        <a-spin :spinning="spinning" />
      </div>
      <div
        v-show="!spinning && (datas == null || datas.length <= 0)"
        class="bi-no-data"
        ref="dataListTipRef"
      >
        暂无数据
      </div>
      <ul>
        <li v-for="(item, index) in datas" :key="index" :title="item.name">
          <div class="ttt" @click="entityClick(item, $event)">
            <div class="div-img"></div>
            <div class="div-txt">
              {{ item.name }}
            </div>
          </div>
          <div class="entity-edit">
            <div class="div-more-menu">
              <a-dropdown placement="bottomLeft">
                <a-icon type="more" style="transform: rotate(90deg); width: 18px; height: 18px" />
                <a-menu slot="overlay" @click="operate(item, $event)">
                  <a-menu-item key="listEdit">
                    <a-icon type="edit" :style="{ fontSize: '16px' }" />
                    编辑
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "StaticDataList",
  props: {
    // 图层
    layer: { type: Object, default: () => [] },
  },
  mounted() {
    this.getInitData();
  },
  data() {
    return {
      spinning: false,
      datas: [],
      rawData: [],
      searchText: "",
      sourceObj: null,
    };
  },
  watch: {
    visibleEditStaticModal(flag) {
      if (this._watchForReflesh && !flag) {
        this.onReflesh();
        this._watchForReflesh = false;
      }
    },
    // 通过监听当前图层的数量变化来同步数据
    "layer.numberOfTags": {
      deep: true,
      handler() {
        this.onReflesh();
      },
    },
  },
  computed: {
    title() {
      return this.sourceObj ? this.sourceObj.mockData.displayName : "";
    },
    omittedTitle() {
      return this.title.length > 5 ? this.title.substr(0, 5) + "..." : this.title;
    },
    ...mapGetters("setting", ["visibleEditStaticModal"]),
  },
  methods: {
    async getInitData() {
      const { staticStore } = this.layer;
      this.sourceObj = staticStore;
      this.getDataList();
    },
    async getDataList() {
      this.rawData = [];

      this.spinning = true;
      this.$refs.dataListTipRef && (this.$refs.dataListTipRef.innerHTML = "暂无数据");

      // 如果有搜索内容，重置
      this.searchText = "";

      this.sourceObj.localData.forEach((i) => {
        this.rawData.push(i);
      });

      this.datas = this.rawData;

      this.spinning = false;
    },
    onClose() {
      this.$emit("onClose");
    },
    // 操作
    operate(item) {
      this._watchForReflesh = true;
      const _data = {
        layer: this.layer,
        id: item.id,
        extData: item,
      };
      this.$store.commit("setting/setUpdateModalData", _data);
      this.$store.commit("setting/setUpdateModalOpen", true);
    },
    // 点击
    entityClick(item) {
      this.$emit("onClick", {
        keyId: item.id,
      });
    },
    // 刷新
    onReflesh() {
      this.getDataList();
    },
    // 搜索
    onSearchEntity(value) {
      this.datas = this.rawData.filter((i) => {
        return i.name.toLowerCase().includes(value.toLowerCase());
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-container {
  width: 250px;
  min-width: 250px;
  height: 100%;
  background-color: @component-background;
  border-right: 1px solid @border-color-base;

  .drawer-title {
    float: left;
    width: 100%;
    height: 45px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 45px;
    border-bottom: 1px solid var(--ds-border-color-base);
  }
  .drawer-serach {
    float: left;
    width: 100%;
    height: 35px;
    padding: 0 5px;
    line-height: 35px;
  }
  .drawer-content {
    float: left;
    width: 100%;
    height: calc(100% - 120px);
    overflow-y: auto;
    ul {
      margin: 0;
      padding: 0;
    }
    li {
      position: relative;
      width: 100%;
      height: 35px;
      line-height: 35px;

      &:hover {
        background-color: @primary-1;

        .entity-edit {
          display: block;
        }
      }
    }
  }
  .drawer-loadmore {
    float: left;
    width: 100%;
    height: 40px;
    padding-top: 8px;
    line-height: 40px;
    text-align: center;
  }
  .drawer-close {
    float: right;
    width: 25px;
    height: 45px;
    padding-right: 10px;
    line-height: 45px;
    text-align: right;
    cursor: pointer;
  }
  .bi-no-data {
    width: 100%;
    height: 40px;
    color: var(--ds-text-color-4, var(--ds-disabled-color));
    line-height: 40px;
    text-align: center;
  }
  .ttt {
    position: relative;
    float: left;
    width: calc(100% - 40px);
    height: 35px;
    padding-left: 10px;
    line-height: 35px;

    cursor: pointer;

    .div-txt {
      float: left;
      width: 80%;
      margin-left: 35px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .div-img {
      position: absolute;
      top: 50%;
      left: 10px;
      float: left;
      width: 16px;
      height: 16px;
      margin: 0 10px;
      background-color: #29b6f6;
      border-radius: 15px;
      transform: translateY(-50%);
      img {
        width: 20px;
        height: 20px;
        vertical-align: top;
      }
    }
  }
  .div-more-menu {
    position: relative;
    float: left;
  }
  .div-spinning {
    width: 100%;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
}
</style>
