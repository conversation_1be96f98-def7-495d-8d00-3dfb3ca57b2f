<template>
  <a-modal
    v-bind="$attrs"
    :class="[modalClass, simpleClass]"
    :visible="visible"
    :footer="null"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <slot></slot>
    <div v-if="!title && title !== ''" slot="title">
      <slot name="title"></slot>
    </div>
  </a-modal>
</template>

<script>
const offsetY = 0;
const offsetX = 10;

let mouseDownX = 0;
let mouseDownY = 0;
let deltaX = 0;
let deltaY = 0;
let sumX = 0;
let sumY = 0;

let header = null; // 标题头部
let contain = null;
let modalContent = null;

let onmousedown = false;
export default {
  name: "DragModal",
  props: {
    // 容器的类名
    modalClass: {
      type: String,
      default: () => {
        return "modal-box";
      },
    },
    visible: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    title: {
      type: String,
      default: () => {
        return undefined;
      },
    },
    footer: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    initialPosition: {
      type: Object,
      default: () => ({
        x: 0,
        y: 0,
      }),
    },
  },
  data() {
    return {};
  },
  computed: {
    simpleClass() {
      return Math.random().toString(36).substring(2);
    },
  },
  watch: {
    visible() {
      this.$nextTick(() => {
        this.initialEvent(this.visible);
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initialEvent(this.visible);
    });
  },
  beforeDestroy() {
    this.removeMove();
    window.removeEventListener("mouseup", this.removeUp, false); // 移除鼠标按下需监听
  },
  methods: {
    // 确定按钮回调
    handleOk(e) {
      this.resetNum();
      this.$emit("ok", e);
    },
    // 取消按钮回调
    handleCancel(e) {
      this.resetNum();
      this.$emit("cancel", e);
    },
    // 弹窗的初始位置初始化
    resetNum() {
      mouseDownX = 0;
      mouseDownY = 0;
      deltaX = 0;
      deltaY = 0;
      sumX = 0;
      sumY = 0;
    },
    // 移动事件
    handleMove(event) {
      const delta1X = event.pageX - mouseDownX;
      const delta1Y = event.pageY - mouseDownY;
      deltaX = delta1X;
      deltaY = delta1Y;

      modalContent.style.transform = `translate(${delta1X + sumX + offsetX}px, ${
        delta1Y + sumY + offsetY
      }px)`;
    },
    // 弹窗初始化
    initialEvent(visible) {
      if (visible) {
        setTimeout(() => {
          window.removeEventListener("mouseup", this.removeUp, false);
          contain = document.getElementsByClassName(this.simpleClass)[0];
          header = contain.getElementsByClassName("ant-modal-header")[0];
          modalContent = contain.getElementsByClassName("ant-modal-content")[0];
          modalContent.style.left = 0;
          modalContent.style.transform = "translate(0px,0px)";
          header.style.cursor = "all-scroll";

          // 初始化弹窗位置
          const { x, y } = this.initialPosition;
          sumX = x;
          sumY = y;
          deltaX = x;
          deltaY = y;
          mouseDownX = x;
          mouseDownY = y;
          this.handleMove({
            pageX: this.initialPosition.x,
            pageY: this.initialPosition.y,
          });

          header.onmousedown = (e) => {
            onmousedown = true;
            mouseDownX = e.pageX;
            mouseDownY = e.pageY;
            document.body.onselectstart = () => false;
            window.addEventListener("mousemove", this.handleMove, false);
          };

          window.addEventListener("mouseup", this.removeUp, false);
        }, 0);
      }
    },
    // 鼠标停止
    removeMove() {
      window.removeEventListener("mousemove", this.handleMove, false);
    },
    // 鼠标抬起事件
    removeUp(e) {
      document.body.onselectstart = () => true;
      if (onmousedown && !(e.pageX === mouseDownX && e.pageY === mouseDownY)) {
        onmousedown = false;
        sumX = sumX + deltaX;
        sumY = sumY + deltaY;
      }
      this.removeMove();
    },
  },
};
</script>

<style lang="less" scoped>
&:deep(.ant-modal) {
  top: 0;
  margin: 0;
}
</style>
