<template>
  <a-spin :spinning="loading" style="height: 100%" tip="保存中...请稍后...">
    <div class="close-btn">
      <a-icon type="close" @click="closeModal" />
    </div>
    <div class="header-wrap">
      <span>【{{ displayName }}】样式</span>
    </div>
    <a-divider style="margin: 0"></a-divider>
    <div class="icon-config-wrap">
      <div class="action-wrap">
        <a-form-model>
          <a-form-model-item label="描边类型">
            <a-radio-group
              v-model="polygonConfig.strokeStyle"
              @change="(e) => updateIcon('strokeStyle', e.target.value)"
            >
              <a-radio value="solid"> 实线 </a-radio>
              <a-radio value="dashed"> 虚线 </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="线条粗细">
            <a-slider
              v-model="polygonConfig.strokeWeight"
              @change="(value) => updateIcon('strokeWeight', value)"
            />
          </a-form-model-item>
          <a-form-model-item label="填充透明度">
            <a-slider
              :max="1"
              :step="0.01"
              v-model="polygonConfig.fillOpacity"
              @change="(value) => updateIcon('fillOpacity', value)"
            />
          </a-form-model-item>
          <a-form-model-item label="线条透明度">
            <a-slider
              :max="1"
              :step="0.01"
              v-model="polygonConfig.strokeOpacity"
              @change="(value) => updateIcon('strokeOpacity', value)"
            />
          </a-form-model-item>
          <a-form-model-item label="填充颜色">
            <a-input
              type="color"
              v-model="polygonConfig.fillColor"
              @change="(e) => updateIcon('fillColor', e.target._value)"
            />
          </a-form-model-item>
          <a-form-model-item label="描边颜色">
            <a-input
              type="color"
              v-model="polygonConfig.strokeColor"
              @change="(e) => updateIcon('strokeColor', e.target._value)"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
      <div class="btns">
        <a-button style="margin-right: 16px" @click="resetConfig">重置</a-button>
      </div>
    </div>
  </a-spin>
</template>

<script>
import _debounce from "lodash/debounce";
import { mapGetters } from "vuex";
import { UpdateResourceData } from "@/services";
import { getSelectedCell } from "@/utils/map-utils";

export default {
  name: "IconConfigModal",
  data() {
    return {
      polygonConfig: {},
      showSlider: false,
      loading: false,
    };
  },
  watch: {
    currentLayer: {
      handler(layer) {
        this.init(layer);
      },
    },
  },
  computed: {
    ...mapGetters(["currentLayer"]),
    displayName() {
      return this.currentLayer?.layerConfig?.displayName || "";
    },
  },
  mounted() {
    this.init(this.currentLayer);
  },
  methods: {
    init(layer) {
      const { polygonConfig } = layer;
      const selectedCell = getSelectedCell();

      this.polygonConfig = JSON.parse(JSON.stringify(selectedCell?.options || polygonConfig));
    },
    // 更新 Icon
    updateIcon: _debounce(function (type, value) {
      if (!this.currentLayer) return;
      this._isChange = true;
      let config = {
        [type]: value,
      };

      const selectedCell = getSelectedCell();
      if (selectedCell) {
        selectedCell.updateStyle(config);
        selectedCell.createContextMenu(this);
        selectedCell.updateLabel({
          opacity: 1,
        });
      } else {
        this.currentLayer.updatePolygons(config);
      }
    }, 300),
    // 重置
    resetConfig() {
      const { id } = this.currentLayer;
      this.currentLayer.resetPolygonConfig(id);
      this._isChange = false;
      this.init(this.currentLayer);
    },
    // 保存配置
    async submitConfig() {
      this.loading = true;
      try {
        const {
          polygons,
          layerConfig: { mappingConfig, resourceId },
        } = this.currentLayer;
        const curr = mappingConfig.find((i) => i.name === "polygonConfig");
        if (!curr) {
          this.$message.warning("请映射配置的字段");
          this.loading = false;
          return;
        }
        const _config = JSON.stringify(this.polygonConfig);
        const { mapColumnName } = curr;
        const results = polygons.map((i) => {
          let _temp = {};
          let { extData } = i;
          for (let j in extData) {
            if (extData[j]) {
              _temp[j] = extData[j];
            }
          }
          extData[mapColumnName] = _config;
          _temp[mapColumnName] = _config;
          return _temp;
        });

        await UpdateResourceData({
          dataResourceId: resourceId,
          values: results,
        });

        this._isChange = false;
        this.currentLayer.updateCache(this.polygonConfig);
        this.$message.success("保存成功");
      } catch (error) {
        console.error(`保存配置错误:${error}`);
      }
      this.loading = false;
    },
    // 关闭当前弹窗
    closeModal() {
      this.$store.commit("setting/setShowConifgModal", false);
      // 关闭需要二次确认保存
      // this._isChange
      //   ? this.$store.dispatch("setting/closeConfigModalConfirm")
      //   : this.$store.commit("setting/setShowConifgModal", false);
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@styles/themes/default";

.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  cursor: pointer;
}

.header-wrap {
  padding: 8px 8px;
  font-weight: 600;
  font-size: 16px;
}

.icon-config-wrap {
  height: 100%;
  padding: 16px;

  .btns {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }

  .header-wrap {
    display: flex;
  }

  .action-wrap {
    width: 100%;
    height: calc(100% - 172px);
    overflow: hidden auto;
    :deep(.ant-form) {
      width: 100%;
      padding: 6px;
    }
  }
}
</style>
