<template>
  <a-spin :spinning="loading" style="height: 100%" tip="保存中...请稍后...">
    <div class="close-btn">
      <a-icon type="close" @click="closeModal" />
    </div>
    <div class="header-wrap">
      <span>【{{ displayName }}】样式</span>
    </div>
    <a-divider style="margin: 0"></a-divider>
    <div class="icon-config-wrap">
      <a-radio-group v-model="iconType" @change="iconTypeChange" style="margin-bottom: 8px">
        <a-radio-button value="svg"> 内置图标 </a-radio-button>
        <a-radio-button value="image"> 自定义图标 </a-radio-button>
      </a-radio-group>
      <div class="action-wrap" v-show="iconType === 'svg'">
        <h3>SVG 图标配置</h3>
        <a-form-model>
          <a-form-model-item label="图标类型">
            <IconSelect
              :svg-id="iconConfig.svgId"
              @change="(value) => updateIcon('svgPath', value)"
            />
          </a-form-model-item>
          <a-form-model-item label="图标大小" v-if="iconType === 'svg'">
            <a-slider
              :min="16"
              v-model="iconConfig.scale"
              @change="(value) => updateIcon('scale', value / 1000)"
            />
          </a-form-model-item>
          <a-form-model-item label="图标填充透明度" v-if="iconType === 'svg'">
            <a-slider
              :max="1"
              :min="0.03"
              :step="0.01"
              v-model="iconConfig.fillOpacity"
              @change="(value) => updateIcon('fillOpacity', value)"
            />
          </a-form-model-item>
          <a-form-model-item label="图标线条大小" v-if="iconType === 'svg'">
            <a-slider
              v-model="iconConfig.strokeWeight"
              @change="(value) => updateIcon('strokeWeight', value)"
            />
          </a-form-model-item>
          <a-form-model-item label="图标线条透明度" v-if="iconType === 'svg'">
            <a-slider
              :max="1"
              :step="0.01"
              v-model="iconConfig.strokeOpacity"
              @change="(value) => updateIcon('strokeOpacity', value)"
            />
          </a-form-model-item>
          <a-form-model-item label="填充颜色">
            <a-input
              type="color"
              v-model="iconConfig.fillColor"
              @change="(e) => updateIcon('fillColor', e.target._value)"
            />
          </a-form-model-item>
          <a-form-model-item label="描边颜色">
            <a-input
              type="color"
              v-model="iconConfig.strokeColor"
              @change="(e) => updateIcon('strokeColor', e.target._value)"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
      <div class="action-wrap" v-show="iconType === 'image'">
        <h3>Image 图标配置</h3>
        <a-form-model>
          <a-form-model-item label="自定义图标">
            <!-- <UploadFile
              ref="uploadFileRef"
              v-if="!resetImage"
              accept=".jpg, .jpeg, .png, .gif"
              :limit="1"
              :urls="[iconConfig.imageId]"
              @change="imageChange"
            /> -->
            <UploadFile
              ref="uploadFileRef"
              accept=".jpg, .jpeg, .png, .gif"
              :urls="[iconConfig.imageId]"
              :limit="1"
              @change="imageChange"
            />
          </a-form-model-item>
          <a-form-model-item
            label="宽度"
            v-if="iconType === 'image' && showSlider && iconConfig?.imageSize?.width"
          >
            <a-slider
              :min="16"
              :max="120"
              v-model="iconConfig.imageSize.width"
              @change="(value) => updateIcon('width', value)"
            />
          </a-form-model-item>
          <a-form-model-item
            label="高度"
            v-if="iconType === 'image' && showSlider && iconConfig?.imageSize?.height"
          >
            <a-slider
              :min="16"
              :max="120"
              v-model="iconConfig.imageSize.height"
              @change="(value) => updateIcon('height', value)"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
      <div class="btns">
        <a-button style="margin-right: 16px" @click="resetConfig">重置</a-button>
        <!-- <a-button type="primary" :loading="loading" @click="submitConfig">保存</a-button> -->
      </div>
    </div>
  </a-spin>
</template>

<script>
import _cloneDeep from "lodash/cloneDeep";
import _debounce from "lodash/debounce";
import { mapGetters } from "vuex";

import { _DEFAULT_IMAGE_CONFIG, _DEFAULT_SVG_CONFIG } from "@/model/icon";

import { DsUpload } from "@daas/components";
import IconSelect from "@/components/icon-select/svg-icon.vue";
import { UpdateResourceData } from "@/services";
import { getSelectedCell } from "@/utils/map-utils";

const _IMAGE_MAX_WIDTH = 80;
const _IMAGE_MIN_WIDTH = 24;

const _IMAGE_MAX_HEIGHT = 80;
const _IMAGE_MIN_HEIGHT = 24;

const _TYPE = {
  svg: _cloneDeep(_DEFAULT_SVG_CONFIG),
  image: _cloneDeep(_DEFAULT_IMAGE_CONFIG),
};

export default {
  name: "IconConfigModal",
  components: {
    IconSelect,
    UploadFile: DsUpload.UploadFile,
  },
  data() {
    return {
      iconType: "svg",
      iconConfig: {},
      showSlider: false,
      loading: false,

      resetImage: false,
    };
  },
  watch: {
    currentLayer: {
      handler(layer) {
        if (layer) {
          this.init(layer);
          this._tempConfig = {};
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["currentLayer"]),
    displayName() {
      return this.currentLayer?.layerConfig?.displayName || "";
    },
  },
  mounted() {
    this.init(this.currentLayer);
  },
  methods: {
    /**
     * 初始化配置
     *
     * @param {Layer} layer 图层
     */
    init(layer) {
      const { markerConfig } = layer;

      const { iconType } = markerConfig;
      const _config = markerConfig[iconType];
      this.iconType = iconType;

      if (_config && _config.scale < 1) _config.scale = _config.scale * 1000;

      this.initConfig(iconType, { ..._config });
    },
    // 初始化配置
    initConfig(type = "svg", config = null) {
      // 当前配置
      this.iconType = type;
      if (config) this.iconConfig = config;
      else this.iconConfig = this.getDefaultConfig(type);

      // 缓存
      this.cacheConfig(type, this.iconConfig);
      this._preType = type;

      this.$nextTick(() => {
        // 图片类型加载图片
        if (type === "image" && config?.imageId) {
          this.$refs.uploadFileRef.initImages([config.imageId]);
          this.picturesHaveBeenUploaded = true;
        }
      });
    },

    // 获取配置
    getConfig(type = "svg") {
      return this.iconConfig || this._tempConfig[type] || this.getDefaultConfig(type) || null;
    },
    // 缓存当前配置
    cacheConfig(type, config) {
      if (!this._tempConfig) {
        this._tempConfig = {
          [type]: config,
        };
      } else {
        this._tempConfig[type] = config;
      }
    },
    // 获取缓存的配置
    getCacheConfig(type) {
      return this._tempConfig[type] || null;
    },
    // 获取默认配置
    getDefaultConfig(type) {
      const config = _cloneDeep(_TYPE[type]);
      return config;
    },

    getBase64(imgs) {
      return new Promise((resolve) => {
        const reader = new FileReader();
        function success() {
          resolve(reader.result);
          reader.removeEventListener("load", success);
        }
        reader.addEventListener("load", success);
        reader.readAsDataURL(imgs[0].originFileObj);
        imgs.splice(0);
      });
    },

    // 加载图片的大小
    lodaImage(url) {
      return new Promise((resolve) => {
        const image = new Image();
        image.onload = () => {
          const { width, height } = image;

          const _width =
            width > _IMAGE_MAX_WIDTH
              ? _IMAGE_MAX_WIDTH
              : width < _IMAGE_MIN_WIDTH
              ? _IMAGE_MIN_WIDTH
              : width;
          const _height =
            height > _IMAGE_MAX_HEIGHT
              ? _IMAGE_MAX_HEIGHT
              : height < _IMAGE_MIN_HEIGHT
              ? _IMAGE_MIN_HEIGHT
              : height;

          image.width = _width;
          image.height = _height;

          resolve(image);
        };
        image.src = url;
      });
    },
    // 上传图片
    async imageChange(images) {
      // 删除或其他情况
      if (!images?.length) {
        this.resetConfig();
        return;
      }
      const base64 = await this.getBase64(images);
      await this.uploadFile(base64);
      if (!base64) return;
      this.showSlider = false;
      const { width, height } = await this.lodaImage(base64);
      const res = await this.uploadFile(base64);
      const regex = /^\/\w+(\/\w+)*(\?.*)?$/;
      const imagesId = res.id
        ? regex.test(res.id)
          ? res.id
          : `/daasPortal/file/download/${res.id}`
        : null;

      let _config = {
        imageId: imagesId,
        imageUrl: base64,
        imageSize: {
          width: width,
          height: height,
        },
        anchorSize: {
          width: width / 2,
          height: height,
        },
        visualWindowSize: {
          width: width,
          height: height,
        },
      };
      this.setUploadRefUrl(_config.imageId);
      this.iconConfig = _cloneDeep(_config);
      if (!this.picturesHaveBeenUploaded) {
        this._preType = "image";
        this._tempConfig[this._preType] = _cloneDeep(this.iconConfig);
      }
      this.picturesHaveBeenUploaded = true;
      this.iconTypeChange({ target: { value: "image" } });
    },

    // 上传封面图片
    async uploadFile(imgUrl) {
      function dataURLtoFile(dataUrl) {
        var arr = dataUrl.split(",");
        var mine = arr[0].match(/:(.*?);/)[1];
        var bstr = window.atob(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        let fileName = "file_" + new Date().getTime() + ".png";
        return new File([u8arr], fileName, { type: mine });
      }

      let formData = new FormData();
      let file = dataURLtoFile(imgUrl);
      formData.append("file", file);
      let res = await this.$http.post("/daasPortal/file/upload", formData);
      return res;
    },

    /**
     * 设置封面地址
     *
     * @param {String} url 地址
     */
    setUploadRefUrl(url) {
      if (!url) return;

      if (!url.includes("null") && this.$refs.uploadFileRef) {
        this.$refs.uploadFileRef.initImages([url]);
      }
    },

    // 更新 Icon
    updateIcon: _debounce(function (type, value) {
      if (!this.currentLayer) return;
      this._isChange = true;
      let config = {};

      if (type === "svgPath") {
        this.iconConfig.svgPath = value.template;
        this.iconConfig.svgId = value.id;
        this.$forceUpdate();
        config = {
          svgPath: value.template,
          svgId: value.id,
        };
      } else {
        config = {
          [type]: value,
        };
      }

      const selectedCell = getSelectedCell();
      if (selectedCell) {
        selectedCell.updateMarkerIcon(config);
      } else {
        this.currentLayer.updateMarkers(this.iconType, config);
      }
    }, 300),

    // 处理scale
    handleScale(scale) {
      if (scale && scale > 1) scale = scale / 1000;
      return scale;
    },

    // 切换 Icon 类型
    iconTypeChange({ target: { value } }) {
      if (value === "image" && !this.picturesHaveBeenUploaded) return;
      // 缓存 config,因为 value 是已经改变了的，所以用上一次的
      this.cacheConfig(this._preType, this.iconConfig);
      this._preType = value;

      // 先获取缓存中的，不存在则去拿默认的
      let _config = this.getCacheConfig(value);
      if (_config) {
        // if (_config.scale && _config.scale > 1) _config.scale = this.handleScale(_config.scale);
      } else {
        _config = this.getDefaultConfig(value);
      }

      this.iconConfig = _cloneDeep(_config);

      this._isChange = true;
      this.$nextTick(() => {
        this.currentLayer.changeMarkersType(value, _config);
        this.$nextTick(() => {
          this.showSlider = true;
        });
      });
    },
    // 保存配置
    async submitConfig() {
      this.loading = true;
      try {
        const {
          markers,
          layerConfig: { mappingConfig, resourceId },
        } = this.currentLayer;

        const curr = mappingConfig.find((i) => i.name === "pointConfig");
        if (!curr) {
          this.$message.warning("请映射配置的字段");
          this.loading = false;
          return;
        }
        const _config = JSON.stringify(this.iconConfig);
        const { mapColumnName } = curr;
        const results = markers.map((i) => {
          let _temp = {};
          let { extData } = i;
          for (let j in extData) {
            if (extData[j]) {
              _temp[j] = extData[j];
            }
          }
          _temp[mapColumnName] = _config;
          extData[mapColumnName] = _config;
          return _temp;
        });

        await UpdateResourceData({
          dataResourceId: resourceId,
          values: results,
        });

        this.currentLayer.updateCache({
          currIconType: this.iconType,
          [this.iconType]: this.iconConfig,
        });
        this._isChange = false;
        this.$message.success("保存成功");
      } catch (error) {
        console.error(`保存配置错误:${error}`);
      }
      this.loading = false;
    },
    // 关闭当前弹窗
    closeModal() {
      this.$store.commit("setting/setShowConifgModal", false);
      // 关闭不保存逻辑
      // this._isChange
      //   ? this.$store.dispatch("setting/closeConfigModalConfirm")
      //   : this.$store.commit("setting/setShowConifgModal", false);
    },
    // 重置配置
    resetConfig() {
      const { id } = this.currentLayer;
      this.currentLayer.resetMarkerConfig(id);
      this._isChange = false;

      // 清空自定义图标
      if (this.iconType === "image") {
        if (this.$refs.uploadFileRef && this.iconType === "image") {
          this.$refs.uploadFileRef.fileList = [];
        } else {
          this.resetImage = true;
        }
        this.picturesHaveBeenUploaded = false;
        if (this._tempConfig) delete this._tempConfig.image;
      }

      // 清空svg
      if (this.iconType === "svg") {
        if (this._tempConfig) delete this._tempConfig.svg;
      }

      const _config = this.getCacheConfig("svg") || this.getDefaultConfig("svg");
      // _config.scale = this.handleScale(_config.scale);
      this.iconConfig = _cloneDeep(_config);
      this.$nextTick(() => {
        this.currentLayer.changeMarkersType("svg", _config);
        this.resetImage = false;
        this._preType = "svg";
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@styles/themes/default";

.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  cursor: pointer;
}

.header-wrap {
  padding: 8px 8px;
  font-weight: 600;
  font-size: 16px;
}

.icon-config-wrap {
  height: 100%;
  padding: 16px;

  .btns {
    display: flex;
    justify-content: center;
  }

  .header-wrap {
    display: flex;
  }

  .action-wrap {
    width: 100%;
    height: calc(100% - 212px);
    overflow: hidden auto;

    :deep(.ant-form) {
      width: 100%;
      padding: 6px;
    }
  }
}
</style>
