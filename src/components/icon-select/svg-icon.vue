<template>
  <div class="icon-contaniner">
    <template v-for="(item, index) in svgs">
      <div
        :class="['icon-wrap', { selected: id === item.id }]"
        :key="`icon-${index}`"
        v-html="item.template"
        @click="select(item)"
      ></div>
    </template>
  </div>
</template>

<script>
import svgs from "./svg-config";
export default {
  name: "SvgIcon",
  props: {
    svgId: {
      type: String,
      default: "local",
    },
  },
  data() {
    return {
      svgs,
    };
  },
  computed: {
    id() {
      console.log("svg-id", this.svgId);
      return this.svgId;
    },
  },
  methods: {
    select(item) {
      this.$emit("change", item);
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@styles/themes/default";
.icon-contaniner {
  display: flex;

  .selected {
    background: @item-hover-bg;
    outline: 1px solid @border-color-base;
  }

  &:deep(.icon-wrap) {
    width: 40px;
    height: 40px;
    cursor: pointer;

    & + .icon-wrap {
      margin-left: 8px;
    }

    &:hover {
      background: @item-hover-bg;
      outline: 1px solid @border-color-base;
    }

    svg {
      width: 40px;
      height: 40px;
    }
  }
}
</style>
