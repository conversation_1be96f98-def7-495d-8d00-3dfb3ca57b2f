<template>
  <div class="icons-selsect">
    <div
      :class="['icon-row', `icon-row-${row<PERSON><PERSON>}`]"
      v-for="(row, row<PERSON>ey) in icnoConfig"
      :key="`row-${rowKey}`"
    >
      <div
        :class="['icon-col', `icon-row-${colKey}`]"
        v-for="(col, colKey) in row"
        :key="`col-${colKey}`"
        @click="selectIcon(col)"
      >
        <span
          class="icon-item"
          :style="{
            width: `${col.w}px`,
            height: `${col.h}px`,
            backgroundImage: `url(${iconSpritUrl})`,
            backgroundPosition: `-${col.l}px -${col.t}px`,
          }"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "IconSelect",
  data() {
    return {
      icnoConfig: [
        [
          {
            w: 12,
            h: 21,
            l: 0,
            t: 0,
          },
          {
            w: 12,
            h: 21,
            l: 23,
            t: 0,
          },
          {
            w: 12,
            h: 21,
            l: 46,
            t: 0,
          },
          {
            w: 12,
            h: 21,
            l: 69,
            t: 0,
          },
          {
            w: 12,
            h: 21,
            l: 92,
            t: 0,
          },
          {
            w: 12,
            h: 21,
            l: 115,
            t: 0,
          },
        ],
        [
          {
            w: 19,
            h: 25,
            l: 0,
            t: 21,
          },
          {
            w: 19,
            h: 25,
            l: 23,
            t: 21,
          },
          {
            w: 19,
            h: 25,
            l: 46,
            t: 21,
          },
          {
            w: 19,
            h: 25,
            l: 69,
            t: 21,
          },
          {
            w: 19,
            h: 25,
            l: 92,
            t: 21,
          },
          {
            w: 19,
            h: 25,
            l: 115,
            t: 21,
          },
        ],
        [
          {
            w: 18,
            h: 22,
            l: 0,
            t: 44,
          },
          {
            w: 18,
            h: 22,
            l: 23,
            t: 44,
          },
          {
            w: 18,
            h: 22,
            l: 46,
            t: 44,
          },
          {
            w: 18,
            h: 22,
            l: 69,
            t: 44,
          },
          {
            w: 18,
            h: 22,
            l: 92,
            t: 44,
          },
          {
            w: 18,
            h: 22,
            l: 115,
            t: 44,
          },
        ],
      ],
    };
  },
  computed: {
    iconSpritUrl() {
      return `${process.env.BASE_URL}static/icons/us_mk_icon.png`;
    },
  },
  methods: {
    // 选中一个icon
    selectIcon(item) {
      this.$emit("change", item);
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@styles/themes/default";
.icons-selsect {
  .icon-col {
    display: inline-block;
    width: 32px;
    height: 33px;
    text-align: center;
    cursor: pointer;

    .icon-item {
      display: inline-block;
      margin-top: 5px;
      overflow: hidden;
    }

    &:hover {
      background: @item-hover-bg;
      outline: 1px solid @border-color-base;
    }
  }
}
</style>
