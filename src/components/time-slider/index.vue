<script>
import { defineComponent, ref, onUnmounted, onMounted, computed, watchEffect } from "vue";

export default defineComponent({
  components: {},
  props: {
    timeRange: {
      type: Array,
      default: () => [],
    },
  },
  emits: ["change"],
  setup(props, { emit }) {
    // 时间点
    const times = ref(props.timeRange);
    // 滑块状态
    const isDragging = ref(false);
    // 当前拖动的滑块
    const currentHandle = ref(null);
    // 滑块位置
    const sliderLeft = ref(0);
    const sliderWidth = ref(100);
    // 容器引用
    const containerRef = ref(null);

    // 计算当前选中的时间范围
    const selectedTimeRange = computed(() => {
      if (!containerRef.value || !times.value.length) return [];

      const containerWidth = containerRef.value.getBoundingClientRect().width;
      const timeCount = times.value.length;
      const unitWidth = containerWidth / (timeCount - 1);

      // 计算左右滑块对应的时间索引
      const leftIndex = Math.round(sliderLeft.value / unitWidth);
      const rightIndex = Math.round((sliderLeft.value + sliderWidth.value) / unitWidth);

      return [
        times.value[Math.max(0, leftIndex)],
        times.value[Math.min(timeCount - 1, rightIndex)],
      ];
    });

    // 计算需要显示的时间点
    const visibleTimes = computed(() => {
      if (!times.value.length) return [];

      const maxVisibleMarks = 10; // 最大显示数量
      const totalTimes = times.value.length;

      if (totalTimes <= maxVisibleMarks) {
        return times.value;
      }

      // 计算间隔
      const interval = Math.ceil(totalTimes / maxVisibleMarks);

      return times.value.filter((_, index) => {
        // 始终保留第一个和最后一个时间点
        return index === 0 || index === totalTimes - 1 || index % interval === 0;
      });
    });

    // 监听时间范围变化
    watchEffect(() => {
      times.value = [
        "",
        ...new Set(
          props.timeRange
            .filter(Boolean)
            .sort((a, b) => new Date(a).valueOf() - new Date(b).valueOf()),
        ),
        "",
      ];
      if (containerRef.value) {
        sliderWidth.value = containerRef.value.getBoundingClientRect().width;
      }
    });

    // 监听时间范围变化
    const emitTimeRangeChange = () => {
      emit("change", selectedTimeRange.value);
    };

    // 计算滑块位置
    const calculateSliderPosition = (clientX) => {
      if (!containerRef.value) return;
      const rect = containerRef.value.getBoundingClientRect();
      const x = clientX - rect.left;
      const containerWidth = rect.width;

      if (currentHandle.value === "left") {
        // 左滑块拖动,限制最小宽度
        const minWidth = 24;
        const maxLeft = sliderLeft.value + sliderWidth.value - minWidth;
        const newLeft = Math.max(0, Math.min(x, maxLeft));
        const newWidth = sliderLeft.value + sliderWidth.value - newLeft;
        return { left: newLeft, width: newWidth };
      } else if (currentHandle.value === "right") {
        // 右滑块拖动,限制最小宽度和最大宽度
        const minWidth = 24;
        const maxWidth = containerWidth - sliderLeft.value;
        const newWidth = Math.max(minWidth, Math.min(x - sliderLeft.value, maxWidth));
        return { left: sliderLeft.value, width: newWidth };
      } else if (currentHandle.value === "slider") {
        // 整体滑块拖动,保持宽度不变
        const newLeft = Math.max(
          0,
          Math.min(x - sliderWidth.value / 2, containerWidth - sliderWidth.value),
        );
        return { left: newLeft, width: sliderWidth.value };
      }
    };

    // 开始拖动
    const handleMouseDown = (e, handle) => {
      e.preventDefault();
      isDragging.value = true;
      currentHandle.value = handle;
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    };

    // 拖动中
    const handleMouseMove = (e) => {
      if (!isDragging.value) return;

      const newPosition = calculateSliderPosition(e.clientX);
      if (newPosition) {
        sliderLeft.value = newPosition.left;
        sliderWidth.value = newPosition.width;
        emitTimeRangeChange();
      }
    };

    // 结束拖动
    const handleMouseUp = () => {
      isDragging.value = false;
      currentHandle.value = null;
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      emitTimeRangeChange();
    };

    // 组件挂载时，设置时间点
    onMounted(() => {
      sliderWidth.value = containerRef.value.getBoundingClientRect().width;
    });

    // 组件卸载时清理事件监听
    onUnmounted(() => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    });

    return {
      isDragging,
      times,
      visibleTimes,
      sliderLeft,
      sliderWidth,
      handleMouseDown,
      containerRef,
      selectedTimeRange,
    };
  },
});
</script>

<template>
  <!-- 滑轨 -->
  <div :class="['timeline-container', { 'timeline-dragging': isDragging }]" ref="containerRef">
    <!-- 时间点标记 -->
    <div class="timeline-marks">
      <div v-for="(time, index) in visibleTimes" :key="index" class="timeline-mark">
        <div class="timeline-mark-line"></div>
        <div class="timeline-mark-text">{{ time || "无限制" }}</div>
      </div>
    </div>
    <!-- 滑块 -->
    <div
      class="timeline-slider"
      :style="{
        left: sliderLeft + 'px',
        width: sliderWidth + 'px',
      }"
      @mousedown="(e) => handleMouseDown(e, 'slider')"
    >
      <!-- 左侧控制点 -->
      <div class="timeline-slider-left" @mousedown.stop="(e) => handleMouseDown(e, 'left')" />
      <!-- 右侧控制点 -->
      <div class="timeline-slider-right" @mousedown.stop="(e) => handleMouseDown(e, 'right')" />
      <!-- 显示当前选中的时间范围 -->
      <div class="timeline-slider-range">
        {{ selectedTimeRange[0] || "无限制" }} ~ {{ selectedTimeRange[1] || "无限制" }}
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.timeline {
  &-container {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    z-index: 99;
    border: 1px solid rgba(173, 201, 255, 1);
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    height: 30px;
    box-shadow: 0 0 10px rgba(173, 201, 255, 0.5);
  }
  &-dragging {
    .timeline-slider-range {
      display: block;
    }
  }
  &-marks {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    z-index: 99;
    pointer-events: none;
  }
  &-mark {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    &-line {
      width: 1px;
      height: 10px;
      background-color: #5c87ff;
      margin-bottom: 4px;
    }
    &-text {
      font-size: 12px;
      color: #5c87ff;
      max-width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  &-slider {
    position: absolute;
    top: 0;
    bottom: 0;
    background-color: rgba(173, 201, 255, 0.8);
    border-left: 1px solid #5c87ff;
    border-right: 1px solid #5c87ff;
    cursor: pointer;
    &-left,
    &-right {
      position: absolute;
      top: 50%;
      width: 5px;
      height: 18px;
      background-color: white;
      border-radius: 9px;
      border: 1px solid #5c87ff;
      cursor: pointer;
    }

    &-left {
      left: -0.5px;
      transform: translate(-50%, -50%);
    }

    &-right {
      right: -0.5px;
      transform: translate(50%, -50%);
    }

    &:hover {
      .timeline-slider-range {
        display: block;
      }
    }

    &-range {
      display: none;
      position: absolute;
      top: -32px;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;
      font-size: 12px;
      color: #5c87ff;
      background-color: rgba(255, 255, 255, 0.9);
      padding: 2px 6px;
      border-radius: 3px;
      border: 1px solid #5c87ff;

      // 向下的箭头
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 100%);
        border-width: 6px;
        border-style: solid;
        border-color: #5c87ff transparent transparent transparent;
      }
    }
  }
}
</style>
