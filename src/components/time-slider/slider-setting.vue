<script>
import { defineComponent, ref } from "vue";
import { merge } from "lodash-es";
import { defaultConfig } from "./slider-setting";

export default defineComponent({
  components: {},
  props: {},
  emits: ["change"],
  setup(props, { emit }) {
    const visible = ref(false);
    let formStatus = ref(defaultConfig);

    const show = (config) => {
      formStatus.value = merge({}, defaultConfig, config);
      if (config.valueFormat) {
        formStatus.value.range = [
          config.valueFormat(config.range[0]),
          config.valueFormat(config.range[1]),
        ];
      }

      visible.value = true;
    };
    const hide = () => {
      visible.value = false;
    };

    const rangeConfig = ref({
      format: "YY-MM-DD HH:mm",
      showTime: {
        format: "HH:mm",
        defaultValue: [moment("00:00:00", "HH:mm:ss"), moment("23:59:59", "HH:mm:ss")],
      },
    });

    const typeOptions = [
      { label: "秒", value: "second" },
      { label: "分钟", value: "minute" },
      { label: "小时", value: "hour" },
      { label: "日期 - xx年xx月xx日", value: "date" },
      { label: "月份 - xx年xx月", value: "month" },
      { label: "年份 - xxxx年", value: "year" },
    ];

    const confirm = () => {
      hide();
      let config = { ...formStatus.value };
      if (config.rawFormat) {
        config.range = [config.rawFormat(config.range[0]), config.rawFormat(config.range[1])];
      }

      emit("change", config);
    };

    return {
      typeOptions,
      rangeConfig,

      visible,
      show,
      hide,

      form: formStatus,
      confirm,
    };
  },
});
</script>

<template>
  <a-drawer
    title="设置"
    placement="right"
    wrapClassName="map-slider-setting-drawer"
    destroyOnClose
    :width="420"
    :visible="visible"
    @close="hide"
  >
    <!-- 表单 -->
    <a-form-model
      class="drawer-form"
      :model="form"
      :wrapper-col="{ span: 18 }"
      :label-col="{ span: 6 }"
    >
      <a-form-model-item label="范围" prop="range">
        <a-range-picker
          v-bind="rangeConfig"
          v-model="form.range"
          :placeholder="['开始', '结束']"
          style="width: 100%"
        />
      </a-form-model-item>
      <a-form-model-item label="自动播放速度" prop="scrollSpeed">
        <a-slider v-model="form.scrollSpeed" :min="0.5" :max="4" :step="0.5" />
      </a-form-model-item>
    </a-form-model>
    <!-- 底部 -->
    <div class="drawer-footer">
      <a-button @click="hide">取消</a-button>
      <a-button type="primary" @click="confirm">确定</a-button>
    </div>
  </a-drawer>
</template>

<style lang="less">
.map-slider-setting-drawer {
  .ant-drawer-body {
    height: calc(100% - 56px);
    display: flex;
    flex-direction: column;
    padding: 0;

    .drawer-form {
      flex: auto;
      padding: 12px;
    }
    .drawer-footer {
      flex: none;

      padding: 12px;
      display: flex;
      justify-content: flex-end;
      column-gap: 8px;
      border-top: 1px solid var(--ds-border-color-split);
    }
  }
}
</style>
