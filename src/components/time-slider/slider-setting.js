// 默认配置
export const defaultConfig = {
  // 样式配置
  height: 48,
  fontSize: 14,
  lineColor: "#ccc",
  textColor: "#666",
  backgroundColor: "#f5f5f5",

  // 数据配置
  range: [0, 100], // 范围
  selectionWidth: 1, // 选中范围宽度
  unitWidth: 10, // 每个小刻度的宽度
  majorTickHeight: 16, // 大刻度高度
  minorTickHeight: 12, // 小刻度高度
  scrollSpeed: 0.5, // 自动滚动速度
  minorTickCount: 10, // 每个大刻度之间的小刻度个数
  minorTickCounts: [], // 用于月份等需要特殊间隔个数的场景，长度不够的情况下 minorTickCount 作为默认值
  labelFormat: null, // 标签格式化 （val) => val
  valueFormat: null, // 值格式化 （val) => val
  rawFormat: null, // 原始格式化 （val) => val
  padding: 50, // 左右两侧留白,防止文字被截断
};

const monthDays = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 30];
function labelFormat(val) {
  // 边界检查
  if (val < 1) return "1月1日";
  if (val > 366) return "12月31日";

  // 计算月份和日期
  let days = val;
  let month = 0;

  // 使用累加数组优化查找
  const accDays = monthDays.reduce((acc, curr) => {
    acc.push((acc[acc.length - 1] || 0) + curr);
    return acc;
  }, []);

  // 二分查找优化月份定位
  month = accDays.findIndex((sum) => sum >= days);
  if (month === -1) {
    return "12月31日";
  }

  // 计算日期
  const prevMonthDays = month === 0 ? 0 : accDays[month - 1];
  days = days - prevMonthDays;

  return `${month + 1}月${days}日`;
}

// 数字范围
export const numberConfig = {
  scrollSpeed: 1,
  selectionWidth: 1,
  minorTickCount: 10,
  unitWidth: 24,
  range: [0, 100],
};

// 日期范围
export const dayConfig = {
  scrollSpeed: 1,
  selectionWidth: 1,
  minorTickCount: 30,
  unitWidth: 5,
  range: [1, 366],
  minorTickCounts: monthDays,
  labelFormat,
};

// 月份范围
export const monthConfig = {
  selectionWidth: 1,
  minorTickCount: 1,
  unitWidth: 72,
  range: [1, 12],
  labelFormat(val) {
    return `${val} 月`;
  },
};

// 年份范围
export const yearConfig = {
  scrollSpeed: 1.2,
  selectionWidth: 1,
  minorTickCount: 1,
  unitWidth: 100,
  range: [2020, 2025],
  labelFormat(val) {
    return `${val} 年`;
  },
};

// 年月日范围
export const ymdConfig = {
  selectionWidth: 1,
  minorTickCount: 1,
  majorTickHeight: 14, // 大刻度高度
  minorTickHeight: 8, // 小刻度高度
  scrollSpeed: 1.5,
  unitWidth: 80,
  range: [
    Math.floor(new Date("2024-01-01").valueOf() / (1000 * 60 * 60 * 24)),
    Math.floor(new Date("2024-12-31").valueOf() / (1000 * 60 * 60 * 24)),
  ],
  extraRangeConfig: {
    // start: {
    //   label: "全部",
    //   value: -1,
    // },
    // end: {
    //   label: "全部",
    //   value: -1,
    // },
  },
  labelFormat(val) {
    return moment(val * 1000 * 60 * 60 * 24).format("YYYY-MM-DD");
  },
  valueFormat(val) {
    if (val === -1) return "";

    return moment(val * 1000 * 60 * 60 * 24).format("YYYY-MM-DD");
  },
  rawFormat(val) {
    return Math.floor(new Date(val).valueOf() / (1000 * 60 * 60 * 24));
  },
};
