<script>
import {
  defineComponent,
  onMounted,
  ref,
  watch,
  onUnmounted,
  watchEffect,
  reactive,
  computed,
} from "vue";
import { merge } from "lodash-es";

import SliderSetting from "./slider-setting.vue";
import { defaultConfig } from "./slider-setting";

export default defineComponent({
  name: "Slider",

  components: {
    SliderSetting,
  },

  props: {
    modelValue: {
      type: Array,
      default: () => [0, 100],
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    useSettingDrawer: {
      type: Boolean,
      default: true,
    },
  },

  model: {
    event: "update:modelValue",
    prop: "modelValue",
  },

  emits: ["update:modelValue", "change"],

  setup(props, { emit }) {
    // 配置
    const sliderSettingRef = ref(null);
    let configState = reactive(merge({}, defaultConfig, props.config));
    watchEffect(() => {
      configState = reactive(merge({}, defaultConfig, props.config));
    });

    // 样式配置
    const styleConfig = computed(() => {
      return {
        "--slider-height": configState.height + "px",
        "--slider-font-size": configState.fontSize + "px",
        "--slider-line-color": configState.lineColor,
        "--slider-text-color": configState.textColor,
        "--slider-background-color": configState.backgroundColor,
      };
    });

    // Canvas相关状态
    const canvasRef = ref(null);
    const ctx = ref(null);
    const offsetX = ref(0);
    const maxOffset = ref((configState.range[1] - configState.range[0]) * configState.unitWidth);

    // 拖拽相关状态
    const isDragging = ref(false);
    const startX = ref(0);
    const lastDragX = ref(0);

    // 选中范围相关状态
    const sliderSelectionWidth = ref(configState.selectionWidth * configState.unitWidth);
    const sliderSelectionRange = ref(props.modelValue);
    const sliderSelectionStartX = ref(0);
    const sliderSelectionLeft = ref(0);
    let canUpdateSliderSelectionLeft = true;

    // 自动滚动相关状态
    const autoScrollTimer = ref(null);
    const isAutoScrolling = ref(false);
    const isAutoScrollEnd = ref(false);

    // 更新偏移量
    const updateOffsetX = (modelValue) => {
      // 处理范围外的特殊值
      const processValue = (v) => {
        if (v === configState.extraRangeConfig?.start?.value) {
          return configState.range[0];
        }
        if (v === configState.extraRangeConfig?.end?.value) {
          return configState.range[1];
        }
        return v;
      };

      const clampedValue = modelValue.map((v) => {
        const processedValue = processValue(v);
        return Math.min(Math.max(processedValue, configState.range[0]), configState.range[1]);
      });

      sliderSelectionRange.value = modelValue; // 保持原始值

      // 计算新的偏移量,使选中范围居中显示
      if (canvasRef.value) {
        const startValue = clampedValue[0];
        const valueOffset = startValue - configState.range[0];
        const pixelOffset = valueOffset * configState.unitWidth;
        const centerX = (canvasRef.value.offsetWidth - sliderSelectionWidth.value) / 2;

        // 设置新的偏移量
        offsetX.value = Math.max(0, Math.min(pixelOffset - centerX, maxOffset.value));
        sliderSelectionStartX.value = offsetX.value + centerX;

        // 重绘标尺
        drawRuler();
      }
    };

    // 监听value变化
    watch(props.modelValue, (modelValue) => {
      updateOffsetX(modelValue);
    });

    // Canvas绘制相关方法
    const initCanvas = () => {
      const canvas = canvasRef.value;
      const dpr = window.devicePixelRatio || 1;
      canvas.width = canvas.offsetWidth * dpr;
      canvas.height = configState.height * dpr;
      ctx.value = canvas.getContext("2d");
      ctx.value.scale(dpr, dpr);
    };

    const drawRuler = () => {
      const context = ctx.value;
      const canvas = canvasRef.value;

      // 清空画布
      context.fillStyle = configState.backgroundColor;
      context.fillRect(0, 0, canvas.width, canvas.height);

      // 计算可见区域,添加左右padding
      const startPixel = offsetX.value - configState.padding;
      const endPixel = startPixel + canvas.offsetWidth + configState.padding * 2;
      const centerOffset = canvas.offsetWidth / 2;

      // 绘制刻度和标签
      context.beginPath();
      context.strokeStyle = configState.lineColor;
      context.fillStyle = configState.textColor;
      context.font = `${configState.fontSize}px Arial`;

      // 刻度绘制状态
      let prevMinorTickIndex = 0; // 上一个小刻度索引
      let minorTickIndex = 0; // 当前小刻度索引
      let minorTickSum = 0; // 小刻度总数
      let isMajorTick = false; // 是否为大刻度

      function drawTick(pixel) {
        const x = pixel - offsetX.value;
        const number =
          Math.floor((pixel - centerOffset) / configState.unitWidth) + configState.range[0];

        // 检查数值是否在有效范围内
        if (number < configState.range[0] || number > configState.range[1]) {
          return;
        }

        // 检查x坐标是否在可见区域内
        if (x < -configState.padding || x > canvas.offsetWidth + configState.padding) {
          return;
        }

        // 计算刻度类型
        const valueOffset = number - configState.range[0];
        const currentMinorTickCount =
          configState.minorTickCounts?.[prevMinorTickIndex] || configState.minorTickCount;

        // 判断是否为大刻度点
        isMajorTick = false;
        if (valueOffset >= minorTickSum) {
          minorTickIndex++;
          minorTickSum += currentMinorTickCount;
          prevMinorTickIndex = minorTickIndex;
          isMajorTick = true;
        }
        // 判断是否为中间刻度
        const isMiddleTick =
          !configState.minorTickCounts?.length &&
          valueOffset % Math.floor(currentMinorTickCount / 2) === 0;

        // 绘制刻度
        if (isMajorTick) {
          // 更新选择区域左边界位置
          if (canUpdateSliderSelectionLeft) {
            sliderSelectionLeft.value = x;
            canUpdateSliderSelectionLeft = false;
          }

          // 绘制大刻度和标签
          context.moveTo(x, configState.height - configState.majorTickHeight);
          context.lineTo(x, configState.height);

          let label = number;
          // 处理范围边界的特殊标签
          if (number === configState.range[0] && configState.extraRangeConfig?.start?.label) {
            label = configState.extraRangeConfig.start.label;
          } else if (number === configState.range[1] && configState.extraRangeConfig?.end?.label) {
            label = configState.extraRangeConfig.end.label;
          } else {
            label = configState.labelFormat?.(number) || number;
          }

          const textWidth = context.measureText(label).width;
          const labelX = x - textWidth / 2;
          context.fillText(label, labelX, configState.height - configState.majorTickHeight - 2);
        } else {
          // 绘制中等或小刻度
          const tickHeight = isMiddleTick
            ? configState.minorTickHeight + 2
            : configState.minorTickHeight;

          context.moveTo(x, configState.height - tickHeight);
          context.lineTo(x, configState.height);
        }
      }

      // 绘制所有可见刻度
      for (
        let pixel = startPixel - (startPixel % configState.unitWidth);
        pixel < endPixel;
        pixel += configState.unitWidth
      ) {
        drawTick(pixel);
      }

      context.stroke();

      // 更新选中范围
      updateSelectionRange(centerOffset);
    };

    // 选中范围相关方法
    const updateSelectionRange = (centerOffset) => {
      const pixelWidth = configState.unitWidth;
      const startValue =
        Math.floor((sliderSelectionStartX.value - centerOffset) / pixelWidth) +
        configState.range[0];
      const endValue =
        Math.floor(
          (sliderSelectionStartX.value + sliderSelectionWidth.value - centerOffset) / pixelWidth,
        ) + configState.range[0];

      let clampedStartValue = Math.max(
        configState.range[0],
        Math.min(startValue, configState.range[1]),
      );
      let clampedEndValue = Math.max(
        configState.range[0],
        Math.min(endValue, configState.range[1]),
      );

      // 处理范围边界的特殊值
      if (
        Math.abs(clampedStartValue - configState.range[0]) < 0.1 &&
        configState.extraRangeConfig?.start?.value !== undefined
      ) {
        clampedStartValue = configState.extraRangeConfig.start.value;
      }
      if (
        Math.abs(clampedEndValue - configState.range[1]) < 0.1 &&
        configState.extraRangeConfig?.end?.value !== undefined
      ) {
        clampedEndValue = configState.extraRangeConfig.end.value;
      }

      if (configState.valueFormat) {
        clampedStartValue = configState.valueFormat(clampedStartValue);
        clampedEndValue = configState.valueFormat(clampedEndValue);
      }

      sliderSelectionRange.value = [clampedStartValue, clampedEndValue];
      emit("update:modelValue", sliderSelectionRange.value);
      emit("change", sliderSelectionRange.value);
    };

    const setSelectionWidth = (width) => {
      sliderSelectionWidth.value = width;
      const centerX = (canvasRef.value.offsetWidth - width) / 2;
      sliderSelectionStartX.value = offsetX.value + centerX;
      drawRuler();
    };

    // 自动滚动相关方法
    const startAutoScroll = (direction) => {
      if (isAutoScrolling.value) return;

      isAutoScrolling.value = true;
      const scroll = () => {
        const delta = direction * configState.scrollSpeed;
        const newOffset = offsetX.value + delta;

        if (newOffset >= 0 && newOffset <= maxOffset.value) {
          offsetX.value = newOffset;
          const centerX = (canvasRef.value.offsetWidth - sliderSelectionWidth.value) / 2;
          sliderSelectionStartX.value = offsetX.value + centerX;
          drawRuler();
          autoScrollTimer.value = requestAnimationFrame(scroll);
        } else {
          stopAutoScroll();
          isAutoScrollEnd.value = true;
        }
      };

      autoScrollTimer.value = requestAnimationFrame(scroll);
    };

    const stopAutoScroll = () => {
      if (autoScrollTimer.value) {
        cancelAnimationFrame(autoScrollTimer.value);
        autoScrollTimer.value = null;
      }
      isAutoScrolling.value = false;
      isAutoScrollEnd.value = false;
    };

    const replayScroll = () => {
      isAutoScrollEnd.value = false;
      offsetX.value = 0;
      const centerX = (canvasRef.value.offsetWidth - sliderSelectionWidth.value) / 2;
      sliderSelectionStartX.value = centerX;
      stopAutoScroll();
      startAutoScroll(1);
    };

    // 事件处理方法
    const handleWheel = (e) => {
      e.stopPropagation();
      e.preventDefault();
      const newOffset = offsetX.value + e.deltaX;
      if (newOffset >= 0 && newOffset <= maxOffset.value) {
        offsetX.value = newOffset;
        const centerX = (canvasRef.value.offsetWidth - sliderSelectionWidth.value) / 2;
        sliderSelectionStartX.value = offsetX.value + centerX;
        drawRuler();
      }
    };

    const handleMouseDown = (e) => {
      isDragging.value = true;
      lastDragX.value = e.clientX;
      startX.value = e.clientX + offsetX.value;

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleGlobalMouseUp);
      document.addEventListener("selectstart", preventSelect);
    };

    const handleMouseMove = (e) => {
      if (isDragging.value) {
        const deltaX = e.clientX - lastDragX.value;
        const newOffset = Math.max(0, Math.min(offsetX.value - deltaX, maxOffset.value));
        offsetX.value = newOffset;
        lastDragX.value = e.clientX;

        const centerX = (canvasRef.value.offsetWidth - sliderSelectionWidth.value) / 2;
        sliderSelectionStartX.value = offsetX.value + centerX;
        drawRuler();

        // 边缘滚动检测
        const rect = canvasRef.value.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;

        if (mouseX < 50) {
          startAutoScroll(-1);
        } else if (mouseX > rect.width - 50) {
          startAutoScroll(1);
        } else {
          stopAutoScroll();
        }
      }
    };

    const preventSelect = (e) => {
      e.preventDefault();
    };

    const handleGlobalMouseUp = () => {
      if (isDragging.value) {
        isDragging.value = false;
        stopAutoScroll();
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleGlobalMouseUp);
        document.removeEventListener("selectstart", preventSelect);
      }
    };

    const handleMouseUp = () => {
      handleGlobalMouseUp();
    };

    // 窗口大小改变处理
    const handleResize = () => {
      initCanvas();
      canUpdateSliderSelectionLeft = true;
      const centerX = (canvasRef.value.offsetWidth - sliderSelectionWidth.value) / 2;
      sliderSelectionStartX.value = offsetX.value + centerX;
      drawRuler();
    };

    // 打开设置
    const openSetting = () => {
      sliderSettingRef.value.show(configState);
    };

    // 设置改变
    const handleSettingChange = (config) => {
      // 更新配置
      configState = reactive(merge({}, configState, config));

      // 更新最大偏移量
      maxOffset.value = (configState.range[1] - configState.range[0]) * configState.unitWidth;

      // 更新选中范围宽度
      sliderSelectionWidth.value = configState.selectionWidth * configState.unitWidth;

      // 重新初始化画布并重绘
      initCanvas();
      canUpdateSliderSelectionLeft = false;

      // 更新选中范围位置
      const centerX = (canvasRef.value.offsetWidth - sliderSelectionWidth.value) / 2;
      sliderSelectionStartX.value = offsetX.value + centerX;

      // 重绘标尺
      drawRuler();
    };

    // 生命周期钩子
    onMounted(() => {
      initCanvas();
      updateOffsetX(props.modelValue);

      window.addEventListener("resize", handleResize);
    });

    onUnmounted(() => {
      window.removeEventListener("resize", handleResize);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
      document.removeEventListener("selectstart", preventSelect);

      if (autoScrollTimer.value) {
        cancelAnimationFrame(autoScrollTimer.value);
      }
    });

    return {
      sliderSettingRef,
      openSetting,
      handleSettingChange,
      styleConfig,
      canvasRef,
      isAutoScrolling,
      isAutoScrollEnd,
      handleWheel,
      handleMouseDown,
      handleMouseMove,
      handleMouseUp,
      sliderSelectionWidth,
      sliderSelectionRange,
      sliderSelectionLeft,
      setSelectionWidth,
      startAutoScroll,
      stopAutoScroll,
      replayScroll,
    };
  },
});
</script>

<template>
  <div class="slider-container" :style="styleConfig" @wheel.prevent.stop>
    <!-- 控制按钮 -->
    <div class="slider-control">
      <!-- 重放 -->
      <a-icon
        v-if="isAutoScrollEnd"
        type="redo"
        :style="{ color: '#40a9ff' }"
        @click="replayScroll"
      />
      <!-- 播放按扭 -->
      <a-icon
        v-else-if="isAutoScrolling"
        type="pause-circle"
        :style="{ color: '#40a9ff' }"
        @click="() => stopAutoScroll()"
      />
      <!-- 暂停按钮 -->
      <a-icon
        v-else
        type="play-circle"
        :style="{ color: '#40a9ff' }"
        @click="() => startAutoScroll(1)"
      />
    </div>
    <!-- 标尺 -->
    <div class="ruler-container">
      <canvas
        ref="canvasRef"
        @wheel="handleWheel"
        @mousedown="handleMouseDown"
        @mouseup="handleMouseUp"
      />
      <!-- 选中范围 -->
      <div
        class="selection-range"
        :style="{ left: `${sliderSelectionLeft}px`, width: `${sliderSelectionWidth}px` }"
      />
    </div>
    <!-- 设置 -->
    <div v-if="useSettingDrawer" class="slider-setting">
      <a-icon type="setting" :style="{ color: '#40a9ff' }" @click="openSetting" />
    </div>

    <!-- 设置 -->
    <SliderSetting v-if="useSettingDrawer" ref="sliderSettingRef" @change="handleSettingChange" />
  </div>
</template>

<style lang="less" scoped>
.slider-container {
  width: 100%;
  display: flex;
  align-items: center;
  background-color: var(--slider-background-color);
  box-shadow: 0 0 10px 0 var(--ds-shadow-color);
  border-radius: 5px;
  overflow: hidden;

  .slider-control {
    position: relative;
    width: 48px;
    height: 48px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;

    &::after {
      content: "";
      position: absolute;
      top: 15%;
      right: 0;
      bottom: 15%;
      width: 1px;
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  .ruler-container {
    position: relative;
    width: 1px;
    height: 48px;
    overflow: hidden;
    user-select: none;
    flex: auto;

    canvas {
      width: 100%;
      height: 100%;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }

    .selection-range {
      position: absolute;
      top: 0;
      transform: translateX(-50%);
      height: 100%;
      background: linear-gradient(to top, rgba(64, 169, 255, 0.4), rgba(64, 169, 255, 0.1));
      pointer-events: none;
      transition: width 0.3s ease;

      // &::before {
      //   content: "";
      //   position: absolute;
      //   bottom: 0;
      //   left: 50%;
      //   transform: translateX(-50%);
      //   width: 0;
      //   height: 0;
      //   border-left: 6px solid transparent;
      //   border-right: 6px solid transparent;
      //   border-bottom: 8px solid #40a9ff;
      // }
    }
  }

  .slider-setting {
    position: relative;
    width: 48px;
    height: 48px;
    margin-left: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    cursor: pointer;

    &::after {
      content: "";
      position: absolute;
      top: 15%;
      left: 0;
      bottom: 15%;
      width: 1px;
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
