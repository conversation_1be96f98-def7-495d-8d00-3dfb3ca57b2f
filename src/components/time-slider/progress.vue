<script>
import {
  defineComponent,
  onMounted,
  ref,
  watch,
  onUnmounted,
  watchEffect,
  reactive,
  computed,
} from "vue";
import { merge } from "lodash-es";

import SliderSetting from "./slider-setting.vue";
import { defaultConfig } from "./slider-setting";

export default defineComponent({
  name: "Slider",

  components: {
    SliderSetting,
  },

  props: {
    modelValue: {
      type: Array,
      default: () => [0, 100],
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    useSettingDrawer: {
      type: Boolean,
      default: true,
    },
  },

  model: {
    event: "update:modelValue",
    prop: "modelValue",
  },

  emits: ["update:modelValue", "change"],

  setup(props, { emit }) {
    // 配置
    const sliderSettingRef = ref(null);
    let configState = reactive(merge({}, defaultConfig, props.config));
    watchEffect(() => {
      configState = reactive(merge({}, defaultConfig, props.config));
    });

    // 样式配置
    const styleConfig = computed(() => {
      return {
        "--slider-height": configState.height + "px",
        "--slider-font-size": configState.fontSize + "px",
        "--slider-line-color": configState.lineColor,
        "--slider-text-color": configState.textColor,
        "--slider-background-color": configState.backgroundColor,
      };
    });

    // Canvas相关状态
    const canvasRef = ref(null);
    const ctx = ref(null);
    const offsetX = ref(0);
    const maxOffset = ref((configState.range[1] - configState.range[0]) * configState.unitWidth);

    // 拖拽相关状态
    const isDragging = ref(false);
    const startX = ref(0);
    const lastDragX = ref(0);

    // 选中范围相关状态
    const sliderSelectionWidth = ref(configState.selectionWidth * configState.unitWidth);
    const sliderSelectionRange = ref(props.modelValue);
    const sliderSelectionStartX = ref(0);
    const sliderSelectionLeft = ref(0);
    let canUpdateSliderSelectionLeft = true;

    // 自动滚动相关状态
    const autoScrollTimer = ref(null);
    const isAutoScrolling = ref(false);
    const isAutoScrollEnd = ref(false);

    // 更新偏移量
    const updateOffsetX = (modelValue) => {
      // 处理范围外的特殊值
      const processValue = (v) => {
        if (v === configState.extraRangeConfig?.start?.value) {
          return configState.range[0];
        }
        if (v === configState.extraRangeConfig?.end?.value) {
          return configState.range[1];
        }
        return v;
      };

      const clampedValue = modelValue.map((v) => {
        const processedValue = processValue(v);
        return Math.min(Math.max(processedValue, configState.range[0]), configState.range[1]);
      });

      sliderSelectionRange.value = modelValue; // 保持原始值

      // 计算新的偏移量,使选中范围从左侧开始
      if (canvasRef.value) {
        const startValue = clampedValue[0];
        const valueOffset = startValue - configState.range[0];
        const pixelOffset = valueOffset * configState.unitWidth;

        // 设置新的偏移量
        offsetX.value = Math.max(0, Math.min(pixelOffset, maxOffset.value));
        sliderSelectionStartX.value = offsetX.value;

        // 重绘标尺
        drawRuler();
      }
    };

    // 监听value变化
    watch(props.modelValue, (modelValue) => {
      updateOffsetX(modelValue);
    });

    // Canvas绘制相关方法
    const initCanvas = () => {
      const canvas = canvasRef.value;
      const dpr = window.devicePixelRatio || 1;
      canvas.width = canvas.offsetWidth * dpr;
      canvas.height = configState.height * dpr;
      ctx.value = canvas.getContext("2d");
      ctx.value.scale(dpr, dpr);
    };

    const drawRuler = () => {
      const context = ctx.value;
      const canvas = canvasRef.value;

      // 清空画布
      context.clearRect(0, 0, canvas.width, canvas.height);

      // 计算垂直居中位置
      const centerY = canvas.height / 4;

      // 绘制进度条背景
      context.fillStyle = '#e0e0e0';
      context.fillRect(0, centerY - 2, canvas.width, 4);

      // 计算可见区域
      const startPixel = offsetX.value - configState.padding;
      const endPixel = startPixel + canvas.offsetWidth + configState.padding * 2;

      // 绘制已播放进度
      context.fillStyle = '#1890ff';
      context.fillRect(0, centerY - 2, sliderSelectionStartX.value, 4);

      // 绘制进度点
      context.beginPath();
      context.arc(sliderSelectionStartX.value + Math.PI * 2, centerY, 6, 0, Math.PI * 2);
      context.fillStyle = '#1890ff';
      context.fill();
      context.strokeStyle = '#fff';
      context.lineWidth = 2;
      context.stroke();

      // 更新选中范围
      updateSelectionRange();
    };

    // 选中范围相关方法
    const updateSelectionRange = () => {
      const pixelWidth = configState.unitWidth;
      const startValue =
        Math.floor(sliderSelectionStartX.value / pixelWidth) +
        configState.range[0];
      const endValue =
        Math.floor(
          (sliderSelectionStartX.value + sliderSelectionWidth.value) / pixelWidth,
        ) + configState.range[0];

      let clampedStartValue = Math.max(
        configState.range[0],
        Math.min(startValue, configState.range[1]),
      );
      let clampedEndValue = Math.max(
        configState.range[0],
        Math.min(endValue, configState.range[1]),
      );

      // 处理范围边界的特殊值
      if (
        Math.abs(clampedStartValue - configState.range[0]) < 0.1 &&
        configState.extraRangeConfig?.start?.value !== undefined
      ) {
        clampedStartValue = configState.extraRangeConfig.start.value;
      }
      if (
        Math.abs(clampedEndValue - configState.range[1]) < 0.1 &&
        configState.extraRangeConfig?.end?.value !== undefined
      ) {
        clampedEndValue = configState.extraRangeConfig.end.value;
      }

      if (configState.valueFormat) {
        clampedStartValue = configState.valueFormat(clampedStartValue);
        clampedEndValue = configState.valueFormat(clampedEndValue);
      }

      sliderSelectionRange.value = [clampedStartValue, clampedEndValue];
      emit("update:modelValue", sliderSelectionRange.value);
      emit("change", sliderSelectionRange.value);
    };

    const setSelectionWidth = (width) => {
      sliderSelectionWidth.value = width;
      sliderSelectionStartX.value = offsetX.value;
      drawRuler();
    };

    // 自动滚动相关方法
    const startAutoScroll = (direction) => {
      if (isAutoScrolling.value) return;

      isAutoScrolling.value = true;
      const scroll = () => {
        const delta = direction * configState.scrollSpeed;
        const newOffset = offsetX.value + delta;

        if (newOffset >= 0 && newOffset <= maxOffset.value) {
          offsetX.value = newOffset;
          sliderSelectionStartX.value = offsetX.value;
          drawRuler();
          autoScrollTimer.value = requestAnimationFrame(scroll);
        } else {
          stopAutoScroll();
          isAutoScrollEnd.value = true;
        }
      };

      autoScrollTimer.value = requestAnimationFrame(scroll);
    };

    const stopAutoScroll = () => {
      if (autoScrollTimer.value) {
        cancelAnimationFrame(autoScrollTimer.value);
        autoScrollTimer.value = null;
      }
      isAutoScrolling.value = false;
      isAutoScrollEnd.value = false;
    };

    const replayScroll = () => {
      isAutoScrollEnd.value = false;
      offsetX.value = 0;
      sliderSelectionStartX.value = 0;
      stopAutoScroll();
      startAutoScroll(1);
    };

    // 事件处理方法
    const handleWheel = (e) => {
      e.stopPropagation();
      e.preventDefault();
      const newOffset = offsetX.value + e.deltaX;
      if (newOffset >= 0 && newOffset <= maxOffset.value) {
        offsetX.value = newOffset;
        sliderSelectionStartX.value = offsetX.value;
        drawRuler();
      }
    };

    const handleMouseDown = (e) => {
      isDragging.value = true;
      lastDragX.value = e.clientX;
      startX.value = e.clientX + offsetX.value;

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleGlobalMouseUp);
      document.addEventListener("selectstart", preventSelect);
    };

    const handleMouseMove = (e) => {
      if (isDragging.value) {
        const deltaX = e.clientX - lastDragX.value;
        const newOffset = Math.max(0, Math.min(offsetX.value + deltaX, maxOffset.value));
        offsetX.value = newOffset;
        lastDragX.value = e.clientX;

        sliderSelectionStartX.value = offsetX.value;
        drawRuler();

        // 边缘滚动检测
        const rect = canvasRef.value.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;

        if (mouseX < 50) {
          startAutoScroll(-1);
        } else if (mouseX > rect.width - 50) {
          startAutoScroll(1);
        } else {
          stopAutoScroll();
        }
      }
    };

    const preventSelect = (e) => {
      e.preventDefault();
    };

    const handleGlobalMouseUp = () => {
      if (isDragging.value) {
        isDragging.value = false;
        stopAutoScroll();
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleGlobalMouseUp);
        document.removeEventListener("selectstart", preventSelect);
      }
    };

    const handleMouseUp = () => {
      handleGlobalMouseUp();
    };

    // 窗口大小改变处理
    const handleResize = () => {
      initCanvas();
      canUpdateSliderSelectionLeft = true;
      sliderSelectionStartX.value = offsetX.value;
      drawRuler();
    };

    // 打开设置
    const openSetting = () => {
      sliderSettingRef.value.show(configState);
    };

    // 设置改变
    const handleSettingChange = (config) => {
      // 更新配置
      configState = reactive(merge({}, configState, config));

      // 更新最大偏移量
      maxOffset.value = (configState.range[1] - configState.range[0]) * configState.unitWidth;

      // 更新选中范围宽度
      sliderSelectionWidth.value = configState.selectionWidth * configState.unitWidth;

      // 重新初始化画布并重绘
      initCanvas();
      canUpdateSliderSelectionLeft = false;

      // 更新选中范围位置
      sliderSelectionStartX.value = offsetX.value;

      // 重绘标尺
      drawRuler();
    };

    // 生命周期钩子
    onMounted(() => {
      initCanvas();
      updateOffsetX(props.modelValue);

      window.addEventListener("resize", handleResize);
    });

    onUnmounted(() => {
      window.removeEventListener("resize", handleResize);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
      document.removeEventListener("selectstart", preventSelect);

      if (autoScrollTimer.value) {
        cancelAnimationFrame(autoScrollTimer.value);
      }
    });

    return {
      sliderSettingRef,
      openSetting,
      handleSettingChange,
      styleConfig,
      canvasRef,
      isAutoScrolling,
      isAutoScrollEnd,
      handleWheel,
      handleMouseDown,
      handleMouseMove,
      handleMouseUp,
      sliderSelectionWidth,
      sliderSelectionRange,
      sliderSelectionLeft,
      setSelectionWidth,
      startAutoScroll,
      stopAutoScroll,
      replayScroll,
    };
  },
});
</script>

<template>
  <div class="slider-container" :style="styleConfig" @wheel.prevent.stop>
    <!-- 控制按钮 -->
    <div class="slider-control">
      <!-- 重放 -->
      <a-icon
        v-if="isAutoScrollEnd"
        type="redo"
        :style="{ color: '#40a9ff' }"
        @click="replayScroll"
      />
      <!-- 播放按扭 -->
      <a-icon
        v-else-if="isAutoScrolling"
        type="pause-circle"
        :style="{ color: '#40a9ff' }"
        @click="() => stopAutoScroll()"
      />
      <!-- 暂停按钮 -->
      <a-icon
        v-else
        type="play-circle"
        :style="{ color: '#40a9ff' }"
        @click="() => startAutoScroll(1)"
      />
    </div>
    <!-- 进度条 -->
    <div class="ruler-container">
      <canvas
        ref="canvasRef"
        @wheel="handleWheel"
        @mousedown="handleMouseDown"
        @mouseup="handleMouseUp"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
.slider-container {
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 5px;
  overflow: hidden;

  .slider-control {
    position: relative;
    width: 38px;
    height: 38px;
    // margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
  }

  .ruler-container {
    position: relative;
    width: 1px;
    height: 48px;
    overflow: hidden;
    user-select: none;
    flex: auto;

    canvas {
      width: 100%;
      height: 100%;
      cursor: pointer;

      &:active {
        cursor: grabbing;
      }
    }
  }
}
</style>
