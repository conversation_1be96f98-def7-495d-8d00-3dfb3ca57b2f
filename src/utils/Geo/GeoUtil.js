import { getAreaCenter, getProperties, pointGeoToRender } from "./utils";
import { _FIELD_ERROR } from "./config";

export default class GeoUtil {
  _resolve = () => {};
  _reject = () => {};

  _geoPointLayer = null;
  _geoPolygonLayer = null;

  constructor() {}

  _initPointLayer() {
    this._geoPointLayer = {
      name: "GEO导入点图层",
      displayName: "GEO导入点图层",
      type: "marker",
      mappingConfig: [
        {
          name: "id",
          displayName: "主键",
          mapColumnName: "id",
          columnType: "idColumn",
        },
        {
          name: "name",
          displayName: "名称",
          mapColumnName: "name",
          columnType: "nameColumn",
        },
        {
          name: "lng",
          displayName: "经度",
          mapColumnName: "lng",
          columnType: "longitudeColumn",
        },
        {
          name: "lat",
          displayName: "纬度",
          mapColumnName: "lat",
          columnType: "latitudeColumn",
        },
      ],
      styleConfig: {
        iconType: "svg",
        svg: {
          type: "svg",
          iconType: "svg",
          svgId: "local",
          svgPath:
            '<svg t="1660786767685" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2390" width="200" height="200"><path d="M512 938.666667c-53.333333 0-384-257.258667-384-469.333334S299.925333 85.333333 512 85.333333s384 171.925333 384 384-330.666667 469.333333-384 469.333334z m0-352c64.8 0 117.333333-52.533333 117.333333-117.333334s-52.533333-117.333333-117.333333-117.333333-117.333333 52.533333-117.333333 117.333333 52.533333 117.333333 117.333333 117.333334z" p-id="2391"></path></svg>',
          scale: 30,
          fillOpacity: 1,
          strokeWeight: 0,
          strokeOpacity: 1,
          fillColor: "#12345e",
          strokeColor: "#000000",
          anchor: {
            width: 470,
            height: 944,
          },
        },
      },
      localData: [],
    };
  }

  _initPolygonLayer() {
    this._geoPolygonLayer = {
      name: "GEO导入面图层",
      displayName: "GEO导入面图层",
      type: "polygon",
      mappingConfig: [
        {
          name: "id",
          displayName: "主键",
          mapColumnName: "id",
          columnType: "idColumn",
        },
        {
          name: "name",
          displayName: "名称",
          mapColumnName: "name",
          columnType: "nameColumn",
        },
        {
          name: "pointList",
          displayName: "点集",
          mapColumnName: "pointList",
          columnType: "pointListColumn",
        },
        {
          name: "centerPoint",
          displayName: "中心点",
          mapColumnName: "centerPoint",
          columnType: "centerPointColumn",
        },
      ],
      localData: [],
    };
  }

  _handlerFieldError(data = {}, fields = []) {
    for (let i = 0; i < fields.length; i++) {
      const key = fields[i];
      if (!data[key]) {
        const err = _FIELD_ERROR[key] || { msg: "缺少字段" };
        this._reject(new Error(err.msg));
        break;
      }
    }
  }

  _getFieldData(data, key = "") {
    if (!key) return null;
    this._handlerFieldError(data, [key]);
    return data[key];
  }

  _handleTypeData(type, data) {
    switch (type) {
      case "Point":
        this._handlePoint(data);
        break;
      case "Polygon":
        this._handlePolygon(data);
        break;
    }
  }

  _handlePoint(data) {
    let _point = pointGeoToRender(data.coordinates);
    let others = getProperties(data);
    this._geoPointLayer.localData.push({
      ..._point,
      ...others,
    });
  }

  _handlePolygon(data) {
    let others = getProperties(data);
    let [lng, lat] = getAreaCenter(data.coordinates[0]);
    this._geoPolygonLayer.localData.push({
      ...others,
      centerPoint: JSON.stringify({ lng, lat }),
      pointList: JSON.stringify(data.coordinates[0]),
    });
  }

  toRenderData(data) {
    this._initPointLayer();
    this._initPolygonLayer();

    return new Promise((resolve, reject) => {
      this._reject = reject;
      this._resolve = resolve;

      this._handlerFieldError(data, ["type", "features"]);

      // 开始处理数据
      data.features?.forEach((i, k) => {
        const geometry = this._getFieldData(i, "geometry");
        const type = this._getFieldData(geometry, "type");
        this._handleTypeData(type, geometry);

        if (k === data.features.length - 1) {
          let _res = [];
          if (this._geoPointLayer.localData?.[0]) _res.push(this._geoPointLayer);
          if (this._geoPolygonLayer.localData?.[0]) _res.push(this._geoPolygonLayer);
          resolve(_res);
        }
      });
    });
  }

  static toGeoJson(data) {
    let _tempMarkers = [];
    let _tempPolygon = [];

    data.forEach((i) => {
      const { type, localData, mappingObj } = i;
      if (type === "marker") {
        const _res = this.structureMarker(localData, mappingObj);
        _tempMarkers = _tempMarkers.concat(_res);
      } else {
        const _res = this.structurePolygon(localData, mappingObj);
        _tempPolygon = _tempPolygon.concat(_res);
      }
    });

    return {
      features: [..._tempMarkers, ..._tempPolygon],
      type: "FeatureCollection",
    };
  }

  static structureMarker(data, columnObj) {
    const _res = data.map((i) => {
      const name = i[columnObj.name];
      const id = i[columnObj.id];
      const lng = i[columnObj.lng];
      const lat = i[columnObj.lat];

      const coordinates = [lng, lat];

      return {
        geometry: { coordinates, type: "Point" },
        type: "Feature",
        properties: {
          name,
          id,
        },
      };
    });

    return _res;
  }

  static structurePolygon(data, columnObj) {
    const _res = data
      .map((i) => {
        const name = i[columnObj.name];
        const id = i[columnObj.id];
        const centerPoint = i[columnObj.centerPoint];
        const pointList = i[columnObj.pointList];
        let coordinates = null;

        try {
          coordinates = [JSON.parse(pointList)];
        } catch (error) {
          return null;
        }

        return {
          geometry: { coordinates, type: "Polygon" },
          type: "Feature",
          properties: {
            name,
            id,
            centerPoint,
          },
        };
      })
      .filter((i) => i);

    return _res;
  }
}
