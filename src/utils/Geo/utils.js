import _uniqueId from "lodash/uniqueId";

// 将坐标数组转换成渲染对象
export const pointGeoToRender = (data) => {
  if (!Array.isArray(data) || !data?.[0]) return null;

  const [lng, lat] = data;

  return {
    lat,
    lng,
  };
};

// 获取配置的自定义属性中的名称和id
export const getProperties = (data) => {
  const { name = "未命名", id = _uniqueId("_geo_") } = data;
  return {
    name,
    id,
  };
};

/**
 * 计算多边形的中心点
 * @param {number[][]} paths 面的点集
 * @returns number[] lng,lat
 */
export const getAreaCenter = (paths) => {
  var total = paths.length;
  var X = 0,
    Y = 0,
    Z = 0;
  paths.forEach((lnglat) => {
    var lng = (lnglat[0] * Math.PI) / 180;
    var lat = (lnglat[1] * Math.PI) / 180;
    var x, y, z;
    x = Math.cos(lat) * Math.cos(lng);
    y = Math.cos(lat) * Math.sin(lng);
    z = Math.sin(lat);
    X += x;
    Y += y;
    Z += z;
  });
  X = X / total;
  Y = Y / total;
  Z = Z / total;

  var Lng = Math.atan2(Y, X);
  var Hyp = Math.sqrt(X * X + Y * Y);
  var Lat = Math.atan2(Z, Hyp);
  return [parseFloat((Lng * 180) / Math.PI), parseFloat((Lat * 180) / Math.PI)];
};

// 配置工厂函数
export const genLayerConfigFactory = () => {};
