class Lifecycle {
  static instance = null;

  constructor() {
    if (Lifecycle.instance) {
      return Lifecycle.instance;
    }
    this.initialized = false;
    this.initCallbacks = [];
    this.destroyCallbacks = [];
    Lifecycle.instance = this;
  }

  static getInstance() {
    if (!Lifecycle.instance) {
      Lifecycle.instance = new Lifecycle();
    }
    return Lifecycle.instance;
  }

  // 注册初始化回调
  onMapInit(callback) {
    if (this.initialized) {
      callback();
    } else {
      this.initCallbacks.push(callback);
    }
  }

  // 初始化完成
  mapInit() {
    this.initialized = true;
    this.initCallbacks.forEach((callback) => callback());
    this.initCallbacks = [];
  }

  // 注册销毁回调
  onMapDestroy(callback) {
    this.destroyCallbacks.push(callback);
  }

  // 销毁实例
  mapDestroy() {
    this.destroyCallbacks.forEach((callback) => callback());
    this.destroyCallbacks = [];
    this.initialized = false;
    Lifecycle.instance = null;
  }
}

export default new Lifecycle();
