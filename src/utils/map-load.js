window.bmapcfg = {
  imgext: ".jpg", //瓦片图的后缀 ------ 根据需要修改，一般是 .png .jpg
  tiles_dir: "", //普通瓦片图的地址，为空默认在 offlinemap/tiles/ 目录
  tiles_hybrid: "", //卫星瓦片图的地址，为空默认在 offlinemap/tiles_hybrid/ 目录
  tiles_self: "", //自定义图层的地址，为空默认在 offlinemap/tiles_self/ 目录
};

//////////////////下面的保持不动///////////////////////////////////
window.bmapcfg.home = "/daas/static/maps/bmap/";
// (function(){
//   window.BMap_loadScriptTime = (new Date).getTime();
//   //加载地图API主文件
//   document.write('<script type="text/javascript" src="'+bmapcfg.home+'bmap_offline_api_v3.0_min.js"></script>');
//  //document.write('<script type="text/javascript" src="'+bmapcfg.home+'bmap_org.js"></script>');
//   //加载扩展函数
//   document.write('<script type="text/javascript" src="'+bmapcfg.home+'map_plus.js"></script>');
//   //加载城市坐标
//   document.write('<script type="text/javascript" src="'+bmapcfg.home+'map_city.js"></script>');
// })();
(function () {
  window.BMap_loadScriptTime = new Date().getTime();
  window.BMap = window.BMap || {};
  window.BMap.apiLoad = function () {
    delete window.BMap.apiLoad;
    if (window.onBMapCallback && typeof onBMapCallback == "function") {
      window.onBMapCallback();
    }
  };

  loadJScript(window.bmapcfg.home + "bmap_offline_api_v3.0_min.js");
  loadJScript(window.bmapcfg.home + "map_plus.js");
  loadJScript(window.bmapcfg.home + "map_city.js");

  function loadJScript(src) {
    var script = document.createElement("script");
    script.type = "text/javascript";
    script.src = src;
    document.body.appendChild(script);
  }
  window.bmapLoadJScript = loadJScript;
})();
///////////////////////////////////////////////////////////////////
