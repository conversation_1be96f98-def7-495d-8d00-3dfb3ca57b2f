import { request } from "@daas/core";

/**
 * 获取文件元数据信息
 */
const getFileInfo = async function (id) {
  return new Promise((resolve, reject) => {
    request({
      method: "get",
      url: `/daasPortal/file/download/${id}`,
      noNotification: true,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        //下载文件没有状态码，所在到错误里来了。
        console.error(err);
        resolve({
          status: "fail",
          msg: "文件未找到",
        });
      });
  });
};

/**
 * 通过附件id从数据资源获取到文件内容
 */
const getFileById = async function (id) {
  return new Promise((resolve) => {
    request({
      method: "post",
      url: "/daasDMS/download/downloadFile",
      data: {
        attachmentId: id,
      },
      noNotification: true,
    })
      .then((res) => {
        //上传成功
        resolve(res);
      })
      .catch((err) => {
        console.error(err);
        resolve({
          status: "fail",
          msg: "文件未找到",
        });
      });
  });
};

const deleteFile = async function (id) {
  return new Promise((resolve) => {
    request({ url: `/daasPortal/file/delete/${id}`, method: "delete" })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        console.error(err);
      });
  });
};

const uploadFile = function (formData) {
  return new Promise((resolve) => {
    request
      .post("/daasPortal/file/uploads", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        console.log(err);
      });
  });
};

const downloadFile = function (filename, text) {
  var element = document.createElement("a");
  element.setAttribute("href", "data:text/plain;charset=utf-8," + encodeURIComponent(text));
  element.setAttribute("download", filename);

  element.style.display = "none";
  document.body.appendChild(element);

  element.click();

  document.body.removeChild(element);
};

const endWith = function (str, endStr) {
  var d = str.length - endStr.length;
  return d >= 0 && str.lastIndexOf(endStr) == d;
};

export default { getFileInfo, getFileById, downloadFile, uploadFile, endWith, deleteFile };
