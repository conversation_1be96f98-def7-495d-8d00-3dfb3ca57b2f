/**
 * 性能优化工具类
 * 提供各种性能优化相关的工具和方法
 */

import { globalResourceManager } from './ResourceManager.js';

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {Number} wait 等待时间
 * @param {Boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(this, args);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {Number} limit 时间限制
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 请求动画帧节流
 * @param {Function} func 要节流的函数
 * @returns {Function} 节流后的函数
 */
export function rafThrottle(func) {
  let rafId = null;
  
  return function executedFunction(...args) {
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, args);
        rafId = null;
      });
    }
  };
}

/**
 * 虚拟滚动管理器
 */
export class VirtualScrollManager {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 50;
    this.containerHeight = options.containerHeight || 400;
    this.buffer = options.buffer || 5;
    this.items = [];
    this.scrollTop = 0;
    this.visibleRange = { start: 0, end: 0 };
  }

  /**
   * 设置数据
   * @param {Array} items 数据项
   */
  setItems(items) {
    this.items = items;
    this.updateVisibleRange();
  }

  /**
   * 更新滚动位置
   * @param {Number} scrollTop 滚动位置
   */
  updateScrollTop(scrollTop) {
    this.scrollTop = scrollTop;
    this.updateVisibleRange();
  }

  /**
   * 更新可见范围
   * @private
   */
  updateVisibleRange() {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    
    this.visibleRange = {
      start: Math.max(0, startIndex - this.buffer),
      end: Math.min(this.items.length, startIndex + visibleCount + this.buffer)
    };
  }

  /**
   * 获取可见项
   * @returns {Array} 可见项数组
   */
  getVisibleItems() {
    return this.items.slice(this.visibleRange.start, this.visibleRange.end);
  }

  /**
   * 获取总高度
   * @returns {Number} 总高度
   */
  getTotalHeight() {
    return this.items.length * this.itemHeight;
  }

  /**
   * 获取偏移量
   * @returns {Number} 偏移量
   */
  getOffset() {
    return this.visibleRange.start * this.itemHeight;
  }
}

/**
 * 图片懒加载管理器
 */
export class ImageLazyLoader {
  constructor(options = {}) {
    this.rootMargin = options.rootMargin || '50px';
    this.threshold = options.threshold || 0.1;
    this.placeholder = options.placeholder || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGNUY1RjUiLz48L3N2Zz4=';
    
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: this.rootMargin,
        threshold: this.threshold
      }
    );
    
    this.loadingImages = new Set();
    this.loadedImages = new Set();
  }

  /**
   * 观察图片元素
   * @param {HTMLImageElement} img 图片元素
   */
  observe(img) {
    if (!img.dataset.src) return;
    
    // 设置占位图
    if (!img.src || img.src === '') {
      img.src = this.placeholder;
    }
    
    this.observer.observe(img);
  }

  /**
   * 取消观察图片元素
   * @param {HTMLImageElement} img 图片元素
   */
  unobserve(img) {
    this.observer.unobserve(img);
  }

  /**
   * 处理交叉观察
   * @param {Array} entries 观察条目
   * @private
   */
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.loadImage(entry.target);
      }
    });
  }

  /**
   * 加载图片
   * @param {HTMLImageElement} img 图片元素
   * @private
   */
  async loadImage(img) {
    if (this.loadingImages.has(img) || this.loadedImages.has(img)) {
      return;
    }

    this.loadingImages.add(img);
    this.observer.unobserve(img);

    try {
      const src = img.dataset.src;
      await this.preloadImage(src);
      
      img.src = src;
      img.classList.add('loaded');
      this.loadedImages.add(img);
    } catch (error) {
      img.classList.add('error');
      console.error('Image load failed:', error);
    } finally {
      this.loadingImages.delete(img);
    }
  }

  /**
   * 预加载图片
   * @param {String} src 图片地址
   * @returns {Promise} 加载 Promise
   * @private
   */
  preloadImage(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = resolve;
      img.onerror = reject;
      img.src = src;
    });
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.observer.disconnect();
    this.loadingImages.clear();
    this.loadedImages.clear();
  }
}

/**
 * 组件缓存管理器
 */
export class ComponentCache {
  constructor(maxSize = 50) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessOrder = [];
  }

  /**
   * 获取缓存的组件
   * @param {String} key 缓存键
   * @returns {Object} 组件实例
   */
  get(key) {
    if (this.cache.has(key)) {
      // 更新访问顺序
      this.updateAccessOrder(key);
      return this.cache.get(key);
    }
    return null;
  }

  /**
   * 设置组件缓存
   * @param {String} key 缓存键
   * @param {Object} component 组件实例
   */
  set(key, component) {
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      // 移除最久未访问的组件
      const oldestKey = this.accessOrder.shift();
      const oldComponent = this.cache.get(oldestKey);
      
      // 销毁组件
      if (oldComponent && oldComponent.$destroy) {
        oldComponent.$destroy();
      }
      
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, component);
    this.updateAccessOrder(key);
  }

  /**
   * 删除缓存
   * @param {String} key 缓存键
   */
  delete(key) {
    if (this.cache.has(key)) {
      const component = this.cache.get(key);
      
      // 销毁组件
      if (component && component.$destroy) {
        component.$destroy();
      }
      
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
    }
  }

  /**
   * 清空缓存
   */
  clear() {
    // 销毁所有组件
    for (const component of this.cache.values()) {
      if (component && component.$destroy) {
        component.$destroy();
      }
    }
    
    this.cache.clear();
    this.accessOrder = [];
  }

  /**
   * 更新访问顺序
   * @param {String} key 缓存键
   * @private
   */
  updateAccessOrder(key) {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  /**
   * 从访问顺序中移除
   * @param {String} key 缓存键
   * @private
   */
  removeFromAccessOrder(key) {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * 获取缓存统计
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys()),
      accessOrder: [...this.accessOrder]
    };
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
  }

  /**
   * 开始性能测量
   * @param {String} name 测量名称
   */
  start(name) {
    this.metrics.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  /**
   * 结束性能测量
   * @param {String} name 测量名称
   * @returns {Number} 持续时间
   */
  end(name) {
    const metric = this.metrics.get(name);
    if (metric) {
      metric.endTime = performance.now();
      metric.duration = metric.endTime - metric.startTime;
      return metric.duration;
    }
    return 0;
  }

  /**
   * 测量函数执行时间
   * @param {String} name 测量名称
   * @param {Function} fn 要测量的函数
   * @returns {*} 函数返回值
   */
  measure(name, fn) {
    this.start(name);
    const result = fn();
    this.end(name);
    return result;
  }

  /**
   * 测量异步函数执行时间
   * @param {String} name 测量名称
   * @param {Function} fn 要测量的异步函数
   * @returns {Promise} 函数返回值
   */
  async measureAsync(name, fn) {
    this.start(name);
    const result = await fn();
    this.end(name);
    return result;
  }

  /**
   * 获取性能指标
   * @param {String} name 测量名称
   * @returns {Object} 性能指标
   */
  getMetric(name) {
    return this.metrics.get(name);
  }

  /**
   * 获取所有性能指标
   * @returns {Object} 所有性能指标
   */
  getAllMetrics() {
    const result = {};
    for (const [name, metric] of this.metrics) {
      result[name] = metric;
    }
    return result;
  }

  /**
   * 清除性能指标
   * @param {String} name 测量名称，不传则清除所有
   */
  clear(name) {
    if (name) {
      this.metrics.delete(name);
    } else {
      this.metrics.clear();
    }
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          console.warn('Long task detected:', {
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name
          });
        }
      });

      observer.observe({ entryTypes: ['longtask'] });
      this.observers.push(observer);
    }
  }

  /**
   * 监控内存使用
   */
  observeMemory() {
    if ('memory' in performance) {
      const logMemory = () => {
        const memory = performance.memory;
        console.log('Memory usage:', {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB',
          limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
        });
      };

      // 每30秒记录一次内存使用
      const intervalId = setInterval(logMemory, 30000);
      globalResourceManager.setInterval(logMemory, 30000);
    }
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();
export const componentCache = new ComponentCache();

export default {
  debounce,
  throttle,
  rafThrottle,
  VirtualScrollManager,
  ImageLazyLoader,
  ComponentCache,
  PerformanceMonitor,
  performanceMonitor,
  componentCache
};
