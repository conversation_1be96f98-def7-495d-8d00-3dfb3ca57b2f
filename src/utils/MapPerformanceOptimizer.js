/**
 * 地图性能优化工具
 * 专门针对地图渲染和覆盖物管理的性能优化
 */

import { debounce, throttle, rafThrottle } from './PerformanceOptimizer.js';
import ErrorCenter from './ErrorCenter.js';

/**
 * 覆盖物池管理器
 * 复用覆盖物实例，减少创建和销毁的开销
 */
export class OverlayPool {
  constructor(options = {}) {
    this.maxSize = options.maxSize || 1000;
    this.pools = {
      marker: [],
      polygon: [],
      polyline: [],
      label: []
    };
    this.activeOverlays = new Set();
  }

  /**
   * 获取覆盖物实例
   * @param {String} type 覆盖物类型
   * @param {Object} options 配置选项
   * @returns {Object} 覆盖物实例
   */
  get(type, options = {}) {
    const pool = this.pools[type];
    
    if (pool && pool.length > 0) {
      const overlay = pool.pop();
      this.activeOverlays.add(overlay);
      this.resetOverlay(overlay, options);
      return overlay;
    }

    // 池中没有可用实例，创建新的
    const overlay = this.createOverlay(type, options);
    this.activeOverlays.add(overlay);
    return overlay;
  }

  /**
   * 归还覆盖物实例到池中
   * @param {String} type 覆盖物类型
   * @param {Object} overlay 覆盖物实例
   */
  release(type, overlay) {
    if (!this.activeOverlays.has(overlay)) {
      return;
    }

    this.activeOverlays.delete(overlay);
    
    const pool = this.pools[type];
    if (pool && pool.length < this.maxSize) {
      this.cleanOverlay(overlay);
      pool.push(overlay);
    } else {
      // 池已满，直接销毁
      this.destroyOverlay(overlay);
    }
  }

  /**
   * 创建新的覆盖物实例
   * @param {String} type 覆盖物类型
   * @param {Object} options 配置选项
   * @returns {Object} 覆盖物实例
   * @private
   */
  createOverlay(type, options) {
    switch (type) {
      case 'marker':
        return new BMap.Marker(new BMap.Point(0, 0), options);
      case 'polygon':
        return new BMap.Polygon([], options);
      case 'polyline':
        return new BMap.Polyline([], options);
      case 'label':
        return new BMap.Label('', options);
      default:
        throw new Error(`Unknown overlay type: ${type}`);
    }
  }

  /**
   * 重置覆盖物状态
   * @param {Object} overlay 覆盖物实例
   * @param {Object} options 新的配置选项
   * @private
   */
  resetOverlay(overlay, options) {
    // 根据覆盖物类型重置状态
    if (overlay instanceof BMap.Marker) {
      if (options.position) overlay.setPosition(options.position);
      if (options.icon) overlay.setIcon(options.icon);
    } else if (overlay instanceof BMap.Polygon) {
      if (options.path) overlay.setPath(options.path);
      if (options.strokeColor) overlay.setStrokeColor(options.strokeColor);
      if (options.fillColor) overlay.setFillColor(options.fillColor);
    } else if (overlay instanceof BMap.Polyline) {
      if (options.path) overlay.setPath(options.path);
      if (options.strokeColor) overlay.setStrokeColor(options.strokeColor);
    } else if (overlay instanceof BMap.Label) {
      if (options.content) overlay.setContent(options.content);
      if (options.position) overlay.setPosition(options.position);
    }
  }

  /**
   * 清理覆盖物状态
   * @param {Object} overlay 覆盖物实例
   * @private
   */
  cleanOverlay(overlay) {
    // 移除所有事件监听器
    overlay.removeEventListener();
    
    // 隐藏覆盖物
    overlay.hide();
  }

  /**
   * 销毁覆盖物
   * @param {Object} overlay 覆盖物实例
   * @private
   */
  destroyOverlay(overlay) {
    if (overlay && typeof overlay.destroy === 'function') {
      overlay.destroy();
    }
  }

  /**
   * 清空所有池
   */
  clear() {
    Object.values(this.pools).forEach(pool => {
      pool.forEach(overlay => this.destroyOverlay(overlay));
      pool.length = 0;
    });
    
    this.activeOverlays.clear();
  }

  /**
   * 获取池统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      active: this.activeOverlays.size,
      pools: {}
    };

    Object.keys(this.pools).forEach(type => {
      stats.pools[type] = this.pools[type].length;
    });

    return stats;
  }
}

/**
 * 视口管理器
 * 只渲染视口内的覆盖物，提升大量数据的渲染性能
 */
export class ViewportManager {
  constructor(map, options = {}) {
    this.map = map;
    this.buffer = options.buffer || 0.2; // 视口缓冲区比例
    this.updateDelay = options.updateDelay || 100;
    
    this.allOverlays = new Map();
    this.visibleOverlays = new Set();
    this.currentBounds = null;
    
    this.debouncedUpdate = debounce(this.updateVisibleOverlays.bind(this), this.updateDelay);
    
    this.bindEvents();
  }

  /**
   * 绑定地图事件
   * @private
   */
  bindEvents() {
    this.map.addEventListener('moveend', this.debouncedUpdate);
    this.map.addEventListener('zoomend', this.debouncedUpdate);
    this.map.addEventListener('resize', this.debouncedUpdate);
  }

  /**
   * 添加覆盖物到管理器
   * @param {String} id 覆盖物ID
   * @param {Object} overlay 覆盖物实例
   * @param {Object} bounds 覆盖物边界
   */
  addOverlay(id, overlay, bounds) {
    this.allOverlays.set(id, { overlay, bounds });
    this.checkOverlayVisibility(id);
  }

  /**
   * 移除覆盖物
   * @param {String} id 覆盖物ID
   */
  removeOverlay(id) {
    const item = this.allOverlays.get(id);
    if (item) {
      this.hideOverlay(item.overlay);
      this.visibleOverlays.delete(id);
      this.allOverlays.delete(id);
    }
  }

  /**
   * 更新可见覆盖物
   * @private
   */
  updateVisibleOverlays() {
    this.currentBounds = this.getExpandedBounds();
    
    for (const [id, item] of this.allOverlays) {
      this.checkOverlayVisibility(id);
    }
  }

  /**
   * 检查覆盖物可见性
   * @param {String} id 覆盖物ID
   * @private
   */
  checkOverlayVisibility(id) {
    const item = this.allOverlays.get(id);
    if (!item) return;

    const isVisible = this.isOverlayInBounds(item.bounds);
    const wasVisible = this.visibleOverlays.has(id);

    if (isVisible && !wasVisible) {
      this.showOverlay(item.overlay);
      this.visibleOverlays.add(id);
    } else if (!isVisible && wasVisible) {
      this.hideOverlay(item.overlay);
      this.visibleOverlays.delete(id);
    }
  }

  /**
   * 判断覆盖物是否在边界内
   * @param {Object} overlayBounds 覆盖物边界
   * @returns {Boolean} 是否在边界内
   * @private
   */
  isOverlayInBounds(overlayBounds) {
    if (!this.currentBounds || !overlayBounds) return true;

    return !(
      overlayBounds.maxLng < this.currentBounds.minLng ||
      overlayBounds.minLng > this.currentBounds.maxLng ||
      overlayBounds.maxLat < this.currentBounds.minLat ||
      overlayBounds.minLat > this.currentBounds.maxLat
    );
  }

  /**
   * 获取扩展后的地图边界
   * @returns {Object} 扩展边界
   * @private
   */
  getExpandedBounds() {
    const bounds = this.map.getBounds();
    const sw = bounds.getSouthWest();
    const ne = bounds.getNorthEast();
    
    const lngSpan = ne.lng - sw.lng;
    const latSpan = ne.lat - sw.lat;
    
    const bufferLng = lngSpan * this.buffer;
    const bufferLat = latSpan * this.buffer;
    
    return {
      minLng: sw.lng - bufferLng,
      maxLng: ne.lng + bufferLng,
      minLat: sw.lat - bufferLat,
      maxLat: ne.lat + bufferLat
    };
  }

  /**
   * 显示覆盖物
   * @param {Object} overlay 覆盖物实例
   * @private
   */
  showOverlay(overlay) {
    if (overlay && typeof overlay.show === 'function') {
      overlay.show();
    }
  }

  /**
   * 隐藏覆盖物
   * @param {Object} overlay 覆盖物实例
   * @private
   */
  hideOverlay(overlay) {
    if (overlay && typeof overlay.hide === 'function') {
      overlay.hide();
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      total: this.allOverlays.size,
      visible: this.visibleOverlays.size,
      hidden: this.allOverlays.size - this.visibleOverlays.size
    };
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.map.removeEventListener('moveend', this.debouncedUpdate);
    this.map.removeEventListener('zoomend', this.debouncedUpdate);
    this.map.removeEventListener('resize', this.debouncedUpdate);
    
    this.allOverlays.clear();
    this.visibleOverlays.clear();
  }
}

/**
 * 聚合管理器
 * 在高缩放级别时聚合密集的覆盖物
 */
export class ClusterManager {
  constructor(map, options = {}) {
    this.map = map;
    this.gridSize = options.gridSize || 60;
    this.maxZoom = options.maxZoom || 18;
    this.minClusterSize = options.minClusterSize || 2;
    
    this.markers = [];
    this.clusters = [];
    this.clusterOverlays = [];
    
    this.throttledUpdate = throttle(this.updateClusters.bind(this), 100);
    
    this.bindEvents();
  }

  /**
   * 绑定地图事件
   * @private
   */
  bindEvents() {
    this.map.addEventListener('zoomend', this.throttledUpdate);
    this.map.addEventListener('moveend', this.throttledUpdate);
  }

  /**
   * 添加标记点
   * @param {Object} marker 标记点数据
   */
  addMarker(marker) {
    this.markers.push(marker);
    this.throttledUpdate();
  }

  /**
   * 移除标记点
   * @param {Object} marker 标记点数据
   */
  removeMarker(marker) {
    const index = this.markers.indexOf(marker);
    if (index > -1) {
      this.markers.splice(index, 1);
      this.throttledUpdate();
    }
  }

  /**
   * 更新聚合
   * @private
   */
  updateClusters() {
    this.clearClusters();
    
    const zoom = this.map.getZoom();
    
    if (zoom >= this.maxZoom) {
      // 高缩放级别，显示所有标记点
      this.showAllMarkers();
    } else {
      // 低缩放级别，进行聚合
      this.createClusters();
      this.renderClusters();
    }
  }

  /**
   * 创建聚合
   * @private
   */
  createClusters() {
    const clusters = [];
    const processed = new Set();
    
    for (let i = 0; i < this.markers.length; i++) {
      if (processed.has(i)) continue;
      
      const marker = this.markers[i];
      const cluster = {
        center: marker.position,
        markers: [marker],
        bounds: this.createBounds(marker.position)
      };
      
      // 查找附近的标记点
      for (let j = i + 1; j < this.markers.length; j++) {
        if (processed.has(j)) continue;
        
        const otherMarker = this.markers[j];
        if (this.isInClusterRange(marker.position, otherMarker.position)) {
          cluster.markers.push(otherMarker);
          this.expandBounds(cluster.bounds, otherMarker.position);
          processed.add(j);
        }
      }
      
      processed.add(i);
      
      if (cluster.markers.length >= this.minClusterSize) {
        cluster.center = this.calculateClusterCenter(cluster.markers);
        clusters.push(cluster);
      } else {
        // 单独显示
        this.showMarker(marker);
      }
    }
    
    this.clusters = clusters;
  }

  /**
   * 渲染聚合
   * @private
   */
  renderClusters() {
    this.clusters.forEach(cluster => {
      const clusterOverlay = this.createClusterOverlay(cluster);
      this.map.addOverlay(clusterOverlay);
      this.clusterOverlays.push(clusterOverlay);
    });
  }

  /**
   * 创建聚合覆盖物
   * @param {Object} cluster 聚合数据
   * @returns {Object} 聚合覆盖物
   * @private
   */
  createClusterOverlay(cluster) {
    const size = cluster.markers.length;
    const icon = this.createClusterIcon(size);
    
    const marker = new BMap.Marker(cluster.center, { icon });
    
    // 添加点击事件
    marker.addEventListener('click', () => {
      this.onClusterClick(cluster);
    });
    
    return marker;
  }

  /**
   * 创建聚合图标
   * @param {Number} size 聚合大小
   * @returns {Object} 图标对象
   * @private
   */
  createClusterIcon(size) {
    const iconSize = Math.min(60, 30 + size * 2);
    
    return new BMap.Icon(
      `data:image/svg+xml;base64,${btoa(`
        <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 ${iconSize} ${iconSize}" xmlns="http://www.w3.org/2000/svg">
          <circle cx="${iconSize/2}" cy="${iconSize/2}" r="${iconSize/2-2}" fill="#1890ff" stroke="#fff" stroke-width="2"/>
          <text x="${iconSize/2}" y="${iconSize/2+4}" text-anchor="middle" fill="white" font-size="12" font-weight="bold">${size}</text>
        </svg>
      `)}`,
      new BMap.Size(iconSize, iconSize)
    );
  }

  /**
   * 处理聚合点击事件
   * @param {Object} cluster 聚合数据
   * @private
   */
  onClusterClick(cluster) {
    // 缩放到聚合边界
    const bounds = new BMap.Bounds(
      new BMap.Point(cluster.bounds.minLng, cluster.bounds.minLat),
      new BMap.Point(cluster.bounds.maxLng, cluster.bounds.maxLat)
    );
    
    this.map.setViewport(bounds);
  }

  /**
   * 判断是否在聚合范围内
   * @param {Object} pos1 位置1
   * @param {Object} pos2 位置2
   * @returns {Boolean} 是否在范围内
   * @private
   */
  isInClusterRange(pos1, pos2) {
    const pixel1 = this.map.pointToPixel(pos1);
    const pixel2 = this.map.pointToPixel(pos2);
    
    const distance = Math.sqrt(
      Math.pow(pixel1.x - pixel2.x, 2) + Math.pow(pixel1.y - pixel2.y, 2)
    );
    
    return distance <= this.gridSize;
  }

  /**
   * 计算聚合中心点
   * @param {Array} markers 标记点数组
   * @returns {Object} 中心点
   * @private
   */
  calculateClusterCenter(markers) {
    let totalLng = 0;
    let totalLat = 0;
    
    markers.forEach(marker => {
      totalLng += marker.position.lng;
      totalLat += marker.position.lat;
    });
    
    return new BMap.Point(
      totalLng / markers.length,
      totalLat / markers.length
    );
  }

  /**
   * 创建边界
   * @param {Object} position 位置
   * @returns {Object} 边界对象
   * @private
   */
  createBounds(position) {
    return {
      minLng: position.lng,
      maxLng: position.lng,
      minLat: position.lat,
      maxLat: position.lat
    };
  }

  /**
   * 扩展边界
   * @param {Object} bounds 边界对象
   * @param {Object} position 位置
   * @private
   */
  expandBounds(bounds, position) {
    bounds.minLng = Math.min(bounds.minLng, position.lng);
    bounds.maxLng = Math.max(bounds.maxLng, position.lng);
    bounds.minLat = Math.min(bounds.minLat, position.lat);
    bounds.maxLat = Math.max(bounds.maxLat, position.lat);
  }

  /**
   * 显示所有标记点
   * @private
   */
  showAllMarkers() {
    this.markers.forEach(marker => this.showMarker(marker));
  }

  /**
   * 显示单个标记点
   * @param {Object} marker 标记点
   * @private
   */
  showMarker(marker) {
    if (marker.overlay) {
      this.map.addOverlay(marker.overlay);
    }
  }

  /**
   * 清除聚合
   * @private
   */
  clearClusters() {
    // 移除聚合覆盖物
    this.clusterOverlays.forEach(overlay => {
      this.map.removeOverlay(overlay);
    });
    this.clusterOverlays = [];
    
    // 隐藏所有标记点
    this.markers.forEach(marker => {
      if (marker.overlay) {
        this.map.removeOverlay(marker.overlay);
      }
    });
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.clearClusters();
    this.map.removeEventListener('zoomend', this.throttledUpdate);
    this.map.removeEventListener('moveend', this.throttledUpdate);
  }
}

// 创建全局实例
export const overlayPool = new OverlayPool();

export default {
  OverlayPool,
  ViewportManager,
  ClusterManager,
  overlayPool
};
