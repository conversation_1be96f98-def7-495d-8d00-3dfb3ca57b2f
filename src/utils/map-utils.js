import store from "@/store/index.js";
import { cloneDeep } from "lodash";
let selectedCell = null;

export function setSelectedCell(cell) {
  selectedCell = cell;
}

export function getSelectedCell() {
  return selectedCell;
}

export function getAllLayerConfig() {
  const elements = [];
  const layers = store.getters["layers"];

  if (layers && layers.length > 0) {
    for (let i = 0; i < layers.length; i++) {
      const currLayer = layers[i];
      const currConfig = currLayer.layerConfig;
      const isMarker = currConfig.type === "marker";

      // 静态
      if (currLayer.isStatic) {
        layers[i].layerConfig.localData = currLayer.staticStore.getAllLocalData();
      }

      layers[i].layerConfig.styleConfig = isMarker
        ? currLayer.markerConfig
        : currLayer.polygonConfig;

      const cells = isMarker ? currLayer.markers : currLayer.polygons;
      const cellConfigs = cells.map((cell) => cloneDeep(cell.exportConfig()));
      layers[i].layerConfig.coverCells = cellConfigs;

      // 保存配置
      elements.push(layers[i].layerConfig);
    }
  }

  return {
    elements: elements,
  };
}
