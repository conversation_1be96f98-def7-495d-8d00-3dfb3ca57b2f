/**
 * 资源管理器
 * 统一管理应用中的资源，防止内存泄漏
 */

/**
 * 资源管理器类
 */
export class ResourceManager {
  constructor() {
    this.resources = new Map();
    this.eventListeners = new Set();
    this.timers = new Set();
    this.intervals = new Set();
    this.observers = new Set();
    this.subscriptions = new Set();
  }

  /**
   * 注册资源
   * @param {String} id 资源ID
   * @param {Object} resource 资源对象
   * @param {Function} cleanup 清理函数
   */
  register(id, resource, cleanup) {
    if (this.resources.has(id)) {
      console.warn(`Resource ${id} already exists, replacing...`);
      this.unregister(id);
    }

    this.resources.set(id, {
      resource,
      cleanup: cleanup || (() => {}),
      createdAt: Date.now()
    });
  }

  /**
   * 注销资源
   * @param {String} id 资源ID
   */
  unregister(id) {
    const resourceInfo = this.resources.get(id);
    if (resourceInfo) {
      try {
        resourceInfo.cleanup(resourceInfo.resource);
      } catch (error) {
        console.error(`Error cleaning up resource ${id}:`, error);
      }
      this.resources.delete(id);
    }
  }

  /**
   * 获取资源
   * @param {String} id 资源ID
   * @returns {Object} 资源对象
   */
  get(id) {
    const resourceInfo = this.resources.get(id);
    return resourceInfo ? resourceInfo.resource : null;
  }

  /**
   * 添加事件监听器
   * @param {Object} target 目标对象
   * @param {String} event 事件名
   * @param {Function} handler 处理函数
   * @param {Object} options 选项
   */
  addEventListener(target, event, handler, options = {}) {
    target.addEventListener(event, handler, options);
    
    const listenerInfo = {
      target,
      event,
      handler,
      options,
      id: `${event}_${Date.now()}_${Math.random()}`
    };
    
    this.eventListeners.add(listenerInfo);
    return listenerInfo.id;
  }

  /**
   * 移除事件监听器
   * @param {String} listenerId 监听器ID
   */
  removeEventListener(listenerId) {
    for (const listener of this.eventListeners) {
      if (listener.id === listenerId) {
        listener.target.removeEventListener(listener.event, listener.handler, listener.options);
        this.eventListeners.delete(listener);
        break;
      }
    }
  }

  /**
   * 创建定时器
   * @param {Function} callback 回调函数
   * @param {Number} delay 延迟时间
   * @returns {Number} 定时器ID
   */
  setTimeout(callback, delay) {
    const timerId = setTimeout(() => {
      callback();
      this.timers.delete(timerId);
    }, delay);
    
    this.timers.add(timerId);
    return timerId;
  }

  /**
   * 清除定时器
   * @param {Number} timerId 定时器ID
   */
  clearTimeout(timerId) {
    clearTimeout(timerId);
    this.timers.delete(timerId);
  }

  /**
   * 创建间隔定时器
   * @param {Function} callback 回调函数
   * @param {Number} interval 间隔时间
   * @returns {Number} 定时器ID
   */
  setInterval(callback, interval) {
    const intervalId = setInterval(callback, interval);
    this.intervals.add(intervalId);
    return intervalId;
  }

  /**
   * 清除间隔定时器
   * @param {Number} intervalId 定时器ID
   */
  clearInterval(intervalId) {
    clearInterval(intervalId);
    this.intervals.delete(intervalId);
  }

  /**
   * 添加观察者
   * @param {Object} observer 观察者对象
   */
  addObserver(observer) {
    this.observers.add(observer);
  }

  /**
   * 移除观察者
   * @param {Object} observer 观察者对象
   */
  removeObserver(observer) {
    if (observer && typeof observer.disconnect === 'function') {
      observer.disconnect();
    }
    this.observers.delete(observer);
  }

  /**
   * 添加订阅
   * @param {Object} subscription 订阅对象
   */
  addSubscription(subscription) {
    this.subscriptions.add(subscription);
  }

  /**
   * 移除订阅
   * @param {Object} subscription 订阅对象
   */
  removeSubscription(subscription) {
    if (subscription && typeof subscription.unsubscribe === 'function') {
      subscription.unsubscribe();
    }
    this.subscriptions.delete(subscription);
  }

  /**
   * 清理所有事件监听器
   */
  clearAllEventListeners() {
    for (const listener of this.eventListeners) {
      try {
        listener.target.removeEventListener(listener.event, listener.handler, listener.options);
      } catch (error) {
        console.error('Error removing event listener:', error);
      }
    }
    this.eventListeners.clear();
  }

  /**
   * 清理所有定时器
   */
  clearAllTimers() {
    for (const timerId of this.timers) {
      clearTimeout(timerId);
    }
    this.timers.clear();

    for (const intervalId of this.intervals) {
      clearInterval(intervalId);
    }
    this.intervals.clear();
  }

  /**
   * 清理所有观察者
   */
  clearAllObservers() {
    for (const observer of this.observers) {
      try {
        if (observer && typeof observer.disconnect === 'function') {
          observer.disconnect();
        }
      } catch (error) {
        console.error('Error disconnecting observer:', error);
      }
    }
    this.observers.clear();
  }

  /**
   * 清理所有订阅
   */
  clearAllSubscriptions() {
    for (const subscription of this.subscriptions) {
      try {
        if (subscription && typeof subscription.unsubscribe === 'function') {
          subscription.unsubscribe();
        }
      } catch (error) {
        console.error('Error unsubscribing:', error);
      }
    }
    this.subscriptions.clear();
  }

  /**
   * 清理所有资源
   */
  clearAll() {
    // 清理注册的资源
    for (const [id] of this.resources) {
      this.unregister(id);
    }

    // 清理事件监听器
    this.clearAllEventListeners();

    // 清理定时器
    this.clearAllTimers();

    // 清理观察者
    this.clearAllObservers();

    // 清理订阅
    this.clearAllSubscriptions();
  }

  /**
   * 获取资源统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      resources: this.resources.size,
      eventListeners: this.eventListeners.size,
      timers: this.timers.size,
      intervals: this.intervals.size,
      observers: this.observers.size,
      subscriptions: this.subscriptions.size
    };
  }

  /**
   * 检查是否有潜在的内存泄漏
   * @returns {Object} 检查结果
   */
  checkMemoryLeaks() {
    const stats = this.getStats();
    const warnings = [];

    if (stats.eventListeners > 50) {
      warnings.push(`Too many event listeners: ${stats.eventListeners}`);
    }

    if (stats.timers > 20) {
      warnings.push(`Too many timers: ${stats.timers}`);
    }

    if (stats.intervals > 10) {
      warnings.push(`Too many intervals: ${stats.intervals}`);
    }

    if (stats.resources > 100) {
      warnings.push(`Too many resources: ${stats.resources}`);
    }

    return {
      hasLeaks: warnings.length > 0,
      warnings,
      stats
    };
  }

  /**
   * 销毁资源管理器
   */
  destroy() {
    this.clearAll();
  }
}

// 创建全局资源管理器实例
export const globalResourceManager = new ResourceManager();

// 在页面卸载时自动清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    globalResourceManager.clearAll();
  });
}

export default ResourceManager;
