/**
 * 事件总线
 * 使用函数式编程实现简单的发布订阅模式
 */

// 事件存储对象
const events = new Map();

// 订阅事件
const on = (event, callback) => {
  if (!events.has(event)) {
    events.set(event, []);
  }
  events.get(event).push(callback);

  // 返回取消订阅函数
  return () => off(event, callback);
};

// 取消订阅
const off = (event, callback) => {
  if (!events.has(event)) return;

  const callbacks = events.get(event);
  const index = callbacks.indexOf(callback);

  if (index !== -1) {
    callbacks.splice(index, 1);
  }

  if (callbacks.length === 0) {
    events.delete(event);
  }
};

// 触发事件
const emit = (event, ...args) => {
  if (!events.has(event)) return;

  events.get(event).forEach((callback) => {
    callback(...args);
  });
};

// 只订阅一次
const once = (event, callback) => {
  const wrapper = (...args) => {
    callback(...args);
    off(event, wrapper);
  };

  on(event, wrapper);
};

export default {
  on,
  off,
  emit,
  once,
};

export const eventMap = {
  mapInit: "mapInit",
  mapDestroy: "mapDestroy",
  provincePolygonConfigChange: "provincePolygonConfigChange",
  mapSettingsChange: "mapSettingsChange",
  startSetProvince: "startSetProvince",
  endSetProvince: "endSetProvince",
};
