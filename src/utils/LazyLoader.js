/**
 * 懒加载工具类
 * 提供组件、模块、资源的懒加载功能
 */

import ErrorCenter, { ERROR_TYPES } from './ErrorCenter.js';

/**
 * 懒加载管理器
 */
export class LazyLoader {
  static cache = new Map();
  static loadingPromises = new Map();

  /**
   * 懒加载 Vue 组件
   * @param {Function} importFn 动态导入函数
   * @param {Object} options 选项
   * @returns {Function} Vue 异步组件
   */
  static component(importFn, options = {}) {
    const {
      loading = null,
      error = null,
      delay = 200,
      timeout = 10000,
      retry = 3
    } = options;

    return () => ({
      component: this.withRetry(importFn, retry),
      loading,
      error,
      delay,
      timeout
    });
  }

  /**
   * 懒加载模块
   * @param {String} modulePath 模块路径
   * @param {Object} options 选项
   * @returns {Promise} 模块导入 Promise
   */
  static async module(modulePath, options = {}) {
    const { cache = true, retry = 3 } = options;

    // 检查缓存
    if (cache && this.cache.has(modulePath)) {
      return this.cache.get(modulePath);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(modulePath)) {
      return this.loadingPromises.get(modulePath);
    }

    try {
      const loadingPromise = this.withRetry(() => import(modulePath), retry);
      this.loadingPromises.set(modulePath, loadingPromise);

      const module = await loadingPromise;

      // 缓存结果
      if (cache) {
        this.cache.set(modulePath, module);
      }

      this.loadingPromises.delete(modulePath);
      return module;
    } catch (error) {
      this.loadingPromises.delete(modulePath);
      ErrorCenter.handle(error, `LazyLoader.module[${modulePath}]`);
      throw error;
    }
  }

  /**
   * 懒加载脚本
   * @param {String} src 脚本地址
   * @param {Object} options 选项
   * @returns {Promise} 加载 Promise
   */
  static async script(src, options = {}) {
    const { 
      cache = true, 
      timeout = 10000,
      integrity = null,
      crossOrigin = null 
    } = options;

    // 检查缓存
    if (cache && this.cache.has(src)) {
      return this.cache.get(src);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    const loadingPromise = new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.async = true;

      if (integrity) {
        script.integrity = integrity;
      }

      if (crossOrigin) {
        script.crossOrigin = crossOrigin;
      }

      const timeoutId = setTimeout(() => {
        reject(new Error(`Script load timeout: ${src}`));
      }, timeout);

      script.onload = () => {
        clearTimeout(timeoutId);
        resolve(script);
      };

      script.onerror = () => {
        clearTimeout(timeoutId);
        reject(new Error(`Script load error: ${src}`));
      };

      document.head.appendChild(script);
    });

    this.loadingPromises.set(src, loadingPromise);

    try {
      const script = await loadingPromise;
      
      if (cache) {
        this.cache.set(src, script);
      }

      this.loadingPromises.delete(src);
      return script;
    } catch (error) {
      this.loadingPromises.delete(src);
      ErrorCenter.handle(error, `LazyLoader.script[${src}]`);
      throw error;
    }
  }

  /**
   * 懒加载样式
   * @param {String} href 样式地址
   * @param {Object} options 选项
   * @returns {Promise} 加载 Promise
   */
  static async style(href, options = {}) {
    const { cache = true, timeout = 10000 } = options;

    // 检查缓存
    if (cache && this.cache.has(href)) {
      return this.cache.get(href);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(href)) {
      return this.loadingPromises.get(href);
    }

    const loadingPromise = new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;

      const timeoutId = setTimeout(() => {
        reject(new Error(`Style load timeout: ${href}`));
      }, timeout);

      link.onload = () => {
        clearTimeout(timeoutId);
        resolve(link);
      };

      link.onerror = () => {
        clearTimeout(timeoutId);
        reject(new Error(`Style load error: ${href}`));
      };

      document.head.appendChild(link);
    });

    this.loadingPromises.set(href, loadingPromise);

    try {
      const link = await loadingPromise;
      
      if (cache) {
        this.cache.set(href, link);
      }

      this.loadingPromises.delete(href);
      return link;
    } catch (error) {
      this.loadingPromises.delete(href);
      ErrorCenter.handle(error, `LazyLoader.style[${href}]`);
      throw error;
    }
  }

  /**
   * 预加载资源
   * @param {Array} resources 资源列表
   * @param {Object} options 选项
   */
  static async preload(resources, options = {}) {
    const { concurrency = 3 } = options;
    
    const chunks = this.chunkArray(resources, concurrency);
    
    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(resource => this.loadResource(resource))
      );
    }
  }

  /**
   * 加载单个资源
   * @param {Object} resource 资源配置
   * @returns {Promise} 加载 Promise
   * @private
   */
  static async loadResource(resource) {
    const { type, path, options = {} } = resource;

    switch (type) {
      case 'component':
      case 'module':
        return this.module(path, options);
      case 'script':
        return this.script(path, options);
      case 'style':
        return this.style(path, options);
      default:
        throw new Error(`Unknown resource type: ${type}`);
    }
  }

  /**
   * 带重试的加载
   * @param {Function} loadFn 加载函数
   * @param {Number} maxRetries 最大重试次数
   * @returns {Promise} 加载 Promise
   * @private
   */
  static async withRetry(loadFn, maxRetries = 3) {
    let lastError;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await loadFn();
      } catch (error) {
        lastError = error;
        
        if (i < maxRetries) {
          // 指数退避
          const delay = Math.pow(2, i) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * 数组分块
   * @param {Array} array 数组
   * @param {Number} size 块大小
   * @returns {Array} 分块后的数组
   * @private
   */
  static chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 清除缓存
   * @param {String} key 缓存键，不传则清除所有
   */
  static clearCache(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计信息
   */
  static getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      loadingCount: this.loadingPromises.size
    };
  }
}

/**
 * 路由懒加载助手
 */
export class RouteLoader {
  /**
   * 创建懒加载路由
   * @param {String} path 组件路径
   * @param {Object} options 选项
   * @returns {Function} 路由组件函数
   */
  static create(path, options = {}) {
    return LazyLoader.component(() => import(path), {
      loading: this.createLoadingComponent(),
      error: this.createErrorComponent(),
      ...options
    });
  }

  /**
   * 创建加载中组件
   * @returns {Object} Vue 组件
   * @private
   */
  static createLoadingComponent() {
    return {
      template: `
        <div class="route-loading">
          <div class="loading-spinner"></div>
          <p>页面加载中...</p>
        </div>
      `,
      style: `
        .route-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 200px;
        }
        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `
    };
  }

  /**
   * 创建错误组件
   * @returns {Object} Vue 组件
   * @private
   */
  static createErrorComponent() {
    return {
      template: `
        <div class="route-error">
          <h3>页面加载失败</h3>
          <p>请检查网络连接或刷新页面重试</p>
          <button @click="retry">重试</button>
        </div>
      `,
      methods: {
        retry() {
          window.location.reload();
        }
      }
    };
  }
}

export default LazyLoader;
