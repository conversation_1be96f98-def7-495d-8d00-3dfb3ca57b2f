/**
 * 错误处理中心
 * 统一管理应用中的错误处理逻辑
 */

// 错误类型枚举
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  MAP_LOAD_ERROR: 'MAP_LOAD_ERROR',
  DATA_PARSE_ERROR: 'DATA_PARSE_ERROR',
  LAYER_ERROR: 'LAYER_ERROR',
  OVERLAY_ERROR: 'OVERLAY_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

// 错误级别枚举
export const ERROR_LEVELS = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
};

/**
 * 错误处理中心类
 */
export class ErrorCenter {
  static handlers = new Map();
  static errorLog = [];
  static maxLogSize = 100;

  /**
   * 注册错误处理器
   * @param {String} type 错误类型
   * @param {Function} handler 处理函数
   */
  static register(type, handler) {
    if (typeof handler !== 'function') {
      throw new Error('Error handler must be a function');
    }
    this.handlers.set(type, handler);
  }

  /**
   * 处理错误
   * @param {Error|Object} error 错误对象
   * @param {String} context 上下文信息
   * @param {Object} options 选项
   */
  static handle(error, context = 'unknown', options = {}) {
    const errorInfo = this._normalizeError(error, context, options);
    
    // 记录错误日志
    this._logError(errorInfo);
    
    // 获取对应的处理器
    const handler = this.handlers.get(errorInfo.type) || this.defaultHandler;
    
    try {
      handler(errorInfo, context, options);
    } catch (handlerError) {
      console.error('Error handler failed:', handlerError);
      this.defaultHandler(errorInfo, context, options);
    }
  }

  /**
   * 默认错误处理器
   * @param {Object} errorInfo 错误信息
   * @param {String} context 上下文
   * @param {Object} options 选项
   */
  static defaultHandler(errorInfo, context, options = {}) {
    // 控制台输出
    console.error(`[${context}]`, errorInfo);
    
    // 用户提示
    if (!options.silent) {
      this._showUserMessage(errorInfo);
    }
    
    // 错误上报
    if (options.report !== false) {
      this._reportError(errorInfo, context);
    }
  }

  /**
   * 标准化错误对象
   * @param {Error|Object} error 错误
   * @param {String} context 上下文
   * @param {Object} options 选项
   * @returns {Object} 标准化的错误对象
   * @private
   */
  static _normalizeError(error, context, options) {
    let errorInfo = {
      type: ERROR_TYPES.UNKNOWN_ERROR,
      level: ERROR_LEVELS.ERROR,
      message: 'Unknown error occurred',
      stack: null,
      timestamp: new Date().toISOString(),
      context,
      ...options
    };

    if (error instanceof Error) {
      errorInfo.message = error.message;
      errorInfo.stack = error.stack;
      errorInfo.type = this._detectErrorType(error);
    } else if (typeof error === 'object' && error !== null) {
      errorInfo = { ...errorInfo, ...error };
    } else if (typeof error === 'string') {
      errorInfo.message = error;
    }

    return errorInfo;
  }

  /**
   * 检测错误类型
   * @param {Error} error 错误对象
   * @returns {String} 错误类型
   * @private
   */
  static _detectErrorType(error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return ERROR_TYPES.NETWORK_ERROR;
    }
    
    if (message.includes('not found') || error.status === 404) {
      return ERROR_TYPES.RESOURCE_NOT_FOUND;
    }
    
    if (message.includes('permission') || error.status === 403) {
      return ERROR_TYPES.PERMISSION_DENIED;
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
      return ERROR_TYPES.VALIDATION_ERROR;
    }
    
    if (message.includes('map') || message.includes('bmap')) {
      return ERROR_TYPES.MAP_LOAD_ERROR;
    }
    
    if (message.includes('parse') || message.includes('json')) {
      return ERROR_TYPES.DATA_PARSE_ERROR;
    }
    
    return ERROR_TYPES.UNKNOWN_ERROR;
  }

  /**
   * 记录错误日志
   * @param {Object} errorInfo 错误信息
   * @private
   */
  static _logError(errorInfo) {
    this.errorLog.push(errorInfo);
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }
  }

  /**
   * 显示用户友好的错误提示
   * @param {Object} errorInfo 错误信息
   * @private
   */
  static _showUserMessage(errorInfo) {
    const userMessages = {
      [ERROR_TYPES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
      [ERROR_TYPES.RESOURCE_NOT_FOUND]: '请求的资源不存在',
      [ERROR_TYPES.PERMISSION_DENIED]: '权限不足，无法执行此操作',
      [ERROR_TYPES.MAP_LOAD_ERROR]: '地图加载失败，请刷新页面重试',
      [ERROR_TYPES.DATA_PARSE_ERROR]: '数据格式错误，请检查数据源',
      [ERROR_TYPES.LAYER_ERROR]: '图层操作失败',
      [ERROR_TYPES.OVERLAY_ERROR]: '覆盖物操作失败',
      [ERROR_TYPES.VALIDATION_ERROR]: '输入数据不符合要求',
      [ERROR_TYPES.UNKNOWN_ERROR]: '操作失败，请稍后重试'
    };

    const message = userMessages[errorInfo.type] || errorInfo.message || '操作失败';
    
    // 这里可以集成具体的 UI 组件库
    if (typeof window !== 'undefined' && window.$message) {
      // Ant Design Vue 的消息提示
      window.$message.error(message);
    } else if (typeof window !== 'undefined' && window.alert) {
      // 降级到原生 alert
      window.alert(message);
    }
  }

  /**
   * 错误上报
   * @param {Object} errorInfo 错误信息
   * @param {String} context 上下文
   * @private
   */
  static _reportError(errorInfo, context) {
    // 这里可以集成错误监控服务，如 Sentry、Bugsnag 等
    if (process.env.NODE_ENV === 'production') {
      // 生产环境才上报
      try {
        // 示例：发送到错误监控服务
        // fetch('/api/error-report', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({ errorInfo, context })
        // });
      } catch (reportError) {
        console.warn('Failed to report error:', reportError);
      }
    }
  }

  /**
   * 获取错误日志
   * @param {Number} limit 限制数量
   * @returns {Array} 错误日志数组
   */
  static getErrorLog(limit = 10) {
    return this.errorLog.slice(-limit);
  }

  /**
   * 清除错误日志
   */
  static clearErrorLog() {
    this.errorLog = [];
  }

  /**
   * 创建错误对象
   * @param {String} type 错误类型
   * @param {String} message 错误消息
   * @param {Object} context 上下文信息
   * @returns {Object} 错误对象
   */
  static createError(type, message, context = {}) {
    return {
      type,
      message,
      level: ERROR_LEVELS.ERROR,
      context,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 包装异步函数，自动处理错误
   * @param {Function} fn 异步函数
   * @param {String} context 上下文
   * @param {Object} options 选项
   * @returns {Function} 包装后的函数
   */
  static wrapAsync(fn, context, options = {}) {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        this.handle(error, context, options);
        if (options.rethrow !== false) {
          throw error;
        }
      }
    };
  }

  /**
   * 包装同步函数，自动处理错误
   * @param {Function} fn 同步函数
   * @param {String} context 上下文
   * @param {Object} options 选项
   * @returns {Function} 包装后的函数
   */
  static wrapSync(fn, context, options = {}) {
    return (...args) => {
      try {
        return fn(...args);
      } catch (error) {
        this.handle(error, context, options);
        if (options.rethrow !== false) {
          throw error;
        }
      }
    };
  }
}

// 注册默认错误处理器
ErrorCenter.register(ERROR_TYPES.NETWORK_ERROR, (errorInfo) => {
  console.error('Network Error:', errorInfo);
});

ErrorCenter.register(ERROR_TYPES.MAP_LOAD_ERROR, (errorInfo) => {
  console.error('Map Load Error:', errorInfo);
});

ErrorCenter.register(ERROR_TYPES.LAYER_ERROR, (errorInfo) => {
  console.error('Layer Error:', errorInfo);
});

export default ErrorCenter;
