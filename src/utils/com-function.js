// 获取0-x的随机数
export function getRandomNumber(len = len + 1) {
  return Math.floor(Math.random() * (len - 0) + 0);
}

// 获取uuid
export const uuid = () => {
  let s = [];
  let hexDigtis = "0123456789abcdef";
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigtis.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4";
  s[19] = hexDigtis.substr((s[19] & 0x3) | 0x8, 1);
  s[8] = s[13] = s[18] = s[23] = "-";

  let uuid = s.join("");
  return uuid;
};

// 随机生成一串ID
export const randomGetid = () => {
  return Math.random().toString(36).substr(3, 10);
};

// 随机生成颜色
export const romdomGetColor = () => {
  let r = Math.floor(Math.random() * 256);
  let g = Math.floor(Math.random() * 256);
  let b = Math.floor(Math.random() * 256);
  let color = "#" + r.toString(16) + g.toString(16) + b.toString(16);
  return color;
};
