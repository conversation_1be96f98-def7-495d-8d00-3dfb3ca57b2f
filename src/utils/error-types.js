/**
 * 错误类型和处理策略定义
 */

import { ERROR_TYPES, ERROR_LEVELS } from './ErrorCenter.js';

/**
 * 地图相关错误处理策略
 */
export const MAP_ERROR_STRATEGIES = {
  [ERROR_TYPES.MAP_LOAD_ERROR]: {
    level: ERROR_LEVELS.CRITICAL,
    userMessage: '地图加载失败，请刷新页面重试',
    autoRetry: true,
    maxRetries: 3,
    retryDelay: 2000,
    fallback: () => {
      // 降级到静态地图或显示错误页面
      console.warn('Map load failed, showing fallback UI');
    }
  },
  
  [ERROR_TYPES.LAYER_ERROR]: {
    level: ERROR_LEVELS.ERROR,
    userMessage: '图层操作失败，请重试',
    autoRetry: false,
    fallback: (context) => {
      // 移除有问题的图层
      if (context.layer) {
        context.layer.removeLayer();
      }
    }
  },
  
  [ERROR_TYPES.OVERLAY_ERROR]: {
    level: ERROR_LEVELS.WARNING,
    userMessage: '覆盖物操作失败',
    autoRetry: false,
    fallback: (context) => {
      // 清理有问题的覆盖物
      if (context.overlay) {
        context.overlay.remove();
      }
    }
  }
};

/**
 * 数据相关错误处理策略
 */
export const DATA_ERROR_STRATEGIES = {
  [ERROR_TYPES.NETWORK_ERROR]: {
    level: ERROR_LEVELS.ERROR,
    userMessage: '网络连接失败，请检查网络设置',
    autoRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    fallback: () => {
      // 使用缓存数据或显示离线提示
      console.warn('Network error, using cached data');
    }
  },
  
  [ERROR_TYPES.DATA_PARSE_ERROR]: {
    level: ERROR_LEVELS.ERROR,
    userMessage: '数据格式错误，请检查数据源',
    autoRetry: false,
    fallback: (context) => {
      // 使用默认数据或跳过错误数据
      console.warn('Data parse error, using default data');
    }
  },
  
  [ERROR_TYPES.RESOURCE_NOT_FOUND]: {
    level: ERROR_LEVELS.WARNING,
    userMessage: '请求的资源不存在',
    autoRetry: false,
    fallback: () => {
      // 显示空状态或默认内容
      console.warn('Resource not found, showing empty state');
    }
  }
};

/**
 * 权限相关错误处理策略
 */
export const PERMISSION_ERROR_STRATEGIES = {
  [ERROR_TYPES.PERMISSION_DENIED]: {
    level: ERROR_LEVELS.WARNING,
    userMessage: '权限不足，无法执行此操作',
    autoRetry: false,
    fallback: () => {
      // 隐藏相关功能或跳转到登录页
      console.warn('Permission denied, hiding restricted features');
    }
  }
};

/**
 * 验证相关错误处理策略
 */
export const VALIDATION_ERROR_STRATEGIES = {
  [ERROR_TYPES.VALIDATION_ERROR]: {
    level: ERROR_LEVELS.WARNING,
    userMessage: '输入数据不符合要求，请检查后重试',
    autoRetry: false,
    fallback: (context) => {
      // 高亮错误字段或显示详细验证信息
      if (context.field) {
        console.warn(`Validation error in field: ${context.field}`);
      }
    }
  }
};

/**
 * 所有错误处理策略的合集
 */
export const ALL_ERROR_STRATEGIES = {
  ...MAP_ERROR_STRATEGIES,
  ...DATA_ERROR_STRATEGIES,
  ...PERMISSION_ERROR_STRATEGIES,
  ...VALIDATION_ERROR_STRATEGIES
};

/**
 * 错误恢复策略
 */
export const RECOVERY_STRATEGIES = {
  // 自动重试策略
  AUTO_RETRY: 'auto_retry',
  // 降级策略
  FALLBACK: 'fallback',
  // 用户手动重试
  MANUAL_RETRY: 'manual_retry',
  // 忽略错误
  IGNORE: 'ignore',
  // 中断操作
  ABORT: 'abort'
};

/**
 * 根据错误类型获取处理策略
 * @param {String} errorType 错误类型
 * @returns {Object} 处理策略
 */
export function getErrorStrategy(errorType) {
  return ALL_ERROR_STRATEGIES[errorType] || {
    level: ERROR_LEVELS.ERROR,
    userMessage: '操作失败，请稍后重试',
    autoRetry: false,
    fallback: () => {
      console.warn('Unknown error, using default fallback');
    }
  };
}

/**
 * 创建重试函数
 * @param {Function} fn 要重试的函数
 * @param {Object} strategy 重试策略
 * @returns {Function} 重试函数
 */
export function createRetryFunction(fn, strategy) {
  return async (...args) => {
    let lastError;
    const maxRetries = strategy.maxRetries || 1;
    const delay = strategy.retryDelay || 1000;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn(...args);
      } catch (error) {
        lastError = error;
        
        if (i < maxRetries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay));
          console.warn(`Retry attempt ${i + 1}/${maxRetries} for:`, error.message);
        }
      }
    }
    
    // 所有重试都失败，抛出最后一个错误
    throw lastError;
  };
}

/**
 * 错误边界组件的错误处理
 * @param {Error} error 错误对象
 * @param {Object} errorInfo 错误信息
 */
export function handleComponentError(error, errorInfo) {
  console.error('Component Error:', error, errorInfo);
  
  // 可以在这里添加错误上报逻辑
  if (process.env.NODE_ENV === 'production') {
    // 上报到错误监控服务
  }
}

/**
 * Promise 错误处理
 * @param {Promise} promise Promise 对象
 * @param {String} context 上下文
 * @returns {Promise} 处理后的 Promise
 */
export function handlePromiseError(promise, context = 'unknown') {
  return promise.catch(error => {
    const strategy = getErrorStrategy(error.type || ERROR_TYPES.UNKNOWN_ERROR);
    
    console.error(`Promise error in ${context}:`, error);
    
    // 执行降级策略
    if (strategy.fallback) {
      strategy.fallback({ error, context });
    }
    
    // 根据策略决定是否重新抛出错误
    if (strategy.level === ERROR_LEVELS.CRITICAL) {
      throw error;
    }
    
    return null; // 返回默认值
  });
}

export default {
  ERROR_TYPES,
  ERROR_LEVELS,
  ALL_ERROR_STRATEGIES,
  RECOVERY_STRATEGIES,
  getErrorStrategy,
  createRetryFunction,
  handleComponentError,
  handlePromiseError
};
