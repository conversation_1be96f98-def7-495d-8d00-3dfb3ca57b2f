//百度地图 http://api.map.baidu.com/lbsapi/creatmap/ 
// 已弃用
export const markerIconConfig = [
  {
    default: {
      w: 23,
      h: 25,
      l: 0,
      t: 21,
      x: 9,
      lb: 12,
    },
    move: {
      w: 21,
      h: 21,
      l: 0,
      t: 0,
      x: 6,
      lb: 5,
    },
  },
  {
    default: {
      w: 23,
      h: 25,
      l: 23,
      t: 21,
      x: 9,
      lb: 12,
    },
    move: {
      w: 21,
      h: 21,
      l: 23,
      t: 0,
      x: 6,
      lb: 5,
    },
  },
  {
    default: {
      w: 23,
      h: 25,
      l: 46,
      t: 21,
      x: 9,
      lb: 12,
    },
    move: {
      w: 21,
      h: 21,
      l: 46,
      t: 0,
      x: 6,
      lb: 5,
    },
  },
  {
    default: {
      w: 23,
      h: 25,
      l: 69,
      t: 21,
      x: 9,
      lb: 12,
    },
    move: {
      w: 21,
      h: 21,
      l: 69,
      t: 0,
      x: 6,
      lb: 5,
    },
  },
  {
    default: {
      w: 23,
      h: 25,
      l: 92,
      t: 21,
      x: 9,
      lb: 12,
    },
    move: {
      w: 21,
      h: 21,
      l: 92,
      t: 0,
      x: 6,
      lb: 5,
    },
  },
  {
    default: {
      w: 23,
      h: 25,
      l: 115,
      t: 21,
      x: 9,
      lb: 12,
    },
    move: {
      w: 21,
      h: 21,
      l: 115,
      t: 0,
      x: 6,
      lb: 5,
    },
  },
];

export const polygonConfig = [
  {
    strokeColor: "#096dd9",
    fillColor: "#40a9ff7d",
    fillOpacity: 0.6,
    strokeWeight: 2,
    strokeOpacity: 0.8,
  },
  {
    strokeColor: "#FFACBE",
    fillColor: "#ffacbe94",
    fillOpacity: 0.6,
    strokeWeight: 2,
    strokeOpacity: 0.8,
  },
  {
    strokeColor: "#32a83a",
    fillColor: "#32a83a8c",
    fillOpacity: 0.6,
    strokeWeight: 2,
    strokeOpacity: 0.8,
  },
  {
    strokeColor: "#cd73e9",
    fillColor: "#cd73e987",
    fillOpacity: 0.6,
    strokeWeight: 2,
    strokeOpacity: 0.8,
  },
];

export const polygonMapping = [
  {
    name: "name",
    displayName: "名称",
    mapColumnName: "m_name",
    columnType: "nameColumn",
    dataRef: {
      resourceId: "dffc0a45-afd4-4e2e-98bc-3fa5e52e0eb4",
      externalLink: null,
      defaultValue: null,
      typeClassify: "STRING",
      precision: null,
      sameName: null,
      searchFormat: null,
      choiceMode: null,
      sensitiveName: null,
      standardCodeId: null,
      tableName: "map_table",
      standardCodeName: null,
      valueType: null,
      options: null,
      logicDefault: null,
      from: null,
      order: 2,
      relationDataResource: [],
      camelName: "mName",
      filterable: null,
      visible: null,
      format: null,
      isPrimaryKey: "0",
      similarityStatus: null,
      secretLevel: "9",
      columnType: "nameColumn",
      unit: null,
      isSearchCondition: null,
      isForeignKey: "0",
      isNullable: "1",
      hasPermission: true,
      name: "m_name",
      defaultPermission: null,
      updatable: null,
      desensitizedRuleId: null,
      logicValue: null,
      encryptType: null,
      extension: null,
      internal: null,
      columnId: "e60f6ee2-d970-41f6-929a-ead8cc61890a",
      displayName: "名称",
      typeName: "VARCHAR",
      increment: null,
      dictResourceId: null,
      securityGradeId: null,
      securityGradeName: null,
      dataAnalysisType: null,
      persist: null,
      validate: null,
      length: "100",
      displayFormat: null,
      sortable: null,
      defaultAggFuncation: null,
      similarityId: null,
      dictRelationId: null,
      dictResourceName: null,
      sensitiveId: null,
      comment: "名称",
      showOrder: 2,
    },
    configure: true,
  },
  {
    name: "id",
    displayName: "id",
    mapColumnName: "m_id",
    columnType: "idColumn",
    dataRef: {
      resourceId: "dffc0a45-afd4-4e2e-98bc-3fa5e52e0eb4",
      externalLink: null,
      defaultValue: null,
      typeClassify: "STRING",
      precision: null,
      sameName: null,
      searchFormat: null,
      choiceMode: null,
      sensitiveName: null,
      standardCodeId: null,
      tableName: "map_table",
      standardCodeName: null,
      valueType: null,
      options: null,
      logicDefault: null,
      from: null,
      order: 1,
      relationDataResource: [],
      camelName: "mId",
      filterable: null,
      visible: null,
      format: null,
      isPrimaryKey: "1",
      similarityStatus: null,
      secretLevel: "9",
      columnType: "idColumn",
      unit: null,
      isSearchCondition: null,
      isForeignKey: "0",
      isNullable: "0",
      hasPermission: true,
      name: "m_id",
      defaultPermission: null,
      updatable: null,
      desensitizedRuleId: null,
      logicValue: null,
      encryptType: null,
      extension: null,
      internal: null,
      columnId: "01125205-91f4-4d28-86fd-bb9c0d51bea9",
      displayName: "id",
      typeName: "VARCHAR",
      increment: null,
      dictResourceId: null,
      securityGradeId: null,
      securityGradeName: null,
      dataAnalysisType: null,
      persist: null,
      validate: null,
      length: "38",
      displayFormat: null,
      sortable: null,
      defaultAggFuncation: null,
      similarityId: null,
      dictRelationId: null,
      dictResourceName: null,
      sensitiveId: null,
      comment: "id",
      showOrder: 1,
    },
    configure: true,
  },
  {
    name: "lng",
    displayName: "经度",
    columnType: "longitudeColumn",
    dataRef: {
      resourceId: "dffc0a45-afd4-4e2e-98bc-3fa5e52e0eb4",
      externalLink: null,
      defaultValue: null,
      typeClassify: "STRING",
      precision: null,
      sameName: null,
      searchFormat: null,
      choiceMode: null,
      sensitiveName: null,
      standardCodeId: null,
      tableName: "map_table",
      standardCodeName: null,
      valueType: null,
      options: null,
      logicDefault: null,
      from: null,
      order: 3,
      relationDataResource: [],
      camelName: "mLng",
      filterable: null,
      visible: null,
      format: null,
      isPrimaryKey: "0",
      similarityStatus: null,
      secretLevel: "9",
      columnType: "longitudeColumn",
      unit: null,
      isSearchCondition: null,
      isForeignKey: "0",
      isNullable: "1",
      hasPermission: true,
      name: "m_lng",
      defaultPermission: null,
      updatable: null,
      desensitizedRuleId: null,
      logicValue: null,
      encryptType: null,
      extension: null,
      internal: null,
      columnId: "4ef5568d-f980-4f9b-832f-0f84ed837488",
      displayName: "经度",
      typeName: "VARCHAR",
      increment: null,
      dictResourceId: null,
      securityGradeId: null,
      securityGradeName: null,
      dataAnalysisType: null,
      persist: null,
      validate: null,
      length: "100",
      displayFormat: null,
      sortable: null,
      defaultAggFuncation: null,
      similarityId: null,
      dictRelationId: null,
      dictResourceName: null,
      sensitiveId: null,
      comment: "经度",
      showOrder: 3,
    },
    configure: true,
  },
  {
    name: "lat",
    displayName: "纬度",
    columnType: "latitudeColumn",
    dataRef: {
      resourceId: "dffc0a45-afd4-4e2e-98bc-3fa5e52e0eb4",
      externalLink: null,
      defaultValue: null,
      typeClassify: "STRING",
      precision: null,
      sameName: null,
      searchFormat: null,
      choiceMode: null,
      sensitiveName: null,
      standardCodeId: null,
      tableName: "map_table",
      standardCodeName: null,
      valueType: null,
      options: null,
      logicDefault: null,
      from: null,
      order: 4,
      relationDataResource: [],
      camelName: "mLat",
      filterable: null,
      visible: null,
      format: null,
      isPrimaryKey: "0",
      similarityStatus: null,
      secretLevel: "9",
      columnType: "latitudeColumn",
      unit: null,
      isSearchCondition: null,
      isForeignKey: "0",
      isNullable: "1",
      hasPermission: true,
      name: "m_lat",
      defaultPermission: null,
      updatable: null,
      desensitizedRuleId: null,
      logicValue: null,
      encryptType: null,
      extension: null,
      internal: null,
      columnId: "040105d5-c55f-4426-8dc1-ced85fc5972d",
      displayName: "纬度",
      typeName: "VARCHAR",
      increment: null,
      dictResourceId: null,
      securityGradeId: null,
      securityGradeName: null,
      dataAnalysisType: null,
      persist: null,
      validate: null,
      length: "100",
      displayFormat: null,
      sortable: null,
      defaultAggFuncation: null,
      similarityId: null,
      dictRelationId: null,
      dictResourceName: null,
      sensitiveId: null,
      comment: "纬度",
      showOrder: 4,
    },
    configure: true,
  },
  {
    name: "pointList",
    displayName: "点集",
    configure: true,
    mapColumnName: "m_point_list",
  },
  {
    name: "centerPoint",
    displayName: "中心点",
    configure: true,
    mapColumnName: "m_center_point",
  },
];
