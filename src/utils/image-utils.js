import { request } from "@daas/core";

/**
 * 获取图片base64
 *
 * @param {File} img
 * @returns
 */
export function getBase64(img) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    function success() {
      resolve(reader.result);
      reader.removeEventListener("load", success);
    }
    reader.addEventListener("load", success);
    reader.readAsDataURL(img?.originFileObj || img);
  });
}

/**
 * 获取图片base64
 *
 * @param {string} url
 * @returns
 */
export function getBase64ByUrl(url) {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = url;
    img.onload = () => {
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0, img.width, img.height);
      const base64 = canvas.toDataURL("image/png");
      resolve(base64);
    };
  });
}

/**
 * 获取图片尺寸
 *
 * @param {string} imgUrl
 * @returns
 */
export function getImageSize(imgUrl) {
  return new Promise((resolve) => {
    const img = new Image();
    img.src = imgUrl;
    img.onload = () => resolve({ width: img.width, height: img.height });
  });
}

/**
 * 将base64转换为file
 *
 * @param {string} dataUrl
 * @returns
 */
function dataURLtoFile(dataUrl) {
  var arr = dataUrl.split(",");
  var mine = arr[0].match(/:(.*?);/)[1];
  var bstr = window.atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  let fileName = "file_" + new Date().getTime() + ".png";
  return new File([u8arr], fileName, { type: mine });
}

/**
 * 上传图片
 *
 * @param {string} base64
 * @returns
 */
export async function uploadFile(base64) {
  let formData = new FormData();
  let file = dataURLtoFile(base64);
  formData.append("file", file);
  formData.append("businessType", "screenshot");
  let res = await request.post("/daasDMS/commonUpload/uploadFile", formData);
  return res;
}
