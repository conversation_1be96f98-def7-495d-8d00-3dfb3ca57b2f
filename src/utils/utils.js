import { uuid } from "@/utils/com-function";

/**
 *
 * @param {*} columns
 * @returns  转换后的图层数据
 */
export function layerColumnsToData(columns, dataSource = []) {
  const _matchCol = getColumnsMatch(columns);
  let layerData = [];
  dataSource.map((data) => {
    let d = {};
    for (let key in _matchCol) {
      if (_matchCol[key]) d[key] = data[_matchCol[key]];
      else if (key == "id") {
        d[key] = uuid();
      }
    }
    d.extData = data;
    layerData.push(d);
  });
  return layerData;
}

export function getColumnsMatch(columns = []) {
  let obj = {};
  columns.map((r) => {
    if ((r.name || "") != "") {
      obj[r.name] = r.mapColumnName;
    }
  });
  return obj;
}

export function getPolygonCenter(pathList) {
  var x = 0,
    y = 0;
  for (var k = 0; k < pathList.length; k++) {
    x = x + parseFloat(pathList[k].lng);
    y = y + parseFloat(pathList[k].lat);
  }
  x = x / pathList.length;
  y = y / pathList.length;
  return JSON.stringify([x, y]);
}

export function toColumnObj(columns) {
  let obj = {};
  columns.map((r) => {
    if (r.name) obj[r.name] = { ...r };
  });
  return obj;
}

export function loadJS(url) {
  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = url;
    script.onload = resolve;
    script.onerror = reject;
    document.body.appendChild(script);
  });
}
