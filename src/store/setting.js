import { Modal } from "ant-design-vue";

export default {
  namespaced: true,
  state: {
    isOpenDrawMarker: false,
    isOpenDrawPolygon: false,
    isDisabledDrawMarker: true,
    isDisabledPolygon: true,
    isShowResourceSelect: false,
    isRemoveLayerStatus: false,
    isShowConifgModal: false,
    isShowLayerMapping: false,
    miniToolBar: false,
    updateModalOpen: false, //数据编辑框
    updateModalData: {}, //更新框数据
    // 当前视图模式
    isStatic: true,
    // 当前是否编辑中,这个编辑指的是是否新增点、面或移动位置
    isEditing: false,
    // 编辑对象 , 即将删除
    editObject: null,
    // 当前编辑的所有标记
    tagsInTheEdit: new Map([]),
    // 选择图层类型的弹窗
    isShowSelectLayerTypeModal: false,
    // 页面模式
    pageMode: "view",
    // 是否显示静态数据编辑的弹窗
    visibleEditStaticModal: false,
    // 展示类型
    showType: "app",
    // 错误信息
    loadFailInfo: {
      failUrl: "",
      failMsg: "",
      failCode: "",
    },
    // 修改地图中心点模式
    isChangeCenter: false,
  },
  getters: {
    isShowConifgModal: ({ isShowConifgModal }) => isShowConifgModal,
    isOpenDrawMarker: ({ isOpenDrawMarker }) => isOpenDrawMarker,
    isOpenDrawPolygon: ({ isOpenDrawPolygon }) => isOpenDrawPolygon,
    isShowResourceSelect: ({ isShowResourceSelect }) => isShowResourceSelect,
    isRemoveLayerStatus: ({ isRemoveLayerStatus }) => isRemoveLayerStatus,
    miniToolBar: ({ miniToolBar }) => miniToolBar,
    updateModalOpen: ({ updateModalOpen }) => updateModalOpen,
    updateModalData: ({ updateModalData }) => updateModalData,
    isShowLayerMapping: ({ isShowLayerMapping }) => isShowLayerMapping,
    isStatic: ({ isStatic }) => isStatic,
    isEditing: ({ isEditing }) => isEditing,
    editObject: ({ editObject }) => editObject,
    tagsInTheEdit: ({ tagsInTheEdit }) => tagsInTheEdit,
    isShowSelectLayerTypeModal: ({ isShowSelectLayerTypeModal }) => isShowSelectLayerTypeModal,
    // 当前页面是否可以编辑
    canEditPage: ({ pageMode }) => pageMode === "edit",
    pageMode: ({ pageMode }) => pageMode,
    // 是否显示静态数据编辑的弹窗
    visibleEditStaticModal: ({ visibleEditStaticModal }) => visibleEditStaticModal,
    showType: ({ showType }) => showType,
    isDomain: ({ showType }) => showType === "domain",
    loadFailInfo: ({ loadFailInfo }) => loadFailInfo,
    loadFailMsg: ({ loadFailInfo }) => loadFailInfo.failMsg,
    isChangeCenter: ({ isChangeCenter }) => isChangeCenter,
  },
  mutations: {
    setShowLayerMapping(state, val) {
      state.isShowLayerMapping = val;
    },
    setShowConifgModal(state, val) {
      state.isShowConifgModal = val;
    },
    setOpenDrawMarker(state, val) {
      state.isOpenDrawMarker = val;
    },
    setOpenDrawPolygon(state, val) {
      state.isOpenDrawPolygon = val;
    },
    setShowResourceSelect(state, val) {
      state.isShowResourceSelect = val;
    },
    setRemoveLayerStatus(state, val) {
      state.isRemoveLayerStatus = val;
    },
    setMiniToolBar(state, val) {
      state.miniToolBar = val;
    },
    setUpdateModalOpen(state, val) {
      // if (state.pageMode !== "edit") return;

      if (state.isStatic) state.visibleEditStaticModal = val;
      else state.updateModalOpen = val;
    },
    setUpdateModalData(state, val) {
      state.updateModalData = val;
    },
    // 设置当前展示类型
    setShowType(state, type) {
      state.showType = type;
    },
    // 左上打标 是否可选， 是否打开
    modifyDrawActionStatus(state, { drawType, type, value }) {
      if (drawType == "marker" || drawType == "point") {
        if (type == "isOpen") {
          state.isOpenDrawMarker = value;
        }
      } else if (drawType == "polygon") {
        if (type == "isOpen") {
          state.isOpenDrawPolygon = value;
        }
      }
    },
    // 设置编辑模式
    SET_EDIT_MOD(state, flag) {
      state.isEditing = flag;
    },
    // 缓存当前编辑的标记
    SET_EDIT_OBJECT(state, object) {
      state.editObject = object;
    },
    // 缓存多个标记
    SET_EDITTING_TAGS(state, tags) {
      state.tagsInTheEdit = tags;
    },
    // 设置当前是为静态数据
    SET_IS_STATIC(state, status) {
      state.isStatic = status;
    },
    SET_SHOWSELECTOR_LAYER_TYPEMODAL(state, flag) {
      state.isShowSelectLayerTypeModal = flag;
    },
    // 设置页面模式
    SET_PAGE_MODE(state, mode) {
      state.pageMode = mode;
    },
    setLoadFailInfo(state, val) {
      state.loadFailInfo = val;
    },
    setIsChangeCenter(state, val) {
      state.isChangeCenter = val;
    },
  },
  actions: {
    initSetting({ state }) {
      state.isOpenDrawMarker = false;
      state.isOpenDrawPolygon = false;
      state.isDisabledDrawMarker = true;
      state.isDisabledPolygon = true;
    },
    // 关闭配置弹窗的二次确认
    closeConfigModalConfirm({ commit, rootState }, cb) {
      Modal.confirm({
        title: "系统提示",
        content: `当前还有未保存的配置，弹窗关闭后将恢复到保存前的配置，是否关闭?`,
        onOk: async () => {
          const { currentLayer } = rootState;
          const { type } = currentLayer;

          if (type === "marker") currentLayer.resetMarkerConfig();
          if (type === "polygon") currentLayer.resetPolygonConfig();

          commit("setShowConifgModal", false);

          cb && cb();
        },
      });
    },
    // 控制编辑模式
    controlEdit({ commit, rootState, dispatch }, { flag, object }) {
      const { layers } = rootState;
      if (flag && object) {
        const { id } = object;
        layers.forEach((i) => {
          i.initEditMod(id);
        });
      }
      if (!flag) {
        layers.forEach((i) => {
          i.exitEditMod();
        });
      }
      commit("SET_EDIT_MOD", flag);
      // 处理缓存
      flag ? dispatch("addTagToEdit", object) : dispatch("clearEdittingTags");
    },
    // 添加标记中的标记点到缓存
    addTagToEdit({ commit, state }, object) {
      const { id } = object;
      if (!id) return;

      const _tagsInTheEdit = state.tagsInTheEdit;
      _tagsInTheEdit.set(id, object);
      commit("SET_EDITTING_TAGS", _tagsInTheEdit);
    },
    // 清空缓存的所有标记点
    clearEdittingTags({ commit }) {
      commit("SET_EDITTING_TAGS", new Map([]));
    },
    // 控制页面的模式切换
    changePageMode({ commit, rootState, dispatch }, { mode, Map }) {
      let flag = mode === "edit";
      let fnKey = flag ? "createAllCoverRightEvent" : "removeAllCoverRightEvent";
      if (!flag) {
        commit("setOpenDrawMarker", false);
        commit("setOpenDrawPolygon", false);
        Map.drawingTool._drawingManager._close();
        dispatch("controlEdit", { flag });
      }

      const layers = rootState.layers;
      layers.forEach((i) => i[fnKey]());

      commit("SET_PAGE_MODE", mode);
    },
    setLoadFailInfo({ commit }, val) {
      commit("setLoadFailInfo", val);
    },
    clearLoadFailInfo({ commit }) {
      commit("setLoadFailInfo", {
        failUrl: "",
        failMsg: "",
        failCode: "",
      });
    },
  },
};
