import Vue from "vue";
import Vuex from "vuex";
import setting from "./setting";

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    setting,
  },
  state: {
    modularType: "",
    modularId: "",
    layers: [], // 图层
    currentLayer: undefined,
  },
  getters: {
    modularType: ({ modularType }) => modularType,
    modularId: ({ modularId }) => modularId,
    layers: ({ layers }) => layers,
    currentLayer: ({ currentLayer }) => currentLayer,
  },

  mutations: {
    setModular(state, { modularType, modularId }) {
      state.modularType = modularType;
      state.modularId = modularId;
    },

    setLayer(state, val) {
      state.layers = val;
    },

    addLayer(state, val) {
      state.layers.push(val);
    },

    setCurrentLayer(state, val) {
      state.currentLayer = val;
    },
  },
  actions: {
    resetData({ commit, state }) {
      state.currentLayer && state.currentLayer.Map.clearOverlays();
      commit("setCurrentLayer", undefined);
      commit("setLayer", []);
    },
  },
});

export default store;
