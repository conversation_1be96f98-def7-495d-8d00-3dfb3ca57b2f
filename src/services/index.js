import { request } from "@daas/core";

/**
 * 获取资源详情
 * @param {*} params {resourceId: string}
 * @returns
 */
export async function GetResourceInfo(resourceId, params) {
  return request.get(`/daasMeta/dataResource/${resourceId}`, params);
}

/**
 * 获取资源详情
 * @param {*} params {resourceId: string}
 * @returns
 */
export async function GetDataResourceRelationOverall(params) {
  return request.post("/api/daasMeta/GetDataResourceRelationOverall", params);
}

/**
 * 获取资源数据
 * @param {*} params {dataResourceId : string }
 */
export async function GetResourceData(params) {
  try {
    const res = await request.post("/api/daasDMS/GetResourceData", params);
    if (res.resultCode === 200) {
      const { rows, pageParam } = res;
      return {
        data: rows || [],
        pageParam,
      };
    } else {
      return Promise.reject(new Error(res));
    }
  } catch (error) {
    return Promise.reject(error);
  }
}

/**
 * 新增资源数据
 * @param {*} params: { dataResourceId: string, values: resourceItem[] }
 * @param {*} reqCfg
 * @returns
 */
export async function InsertResourceData(params, reqCfg) {
  try {
    const res = await request({
      url: "/api/daasDMS/InsertResourceData",
      method: "POST",
      data: params,
      ...reqCfg,
    });
    if (res.resultCode === 200) {
      return res;
    } else {
      return Promise.reject(new Error(res));
    }
  } catch (error) {
    return Promise.reject(error);
  }
}

/**
 * 修改资源数据
 * @param {*} params: { dataResourceId: string, values: resourceItem[] }
 * @param {*} reqCfg
 * @returns
 */
export async function UpdateResourceData(params, reqCfg) {
  try {
    const res = await request({
      url: "/api/daasDMS/UpdateResourceData",
      method: "POST",
      data: params,
      ...reqCfg,
    });
    if (res.resultCode === 200) {
      return res;
    } else {
      return Promise.reject(new Error(res));
    }
  } catch (error) {
    return Promise.reject(error);
  }
}

/**
 * 删除资源数据
 * @param {*} params { dataResourceId: string, values: resourceItem[] }
 * @returns
 */
export async function DeleteResourceData(params) {
  return request.post("/api/daasDMS/DeleteResourceData", params);
}
