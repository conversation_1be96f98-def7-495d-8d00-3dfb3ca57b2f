const routes = [
  // {
  //   path: "/",
  //   redirect: "/app",
  // },
  {
    path: "/:dataViewType?", // view edit usage
    name: "map.home",
    meta: { title: "地图" },
    component: { render: (h) => h("router-view") },
    children: [
      {
        path: "",
        name: "map.setting",
        component: () => import("@pages/index.vue"),
        meta: { title: "地图" },
      },
      {
        path: "view",
        name: "map.view",
        component: () => import("@pages/index.vue"),
        meta: { title: "地图" },
      },
      {
        path: "test",
        name: "test",
        component: () => import("@pages/test/index.vue"),
      },
    ],
  },
];

export default routes;
