/** options
 * @param {width}	Number	信息窗宽度，单位像素。取值范围：0, 220 - 730。如果您指定宽度为0，则信息窗口的宽度将按照其内容自动调整
 * @param {height}	Number	信息窗高度，单位像素。取值范围：0, 60 - 650。如果您指定高度为0，则信息窗口的高度将按照其内容自动调整
 * @param {maxWidth}	Number	信息窗最大化时的宽度，单位像素。取值范围：220 - 730
 * @param {offset}	Size	信息窗位置偏移值。默认情况下在地图上打开的信息窗底端的尖角将指向其地理坐标，在标注上打开的信息窗底端尖角的位置取决于标注所用图标的infoWindowOffset属性值，您可以为信息窗添加偏移量来改变默认位置
 * @param {title}	String	信息窗标题文字，支持HTML内容
 * @param {enableAutoPan}	Boolean	是否开启信息窗口打开时地图自动移动（默认开启）
 * @param {enableCloseOnClick}	Boolean	是否开启点击地图关闭信息窗口（默认开启）
 * @param {enableMessage}	Boolean	是否在信息窗里显示短信发送按钮（默认开启）
 * @param {message}	String	自定义部分的短信内容，可选项。完整的短信内容包括：自定义部分+位置链接，不设置时，显示默认短信内容。短信内容最长为140个字
 */

export const _DEFAULT_CONFIG = {
  confirmText: "确认",
  canceText: "取消",
  title: "系统提示",
  content: "是否确认执行该操作",
};

export default class InfoWindow {
  constructor(
    dom,
    options = {
      enableCloseOnClick: false,
      offset: new BMap.Size(135, 40),
      width: 1,
      height: 1,
    },
  ) {
    const _infoWindow = this._init(dom, options);
    this._infoWindow = _infoWindow;
  }

  #_DEFAULT_CONFIG = _DEFAULT_CONFIG;

  _init(dom, options = {}) {
    let _dom = dom || this._createDom(options);
    this._dom = _dom;
    return new BMap.InfoWindow(_dom, options);
  }

  // 创建的 dom 的样式来源于 antd 组件，部分在 reset-map.less 中
  _createDom() {
    const confirmText = this.#_DEFAULT_CONFIG.confirmText;
    const canceText = this.#_DEFAULT_CONFIG.canceText;

    const title = this.#_DEFAULT_CONFIG.title;
    const content = this.#_DEFAULT_CONFIG.content;

    const _wrap = document.createElement("div");
    _wrap.className = `ant-notification ant-notification-topRight`;

    const _domText = `<img src="/static/icons/arr.png" class="noti-arr" />
                      <span>
                        <div class="ant-notification-notice ant-notification-notice-closable">
                          <div class="ant-notification-notice-content">
                            <div class="">
                              <div class="ant-notification-notice-message title">${title}</div>
                              <div class="ant-notification-notice-description content">${content}</div>
                              <span class="ant-notification-notice-btn">
                                <button type="button" class="ant-btn ant-btn-primary ant-btn-sm btn-confirm">
                                  <span>${confirmText}</span>
                                </button>
                                <button type="button" class="ant-btn ant-btn-primary ant-btn-sm btn-cancel">
                                  <span>${canceText}</span>
                                </button>
                              </span>
                            </div>
                          </div>
                          <a tabindex="0" class="ant-notification-notice-close">
                            <span class="ant-notification-close-x">
                              <i aria-label="icon: close" class="anticon anticon-close ant-notification-close-icon">
                                <svg viewBox="64 64 896 896" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true"
                                  focusable="false" class="">
                                  <path
                                    d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z">
                                  </path>
                                </svg>
                              </i>
                            </span>
                          </a>
                        </div>
                      </span>`;
    _wrap.innerHTML = _domText;

    const _close = _wrap.querySelector(".ant-notification-notice-close");
    const _confirm = _wrap.querySelector(".btn-confirm");
    const _cancel = _wrap.querySelector(".btn-cancel");
    const _title = _wrap.querySelector(".title");
    const _content = _wrap.querySelector(".content");

    this._eventDoms = {
      _close,
      _confirm,
      _cancel,
    };
    this._textDoms = {
      _title,
      _content,
    };

    return _wrap;
  }

  /**
   * 给 dom 注册事件
   * @param {DOm} ele 需要绑定事件的 dom 对象
   * @param {String} eventType 事件类型
   * @param {Function} fn 事件
   * @param {Boolean} isRepeat 是否允许重复 默认 true
   * @param {Boolean} isCaptureCatch
   * @returns nundefined
   */
  _loginEvent(ele, eventType, fn, isRepeat, isCaptureCatch) {
    if (ele == undefined || eventType === undefined || fn === undefined) {
      throw new Error("传入的参数错误！");
    }

    if (typeof ele !== "object") {
      throw new TypeError("不是对象！");
    }

    if (typeof eventType !== "string") {
      throw new TypeError("事件类型错误！");
    }

    if (typeof fn !== "function") {
      throw new TypeError("fn 不是函数！");
    }

    if (isCaptureCatch === undefined || typeof isCaptureCatch !== "boolean") {
      isCaptureCatch = false;
    }

    if (isRepeat === undefined || typeof isRepeat !== "boolean") {
      isRepeat = true;
    }

    if (ele.eventList === undefined) {
      ele.eventList = {};
    }

    if (isRepeat === false) {
      for (let key in ele.eventList) {
        if (key === eventType) {
          // 移除重复绑定的事件
          ele.removeEventListener(eventType, ele.fn);
        }
      }
    }

    // 添加事件监听
    if (ele.addEventListener) {
      ele.addEventListener(eventType, fn, isCaptureCatch);
    } else if (ele.attachEvent) {
      ele.attachEvent("on" + eventType, fn);
    } else {
      return false;
    }

    ele.eventList[eventType] = true;
    ele.fn = fn;
  }

  /**
   * 添加事件
   * @param {String} type 枚举 confirm / close
   * @param {Function} fun
   * @returns
   */
  addEventListener(type, fun) {
    if (!type || !fun) return;
    switch (type) {
      case "confirm":
        const { _confirm } = this._eventDoms;
        this._loginEvent(_confirm, "click", fun, false);
        break;
      case "cancel":
        const { _cancel, _close } = this._eventDoms;
        this._loginEvent(_cancel, "click", fun, false);
        this._loginEvent(_close, "click", fun, false);
        break;
      default:
        break;
    }
  }

  /**
   * 移除事件
   * @param {String} type 需要移除的事件类型
   * @returns undefined
   */
  removeEventListener(type) {
    if (!type) return;
    switch (type) {
      case "confirm":
        const { _confirm } = this._eventDoms;
        _confirm?.fn && _confirm.removeEventListener("click", _confirm.fn);
        break;
      case "cancel":
        const { _cancel, _close } = this._eventDoms;
        _cancel?.fn && _cancel.removeEventListener("click", _cancel.fn);
        _close?.fn && _close.removeEventListener("click", _close.fn);
        break;
      default:
        break;
    }
  }

  updateTitle(text) {
    const { _title } = this._textDoms;
    _title.innerHTML = text;
  }

  updateContent(text) {
    const { _content } = this._textDoms;
    _content.innerHTML = text;
  }
}
