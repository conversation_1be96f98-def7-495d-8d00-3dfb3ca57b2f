import { uuid } from "@/utils/com-function";
import { getPolygonCenter } from "@/utils/utils";
import store from "@/store";
import { setSelectedCell } from "@/utils/map-utils";
import { merge } from "lodash";

export const _DEFAULT_POLYGON_CONFIG = {
  strokeColor: "#096dd9",
  fillColor: "#40a9ff",
  fillOpacity: 0.6,
  strokeWeight: 2,
  strokeOpacity: 0.8,
  strokeStyle: "solid",
};

export default class Polygon {
  #MENU_CONFIG = {
    show: {
      title: "查看数据",
      id: "menu-detail",
      callBack: () => {
        this._rightClickSetCurrentLayer(this.layer);
        this.polygonEdit(this.layer, true);
      },
    },
    detail: {
      title: "查看实体详情",
      id: "menu-detail",
      callBack: () => {
        this.layer.showEntityDetail(this.id);
      },
    },
    move: {
      title: "编辑图形",
      id: "menu-move",
      callBack: () => {
        store.dispatch("setting/controlEdit", { object: this, flag: true });
      },
    },
    style: {
      title: "设置样式",
      id: "menu-style",
      callBack: () => {
        // 设置选中
        setSelectedCell(this);
        store.commit("setting/setShowConifgModal", true);
        store.commit("setCurrentLayer", this.layer);
      },
    },
    edit: {
      title: "编辑数据",
      id: "menu-edit",
      callBack: () => {
        this._rightClickSetCurrentLayer(this.layer);
        this.polygonEdit(this.layer, true);
      },
    },
    delete: {
      title: "删除图形",
      id: "menu-del",
      callBack: () => {
        this._rightClickSetCurrentLayer(this.layer);
        this.deletePolygon(this.id, this.layer);
      },
    },
  };

  #_DEFAULT_STYLE_FUNCTION = {
    strokeColor: "setStrokeColor",
    fillColor: "setFillColor",
    strokeOpacity: "setStrokeOpacity",
    fillOpacity: "setFillOpacity",
    strokeWeight: "setStrokeWeight",
    strokeStyle: "setStrokeStyle",
  };
  #_DEFAULT_POLYGON_CONFIG = _DEFAULT_POLYGON_CONFIG;

  constructor(layer, param) {
    this.id = param.id || uuid();
    this.name = param.name === "" || param.name ? param.name : "未命名";

    this.pointList = param.pointList ? param.pointList.map((p) => new BMap.Point(p[0], p[1])) : [];
    this.centerPoint = param.centerPoint;
    this.getCenterPoint(this.centerPoint, this.pointList); //获取中心点位
    this.options = param.polygonConfig || layer.polygonConfig || this.#_DEFAULT_POLYGON_CONFIG;
    this.polygon = new BMap.Polygon(this.pointList, this.options);

    this.extData = param.extData || {};
    this.data = param.data || {};

    this._edit = false;
    this.label = param.label;
    this.addPolygonLabel(layer);

    this.createContextMenu(layer);

    this.mapFieldNames = layer.getMapFieldNames();

    this.addLeftClickEvent(this.polygon);

    this.layer = layer;
  }

  get type() {
    return "polygon";
  }

  show() {
    this.polygon.show();
    this.label.show();
  }

  hide() {
    this.polygon.hide();
    this.label.hide();
  }

  addPolygonLabel(layer) {
    const { name } = this;
    const pointList = this.polygon.getPath();
    let centerPoint = this.centerPoint || getPolygonCenter(pointList);
    centerPoint = typeof centerPoint == "string" ? JSON.parse(centerPoint) : centerPoint;
    let point;
    if (this.centerPoint) {
      point = new BMap.Point(centerPoint["lng"], centerPoint["lat"]);
    } else {
      point = new BMap.Point(centerPoint[0], centerPoint[1]);
    }
    this.label = new BMap.Label(name, { offset: new BMap.Size(-20, -20), position: point });
    layer.Map.addOverlay(this.label);
    this.label.setStyle({
      color: "rgb(51 51 51)",
      border: "1px solid #e8e8e8",
      padding: "2px 6px",
      opacity: name === "" ? 0 : 1,
    });
  }

  // 创建右键事件
  createContextMenu(layer) {
    if (this._contextMenu) return;
    const pageMode = store.getters["setting/pageMode"];

    const contextMenuData =
      pageMode === "view"
        ? [this.#MENU_CONFIG.show]
        : [
            this.#MENU_CONFIG.move,
            this.#MENU_CONFIG.edit,
            this.#MENU_CONFIG.style,
            this.#MENU_CONFIG.delete,
          ];

    if (!layer.isStatic && pageMode !== "view") {
      contextMenuData.unshift(this.#MENU_CONFIG.detail);
    }

    let contextMenu = new BMap.ContextMenu();
    contextMenuData.map((menu) => {
      contextMenu.addItem(new BMap.MenuItem(menu.title, menu.callBack, { id: menu.id }));
    });

    this._contextMenu = contextMenu;
    this.polygon.addContextMenu(contextMenu);
  }

  // 创建左键事件
  addLeftClickEvent(polygon) {
    polygon.addEventListener("click", (e) => {
      // 阻止冒泡
      e.domEvent.stopPropagation();
      if (this.layer.isEditMode) {
        // 切换当前编辑的面
        store.dispatch("setting/controlEdit", { object: this, flag: true });
      }
    });
  }

  // 开启编辑图形
  openEditPolygon(layer) {
    this._rightClickSetCurrentLayer(layer);
    this.polygonDrag(layer);
  }

  removeRightClickEvent() {
    this._contextMenu && this.polygon.removeContextMenu(this._contextMenu);
    this._contextMenu = null;
  }

  _rightClickSetCurrentLayer(layer) {
    if (!layer) return;
    const { currentLayer } = store.getters;
    if (currentLayer.id !== layer.id) {
      store.commit("setCurrentLayer", layer);
    }
  }

  polygonEdit(layer, isOpen) {
    let obj = {
      id: this.id,
      extData: this.extData,
    };
    layer.openEdit(isOpen, obj);
  }

  async _editConfirm(layer) {
    this.polygon.disableEditing();
    this.polygonEdit(layer, false);
    let pointList = [];
    let a;
    let i;
    let data = {};
    //将数据转化成接口接收的数据
    for (a = 0; a < this.polygon.vo.length; a++) {
      let point = [this.polygon.vo[a].lng, this.polygon.vo[a].lat];
      pointList.push(point);
    }
    let pointStr = JSON.stringify(pointList);
    for (i = 0; i < this.layer.layerConfig.mappingConfig.length; i++) {
      let mappingConfig = this.layer.layerConfig.mappingConfig[i];
      if (mappingConfig.name == "pointList") {
        data[mappingConfig.mapColumnName] = pointStr;
      }
      if (mappingConfig.name == "name") {
        data[mappingConfig.mapColumnName] = this.name;
      }
      if (mappingConfig.name == "id") {
        data[mappingConfig.mapColumnName] = this.id;
      }
    }
    // await this.layer.resourceStore.dispatch("data/updateData", { values: [data] });
    this.layer.isStatic
      ? this.layer.staticStore.update(data)
      : await this.layer.resourceStore.dispatch("data/updateData", { values: [data] });

    this._editComplete(layer);
  }

  _editCancel(layer) {
    this.polygon.setPath(this.pointList);
    this.polygon.disableEditing();
    this._editComplete(layer);
  }

  _editComplete(layer) {
    layer._map.closeInfoWindow();
  }

  polygonDrag() {
    this.polygon.enableEditing();
    // store.dispatch("setting/controlEdit", { flag: true, object: this });
    this._edit = true;
  }

  // 删除图型
  async deletePolygon(id, layer) {
    layer.isStatic
      ? layer.staticStore.del(id)
      : await layer.resourceStore.dispatch("data/delData", { primaryIds: [id] });

    this.delete(id, layer);
  }

  //图形中心坐标数据转化
  getCenterPoint(centerPoint, pointList) {
    if (!centerPoint) {
      let centerPoint = pointList ? getPolygonCenter(pointList) : undefined;
      centerPoint = typeof centerPoint == "string" ? JSON.parse(centerPoint) : centerPoint;
      this.centerPoint = new BMap.Point(centerPoint[0], centerPoint[1]);
    }
  }

  // 更新样式
  updateStyle(config) {
    let polygon = this.polygon;

    for (let i in config) {
      const funKey = this.#_DEFAULT_STYLE_FUNCTION[i];
      if (funKey && config[i]) {
        polygon[funKey](config[i]);
      }
    }

    this.options = merge(this.options, config);

    this.polygon = polygon;
    return polygon;
  }

  // 更新标签
  updateLabel(config) {
    const { opacity } = config;
    if (opacity || opacity === 0) {
      const flag = this.name;

      this.label.setStyle({
        opacity: flag ? opacity : 0,
      });
    }
  }

  // 更新数据
  updateData(data) {
    this.extData = Object.assign(this.extData, data.rawData || data);
    this.data = data;

    const label = data.name;
    this.name = label;

    this.updateLabel({
      opacity: label ? 1 : 0,
    });

    this.label.setContent(label); //修改名称
  }

  // 删除当前
  delete(id, layer) {
    layer.Map.removeOverlay(this.polygon);
    layer.Map.removeOverlay(this.label);

    layer.delOverlay("polygon", id);

    layer.stack.delete(this.id);
  }

  // 导出配置
  exportConfig() {
    return {
      id: this.id,
      name: this.name,
      options: JSON.parse(JSON.stringify(this.options)),
    };
  }
}
