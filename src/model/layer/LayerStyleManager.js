import { cloneDeep } from "lodash-es";
import { _DEFAULT_MARKER_CONFIG } from "../marker";
import { _DEFAULT_POLYGON_CONFIG } from "../polygon";
import { _DEFAULT_POLYLINE_CONFIG } from "../Polyline";

// 缓存
let _CACHE = {};

/**
 * 图层样式管理器
 * 负责处理图层的样式配置和更新
 */
export default class LayerStyleManager {
  constructor(layer) {
    this.layer = layer;
    this._beforeEditMarkerConfig = null;
    this._beforeEditPolygonConfig = null;
  }

  /**
   * 获取 marker 配置
   */
  get markerConfig() {
    return this.layer.layerConfig.styleConfig;
  }

  /**
   * 设置 marker 配置
   */
  set markerConfig(config) {
    this.layer.layerConfig.changeStyleConfig(config);
  }

  /**
   * 获取 polygon 配置
   */
  get polygonConfig() {
    return this.layer.layerConfig.styleConfig;
  }

  /**
   * 设置 polygon 配置
   */
  set polygonConfig(config) {
    this.layer.layerConfig.changeStyleConfig(config);
  }

  /**
   * 初始化 marker 配置
   * @param {Object} config 配置对象
   */
  initMarkerConfig(config) {
    // 缓存
    !_CACHE[this.layer.id] && (_CACHE[this.layer.id] = {});

    this.markerConfig = config;

    // 存入缓存
    _CACHE[this.layer.id].markerConfig = cloneDeep(this.markerConfig);
  }

  /**
   * 更新 marker 配置
   * @param {String} type 类型
   * @param {Object} config 配置对象
   */
  updateMarkerConfig(type, config = {}) {
    this.markerConfig = {
      iconType: type,
      [type]: {
        ...this.markerConfig[type],
        ...config,
      },
    };
  }

  /**
   * 重置 marker 配置
   * @param {String|Number} id 覆盖物id
   * @param {Object} config 配置对象
   */
  resetMarkerConfig(id, config = null) {
    this.markerConfig = cloneDeep(
      config || _CACHE[id || this.layer.id]?.markerConfig || _DEFAULT_MARKER_CONFIG
    );
    this.updateMarkers(this.markerConfig);
    this._beforeEditMarkerConfig = null;
    return this.markerConfig;
  }

  /**
   * 更新所有 markers
   * @param {Object} config 配置对象
   */
  updateMarkers(config) {
    this.updateMarkerConfig(config.iconType, config[config.iconType]);
    this.layer.markers.map((i) => {
      i.updateIcon(config);
      i.createContextMenu(this.layer);
      i.updateLabel({
        opacity: 1,
      });
    });
  }

  /**
   * 初始化 polygon 配置
   * @param {Object} config 配置对象
   */
  initPolygonConfig(config) {
    // 缓存
    !_CACHE[this.layer.id] && (_CACHE[this.layer.id] = {});

    this.polygonConfig = config;

    // 存入缓存
    _CACHE[this.layer.id].polygonConfig = cloneDeep(this.polygonConfig);
  }

  /**
   * 更新 polygon 配置
   * @param {Object} config 配置对象
   */
  updatePolygonConfig(config) {
    this.polygonConfig = config;
  }

  /**
   * 更新所有 polygons
   * @param {Object} config 配置对象
   */
  updatePolygons(config) {
    this.updatePolygonConfig(config);
    this.layer.polygons.map((i) => {
      i.updateStyle(config);
      i.createContextMenu(this.layer);
      i.updateLabel({
        opacity: 1,
      });
    });
  }

  /**
   * 重置 polygon 配置
   * @param {String|Number} id 覆盖物id
   * @param {Object} config 配置对象
   */
  resetPolygonConfig(id, config = null) {
    this.polygonConfig = cloneDeep(
      config || _CACHE[id || this.layer.id]?.polygonConfig || _DEFAULT_POLYGON_CONFIG
    );
    this.updatePolygons(this.polygonConfig);
    this._beforeEditPolygonConfig = null;
    return this.polygonConfig;
  }

  /**
   * 初始化 polyline 配置
   * @param {Object} config 配置对象
   */
  initPolylineConfig(config) {
    // 缓存
    !_CACHE[this.layer.id] && (_CACHE[this.layer.id] = {});

    this.polylineConfig = config;

    // 存入缓存
    _CACHE[this.layer.id].polylineConfig = cloneDeep(this.polylineConfig);
  }

  /**
   * 更新覆盖物的样式
   * @param {Object} config 样式配置
   */
  updateCellsStyle(config) {
    switch (this.layer.type) {
      case "marker":
        this.updateMarkers(config);
        break;
      case "polygon":
        this.updatePolygons(config);
        break;
    }
  }

  /**
   * 更新覆盖物的配置
   * @param {Object} config 配置
   */
  updateCellConfig(config) {
    this.layer.layerConfig.changeStyleConfig(config);
  }

  /**
   * 更新当前缓存
   * @param {Object} config 配置
   */
  updateCache(config) {
    const typeKey = this.layer.type === "polygon" ? "polygonConfig" : "markerConfig";
    _CACHE[this.layer.id][typeKey] = cloneDeep(config);
  }

  /**
   * 开始编辑样式
   * @param {String} type 类型
   */
  startEditStyle(type) {
    if (type === "marker") {
      this._beforeEditMarkerConfig = cloneDeep(this.markerConfig);
    } else if (type === "polygon") {
      this._beforeEditPolygonConfig = cloneDeep(this.polygonConfig);
    }
  }

  /**
   * 取消编辑样式
   * @param {String} type 类型
   */
  cancelEditStyle(type) {
    if (type === "marker" && this._beforeEditMarkerConfig) {
      this.markerConfig = this._beforeEditMarkerConfig;
      this.updateMarkers(this.markerConfig);
      this._beforeEditMarkerConfig = null;
    } else if (type === "polygon" && this._beforeEditPolygonConfig) {
      this.polygonConfig = this._beforeEditPolygonConfig;
      this.updatePolygons(this.polygonConfig);
      this._beforeEditPolygonConfig = null;
    }
  }

  /**
   * 确认编辑样式
   * @param {String} type 类型
   */
  confirmEditStyle(type) {
    if (type === "marker") {
      this.updateCache(this.markerConfig);
      this._beforeEditMarkerConfig = null;
    } else if (type === "polygon") {
      this.updateCache(this.polygonConfig);
      this._beforeEditPolygonConfig = null;
    }
  }

  /**
   * 通过时间范围控制图层中覆盖物是否显示
   * @param {Array} timeRange 时间范围, 只包含最大值和最小值
   */
  filterCoverByTimeRange(timeRange) {
    const [start, end] = timeRange;
    const coverCells = this.layer.getAllCover();

    if (!start || !end) {
      coverCells.forEach((i) => {
        i.show();
      });
      return;
    }

    coverCells.forEach((i) => {
      // 判断是时间是否在范围内
      let isInRange = false;

      if (!start) {
        isInRange = !i.data.time || new Date(i.data.time).valueOf() <= new Date(end).valueOf();
      } else if (!end) {
        isInRange =
          i.data.time == start || new Date(i.data.time).valueOf() >= new Date(start).valueOf();
      } else {
        isInRange =
          new Date(i.data.time).valueOf() >= new Date(start).valueOf() &&
          new Date(i.data.time).valueOf() <= new Date(end).valueOf();
      }

      if (isInRange) {
        i.show();
      } else {
        i.hide();
      }
    });
  }

  /**
   * 销毁样式管理器
   */
  destroy() {
    // 清理缓存
    if (_CACHE[this.layer.id]) {
      delete _CACHE[this.layer.id];
    }
    this.layer = null;
    this._beforeEditMarkerConfig = null;
    this._beforeEditPolygonConfig = null;
  }
}
