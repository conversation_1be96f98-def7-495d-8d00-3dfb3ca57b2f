import { randomGetid } from "@/utils/com-function";
import LayerConfig from "../LayerConfig.js";
import LayerDataManager from "./LayerDataManager.js";
import LayerStyleManager from "./LayerStyleManager.js";
import LayerEventManager from "./LayerEventManager.js";
import LayerRenderer from "./LayerRenderer.js";
import ErrorCenter, { ERROR_TYPES } from "@/utils/ErrorCenter.js";
import {
  LAYER_TYPES,
  DEFAULT_LAYER_CONFIG,
  validateLayerConfig,
  createDefaultLayerConfig
} from "./types.js";

// 缓存
let _CACHE = {};

/**
 * 重构后的地图图层类
 * 采用组合模式，将职责分离到不同的管理器中
 */
export default class Layer {
  constructor(
    Map,
    {
      id = randomGetid(),
      hide = false,
      isSelected = false,
      rightAction = true,
      markers = [],
      polygons = [],
      polylines = [],
      isStatic = true,
      showEntityDetailCallBack = null,
    } = {}
  ) {
    // 基本属性
    this.id = id;
    this.Map = Map["_map"]; // 百度地图的实例
    this._map = Map; // 包装过的地图实例
    this.hide = hide; // 图层显示隐藏
    this.isSelected = isSelected; // 被选中
    this.rightAction = rightAction; // 右键操作显示
    this.status = false; // 当前配置无法展示图层

    // 覆盖物数组
    this.markers = markers;
    this.polygons = polygons;
    this.polylines = polylines;

    // 图层配置
    this.layerConfig = {};
    
    // 回调函数
    this.showEntityDetailCallBack = showEntityDetailCallBack;

    // 初始化管理器
    this.dataManager = new LayerDataManager(this, { isStatic });
    this.styleManager = new LayerStyleManager(this);
    this.eventManager = new LayerEventManager(this);
    this.renderer = new LayerRenderer(this);

    // 缓存当前实例
    _CACHE[this.id] = this;
  }

  /**
   * 获取图层类型
   */
  get type() {
    return this.layerConfig.type;
  }

  /**
   * 获取资源字段
   */
  get resourceColumns() {
    return this.dataManager.columns.columnConfig;
  }

  /**
   * 获取 marker 配置
   */
  get markerConfig() {
    return this.styleManager.markerConfig;
  }

  /**
   * 设置 marker 配置
   */
  set markerConfig(config) {
    this.styleManager.markerConfig = config;
  }

  /**
   * 获取 polygon 配置
   */
  get polygonConfig() {
    return this.styleManager.polygonConfig;
  }

  /**
   * 设置 polygon 配置
   */
  set polygonConfig(config) {
    this.styleManager.polygonConfig = config;
  }

  /**
   * 获取资源字段映射
   */
  get resourceColumnsMap() {
    return this.eventManager.resourceColumnsMap;
  }

  /**
   * 根据图层配置初始化图层
   * @param {Object} layerConfig 图层配置
   */
  async init(layerConfig) {
    try {
      // 验证配置
      if (!validateLayerConfig(layerConfig)) {
        const error = ErrorCenter.createError(
          ERROR_TYPES.VALIDATION_ERROR,
          'Invalid layer configuration',
          { layerConfig, layerId: this.id }
        );
        throw error;
      }

      this.layerConfig = new LayerConfig(layerConfig);

      // 初始化各个管理器
      await this.dataManager.init(layerConfig);
      this.eventManager.setStack(this.dataManager.getResourceInfo().columns);
    } catch (error) {
      ErrorCenter.handle(error, `Layer.init[${this.id}]`, {
        layerConfig,
        layerId: this.id
      });
      throw error;
    }
  }

  /**
   * 加载数据
   */
  async loadData() {
    try {
      await this.renderer.loadLayerData();
    } catch (error) {
      ErrorCenter.handle(error, `Layer.loadData[${this.id}]`, {
        layerId: this.id,
        layerType: this.type
      });
      throw error;
    }
  }

  /**
   * 获取资源数据
   */
  getResourceData() {
    return this.dataManager.getResourceData();
  }

  /**
   * 获取分页参数
   */
  getPageParam() {
    return this.dataManager.getPageParam();
  }

  /**
   * 获取资源详情
   */
  getResourceInfo() {
    return this.dataManager.getResourceInfo();
  }

  /**
   * 获取字段映射名称
   */
  getMapFieldNames() {
    return this.eventManager.getMapFieldNames();
  }

  /**
   * 修改图层的显示和隐藏
   */
  changeShow() {
    this.eventManager.changeShow();
  }

  /**
   * 删除图层
   */
  removeLayer() {
    delete _CACHE[this.id];
    this.renderer.removeLayerOverlay();
  }

  /**
   * 操作打点画图等绘制
   */
  manualDraw(type, data) {
    return this.renderer.manualDraw(type, data);
  }

  /**
   * 打开编辑弹窗
   */
  openEdit(openModal, overlay) {
    this.eventManager.openEdit(openModal, overlay);
  }

  /**
   * 删除覆盖物
   */
  delOverlay(type, id) {
    this.eventManager.delOverlay(type, id);
  }

  /**
   * 初始化 marker 配置
   */
  initMarkerConifg(config) {
    this.styleManager.initMarkerConfig(config);
  }

  /**
   * 更新 marker 配置
   */
  updateMarkerConfig(type, config) {
    this.styleManager.updateMarkerConfig(type, config);
  }

  /**
   * 重置 marker 配置
   */
  resetMarkerConfig(id, config) {
    return this.styleManager.resetMarkerConfig(id, config);
  }

  /**
   * 更新 markers
   */
  updateMarkers(config) {
    this.styleManager.updateMarkers(config);
  }

  /**
   * 初始化 polygon 配置
   */
  initPolygonConfig(config) {
    this.styleManager.initPolygonConfig(config);
  }

  /**
   * 更新 polygon 配置
   */
  updatePolygonConfig(config) {
    this.styleManager.updatePolygonConfig(config);
  }

  /**
   * 更新 polygons
   */
  updatePolygons(config) {
    this.styleManager.updatePolygons(config);
  }

  /**
   * 重置 polygon 配置
   */
  resetPolygonConfig(id, config) {
    return this.styleManager.resetPolygonConfig(id, config);
  }

  /**
   * 更新当前缓存
   */
  updateCache(config) {
    this.styleManager.updateCache(config);
  }

  /**
   * 获取所有覆盖物
   */
  getAllCover() {
    return this.renderer.getAllCover();
  }

  /**
   * 获取时间范围
   */
  getTimeRange() {
    return this.dataManager.getTimeRange();
  }

  /**
   * 通过时间范围控制图层中覆盖物是否显示
   */
  filterCoverByTimeRange(timeRange) {
    this.styleManager.filterCoverByTimeRange(timeRange);
  }

  /**
   * 更新覆盖物的样式
   */
  updateCellsStyle(config) {
    this.styleManager.updateCellsStyle(config);
  }

  /**
   * 更新覆盖物的配置
   */
  updateCllConfig(config) {
    this.styleManager.updateCellConfig(config);
  }

  /**
   * 更新图层中的数据
   */
  updateData(data = null, translateToLocal = false) {
    this.eventManager.handleDataUpdate(data, translateToLocal);
  }

  /**
   * 更新数量
   */
  updateTotal(temp, type) {
    this.dataManager.updateTotal(temp, type);
  }

  /**
   * 销毁图层
   */
  destroy() {
    // 销毁各个管理器
    this.dataManager?.destroy();
    this.styleManager?.destroy();
    this.eventManager?.destroy();
    this.renderer?.destroy();

    // 清理缓存
    delete _CACHE[this.id];

    // 清理引用
    this.dataManager = null;
    this.styleManager = null;
    this.eventManager = null;
    this.renderer = null;
    this.layerConfig = null;
    this.Map = null;
    this._map = null;
  }
}
