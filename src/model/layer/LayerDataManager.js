import { createStore } from "@daas/surface-web-lib/resource";
import { toColumnObj } from "@/utils/utils";
import StaticData from "../StaticData.js";
import Columns from "../Columns.js";
import store from "@/store/index.js";
import DataSourceFactory, { DATA_SOURCE_TYPES } from "../datasource/DataSourceFactory.js";
import ErrorCenter, { ERROR_TYPES } from "@/utils/ErrorCenter.js";

// 字段映射列定义
export const MarkerColumns = [
  { name: "name", displayName: "名称", configure: true },
  { name: "lng", displayName: "经度", configure: true },
  { name: "lat", displayName: "纬度", configure: true },
];

export const PolygonColumns = [
  { name: "pointList", displayName: "点集", configure: true },
  { name: "centerPoint", displayName: "中心点", configure: true },
];

const DefaultColumns = [
  { name: "id", displayName: "主键", configure: true },
  ...MarkerColumns,
  ...PolygonColumns,
  { name: "time", displayName: "时间", configure: true },
];

export const ColRelation = new Map([
  ["idColumn", { column: "id", required: true }],
  ["nameColumn", { column: "name", required: true }],
  ["longitudeColumn", { column: "lng", required: true }],
  ["latitudeColumn", { column: "lat", required: true }],
  ["pointListColumn", { column: "pointList", required: true }],
  ["centerPointColumn", { column: "centerPoint", required: true }],
  ["timeColumn", { column: "time", required: false }],
  ["startColumn", { column: "startPoint", required: true }],
  ["endColumn", { column: "endPoint", required: true }],
]);

/**
 * 图层数据管理器
 * 负责处理图层的数据相关逻辑，包括静态数据和动态数据的管理
 */
export default class LayerDataManager {
  constructor(layer, config = {}) {
    this.layer = layer;
    this.isStatic = config.isStatic || true;

    // 数据源
    this.dataSource = null;

    // 兼容性：保留原有的数据存储
    this.resourceStore = createStore();
    this.staticStore = null;
    this.columns = new Columns();

    // 统计信息
    this.numberOfTags = 0;
    this.hasTimeRange = false;
  }

  /**
   * 初始化数据源
   * @param {Object} layerConfig 图层配置
   */
  async init(layerConfig) {
    try {
      // 创建新的数据源
      if (this.isStatic) {
        this.dataSource = DataSourceFactory.createStatic({
          id: `${this.layer.id}_datasource`,
          localData: layerConfig.localData || [],
          mappingConfig: layerConfig.mappingConfig
        });
        await this.dataSource.init(layerConfig);

        // 兼容性：同时初始化旧的静态数据
        this._initStatic(layerConfig);
      } else {
        this.dataSource = DataSourceFactory.createDynamic({
          id: `${this.layer.id}_datasource`,
          resourceId: layerConfig.resourceId
        });
        await this.dataSource.init(layerConfig);

        // 兼容性：同时初始化旧的资源数据
        await this._initResource(layerConfig.resourceId);
      }
    } catch (error) {
      ErrorCenter.handle(error, `LayerDataManager.init[${this.layer.id}]`, {
        layerConfig,
        isStatic: this.isStatic
      });
      throw error;
    }
  }

  /**
   * 初始化静态数据
   * @param {Object} layerConfig 图层配置
   */
  _initStatic(layerConfig) {
    let _staticData = new StaticData({ type: layerConfig.type });

    const { localData = [], mappingConfig } = layerConfig;

    _staticData.initLocalData(localData);

    let format = true;
    if (mappingConfig) {
      format = false;
      _staticData.mockData.columns = mappingConfig;
    }

    const _mockResourceInfo = _staticData.mockData;
    this.resourceStore.commit("setResourceInfo", _mockResourceInfo);

    this.staticStore = _staticData;

    this._formatColumns(format);
    this._setLayerType();
  }

  /**
   * 根据资源编码，初始化图层
   * @param {String} resourceId 资源编码
   */
  async _initResource(resourceId) {
    if (resourceId && resourceId != "") {
      const resourceInfo = await this.resourceStore.dispatch("getResourceInfo", {
        resourceId: resourceId,
      });
      this.resourceStore.commit("setResourceInfo", resourceInfo);
      this._formatParams();
      this._formatColumns();
      this._setLayerType();
      this._initDisplayName(resourceInfo);
    }
  }

  /**
   * 初始化显示名称
   * @param {Object} resourceInfo 资源信息
   */
  _initDisplayName(resourceInfo) {
    this.layer.layerConfig.displayName = resourceInfo.resourceName;
  }

  /**
   * 初始化数据获取参数
   */
  _formatParams() {
    const modularType = store.getters["modularType"];
    const modularId = store.getters["modularId"];
    if (modularId && modularId != "") {
      this.resourceStore.commit("data/setExtendAuthParams", {
        modularType: modularType,
        modularId: modularId,
      });
    }
    this.resourceStore.commit("data/setPageParam", { pageIndex: 1, limit: 100 });
  }

  /**
   * 解析字段
   * @param {Boolean} format 是否需要格式化
   */
  _formatColumns(format = true) {
    if (format) {
      this._format(this.getResourceInfo().columns);
    }

    const cols = toColumnObj(this.layer.layerConfig.mappingConfig);
    const _resourceColumns = DefaultColumns.map((r) => {
      if (cols[r.name]) return Object.assign(cols[r.name], r);
      else return r;
    });
    
    // 存储配置
    this.columns.setConfigByConfig(_resourceColumns);
  }

  /**
   * 格式化字段映射
   * @param {Array} columns 字段列表
   */
  _format(columns) {
    columns.forEach((item) => {
      const hased = this.layer.layerConfig.mappingConfig.find(
        (r) => r.mapColumnName === item.name
      );
      if (!hased) {
        if (item.isPrimaryKey == "1" && (!item.columnType || item.columnType == "")) {
          item.columnType = "idColumn";
        }
        const _col_relation = ColRelation.get(item.columnType);
        if (_col_relation) {
          this.layer.layerConfig.mappingConfig.push({
            name: _col_relation.column,
            displayName: item.displayName || item.name,
            mapColumnName: item.name,
            columnType: item.columnType,
          });
        }
      }
    });
  }

  /**
   * 设置图层类别
   * @param {Boolean} mandatory 是否强制更新 type
   */
  _setLayerType(mandatory = false) {
    const { mappingConfig, type } = this.layer.layerConfig;
    // 如果 type 存在则不需要识别
    if (type && !mandatory) return;

    const mappingStatus = {
      hasLat:
        mappingConfig.find((r) => r.name == "lat") &&
        !!mappingConfig.find((r) => r.name == "lat").mapColumnName,
      hasLng:
        mappingConfig.find((r) => r.name == "lng") &&
        !!mappingConfig.find((r) => r.name == "lng").mapColumnName,
      hasCenterPoint:
        mappingConfig.find((r) => r.name == "centerPoint") &&
        !!mappingConfig.find((r) => r.name == "centerPoint").mapColumnName,
      hasPointList:
        mappingConfig.find((r) => r.name == "pointList") &&
        !!mappingConfig.find((r) => r.name == "pointList").mapColumnName,
    };

    if (mappingStatus.hasLat && mappingStatus.hasLng) {
      this.layer.layerConfig.changeType("marker");
    } else if (mappingStatus.hasCenterPoint && mappingStatus.hasPointList) {
      this.layer.layerConfig.changeType("polygon");
    }
  }

  /**
   * 加载数据
   */
  async loadData() {
    if (!this.isStatic) {
      await this.resourceStore.dispatch("data/getData");
    }
    return this.getResourceData();
  }

  /**
   * 获取资源数据
   * @returns {Array} 资源数据
   */
  getResourceData() {
    const data = this.isStatic
      ? this.staticStore.getAllLocalData()
      : this.resourceStore.getters["data/resourceDataList"];
    
    this.getPageParam();
    this.updateTotal(data, "init");
    return data;
  }

  /**
   * 获取分页参数
   * @returns {Object} 分页参数
   */
  getPageParam() {
    let params = this.resourceStore.getters["data/pageParam"];
    this.numberOfTags = params.recordTotal;
    return params;
  }

  /**
   * 获取资源详情
   * @returns {Object} 资源详情
   */
  getResourceInfo() {
    return this.resourceStore.getters["resourceInfo"];
  }

  /**
   * 更新数量
   * @param {Array} temp 数据
   * @param {String} type 操作类型
   */
  updateTotal(temp, type) {
    let params = this.resourceStore.getters["data/pageParam"];

    if (this.isStatic) {
      params.recordTotal = temp?.length || 0;
    } else {
      const number = type === "add" ? 1 : type === "remove" ? -1 : 0;
      this.numberOfTags += number;
      params.recordTotal = this.numberOfTags || 0;
    }

    this.resourceStore.commit("data/setPageParam", params);
  }

  /**
   * 更新数据
   * @param {Object} data 数据
   * @param {Boolean} translateToLocal 是否需要翻译成本地数据
   */
  updateData(data = null, translateToLocal = false) {
    let _data = data;
    
    // 转换数据
    if (translateToLocal) {
      const { data: updateData, rawData } = this.columns.translateData(data, "toLocal");
      _data = {
        ...updateData,
        rawData,
      };
    }

    // 静态数据
    if (this.isStatic) {
      this.staticStore.update(_data);
    }

    return _data;
  }

  /**
   * 获取时间范围
   * @returns {Array} 时间范围数组
   */
  getTimeRange() {
    let coverCells = [];
    switch (this.layer.type) {
      case "marker":
        coverCells = this.layer.markers;
        break;
      case "polygon":
        coverCells = this.layer.polygons;
        break;
      case "polyline":
        coverCells = this.layer.polylines;
        break;
    }
    const timeRange = coverCells.map((i) => i.data.time);
    this.hasTimeRange = timeRange.some(Boolean);
    return timeRange;
  }

  /**
   * 销毁数据管理器
   */
  destroy() {
    this.resourceStore = null;
    this.staticStore = null;
    this.columns = null;
    this.layer = null;
  }
}
