import store from "@/store/index.js";
import Stack from "../stack.js";

/**
 * 图层事件管理器
 * 负责处理图层的事件逻辑和用户交互
 */
export default class LayerEventManager {
  constructor(layer) {
    this.layer = layer;
    this.stack = null;
  }

  /**
   * 设置记录操作数组
   * @param {Array} columns 字段列表
   */
  setStack(columns) {
    this.stack = new Stack(columns, {});
  }

  /**
   * 打开编辑弹窗
   * @param {Boolean} openModal 是否打开弹窗
   * @param {Object} overlay 覆盖物
   */
  openEdit(openModal, overlay) {
    if (openModal) {
      // 如果存储的图层不是当前图层则切换图层
      const { currentLayer } = store.getters;
      if (currentLayer.id !== this.layer.id) {
        store.commit("setCurrentLayer", this.layer);
      }

      const modelData = {
        layer: this.layer,
        ...overlay,
      };
      store.commit("setting/setUpdateModalData", modelData);
    } else {
      this.stack.update(overlay.extData); //添加到 stack
    }
    store.commit("setting/setUpdateModalOpen", openModal);
  }

  /**
   * 改变图层类型
   * @param {String} value 图层类型
   */
  changeLayerType(value) {
    this.layer.layerConfig.changeType(value);

    store.commit("setting/modifyDrawActionStatus", {
      drawType: value,
      type: "isDisabled",
      value: false,
    });
  }

  /**
   * 修改图层的显示和隐藏
   */
  changeShow() {
    this.layer.hide = !this.layer.hide;
    if (this.layer.hide) {
      this._hideLayer();
    } else {
      this._showLayer();
    }
  }

  /**
   * 隐藏图层
   * @private
   */
  _hideLayer() {
    this.layer.markers.map((d) => d.marker.hide());
    this.layer.polygons.map((d) => {
      d.polygon.hide();
      d.label.hide();
    });
    this.layer.polylines.map((d) => d.polyline.hide());
  }

  /**
   * 显示图层
   * @private
   */
  _showLayer() {
    this.layer.markers.map((d) => d.marker.show());
    this.layer.polygons.map((d) => {
      d.polygon.show();
      d.label.show();
    });
    this.layer.polylines.map((d) => d.polyline.show());
  }

  /**
   * 删除覆盖物
   * @param {String} type 覆盖物类型
   * @param {String} id 覆盖物ID
   */
  delOverlay(type, id) {
    const overlays = this.layer[`${type}s`];
    const index = overlays.findIndex((item) => item.id === id);
    if (index !== -1) {
      overlays.splice(index, 1);
    }
  }

  /**
   * 获取字段映射名称
   * @returns {Object} 字段映射对象
   */
  getMapFieldNames() {
    const { mappingConfig } = this.layer.layerConfig;
    const mapFieldNames = {};
    
    mappingConfig.forEach((config) => {
      mapFieldNames[config.name] = config.mapColumnName;
    });
    
    return mapFieldNames;
  }

  /**
   * 获取资源字段映射
   * @returns {Object} 资源字段映射
   */
  get resourceColumnsMap() {
    const { mappingConfig } = this.layer.layerConfig;
    const map = {};
    
    mappingConfig.forEach((config) => {
      map[config.name] = config.mapColumnName;
    });
    
    return map;
  }

  /**
   * 处理数据更新事件
   * @param {Object} data 更新的数据
   * @param {Boolean} translateToLocal 是否需要翻译成本地数据
   */
  handleDataUpdate(data = null, translateToLocal = false) {
    let _data = this.layer.dataManager.updateData(data, translateToLocal);
    
    this.stack.update(_data); // 添加到stack

    const oldDatas =
      this.layer.type === "marker" 
        ? this.layer.markers 
        : this.layer.type === "polygon" 
        ? this.layer.polygons 
        : null;

    if (oldDatas) {
      const index = oldDatas.findIndex((item) => item.id === _data.id);
      if (index >= 0) {
        const current = oldDatas[index];
        current.updateData(_data);
      }
    }
  }

  /**
   * 处理覆盖物删除事件
   * @param {String} type 覆盖物类型
   * @param {String} id 覆盖物ID
   */
  handleOverlayDelete(type, id) {
    this.delOverlay(type, id);
    this.stack.delete(id);
    this.layer.dataManager.updateTotal(null, "remove");
  }

  /**
   * 处理覆盖物添加事件
   * @param {Object} overlay 覆盖物对象
   */
  handleOverlayAdd(overlay) {
    this.layer.dataManager.updateTotal(null, "add");
    return overlay;
  }

  /**
   * 处理图层选择事件
   * @param {Boolean} selected 是否选中
   */
  handleLayerSelect(selected) {
    this.layer.isSelected = selected;
    
    if (selected) {
      store.commit("setCurrentLayer", this.layer);
    }
  }

  /**
   * 处理右键菜单事件
   * @param {String} action 动作类型
   * @param {Object} target 目标对象
   */
  handleContextMenu(action, target) {
    switch (action) {
      case "edit":
        this.openEdit(true, target);
        break;
      case "delete":
        this.handleOverlayDelete(target.type, target.id);
        break;
      case "copy":
        // 复制逻辑
        break;
      default:
        console.warn(`Unknown context menu action: ${action}`);
    }
  }

  /**
   * 处理绘制完成事件
   * @param {String} type 绘制类型
   * @param {Object} data 绘制数据
   */
  handleDrawComplete(type, data) {
    const overlay = this.layer.renderer.createOverlay(type, data);
    this.handleOverlayAdd(overlay);
    return overlay;
  }

  /**
   * 销毁事件管理器
   */
  destroy() {
    this.stack = null;
    this.layer = null;
  }
}
