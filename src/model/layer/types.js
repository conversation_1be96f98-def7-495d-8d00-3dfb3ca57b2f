/**
 * 图层类型定义
 * 定义图层相关的常量和类型
 */

// 图层类型枚举
export const LAYER_TYPES = {
  MARKER: 'marker',
  POLYGON: 'polygon',
  POLYLINE: 'polyline'
};

// 覆盖物状态枚举
export const OVERLAY_STATUS = {
  VISIBLE: 'visible',
  HIDDEN: 'hidden',
  EDITING: 'editing',
  SELECTED: 'selected'
};

// 数据源类型枚举
export const DATA_SOURCE_TYPES = {
  STATIC: 'static',
  DYNAMIC: 'dynamic'
};

// 事件类型枚举
export const EVENT_TYPES = {
  LAYER_CREATED: 'layer:created',
  LAYER_UPDATED: 'layer:updated',
  LAYER_DELETED: 'layer:deleted',
  LAYER_SHOW: 'layer:show',
  LAYER_HIDE: 'layer:hide',
  OVERLAY_ADDED: 'overlay:added',
  OVERLAY_UPDATED: 'overlay:updated',
  OVERLAY_DELETED: 'overlay:deleted',
  STYLE_CHANGED: 'style:changed'
};

// 错误类型枚举
export const ERROR_TYPES = {
  INVALID_CONFIG: 'invalid_config',
  DATA_LOAD_FAILED: 'data_load_failed',
  RENDER_FAILED: 'render_failed',
  RESOURCE_NOT_FOUND: 'resource_not_found',
  PERMISSION_DENIED: 'permission_denied'
};

// 默认配置
export const DEFAULT_LAYER_CONFIG = {
  id: null,
  name: '未命名图层',
  displayName: '未命名图层',
  type: LAYER_TYPES.MARKER,
  visible: true,
  isStatic: true,
  styleConfig: {},
  mappingConfig: []
};

// 字段映射关系
export const FIELD_MAPPING = {
  ID: 'id',
  NAME: 'name',
  LONGITUDE: 'lng',
  LATITUDE: 'lat',
  POINT_LIST: 'pointList',
  CENTER_POINT: 'centerPoint',
  TIME: 'time',
  START_POINT: 'startPoint',
  END_POINT: 'endPoint'
};

// 样式配置键
export const STYLE_KEYS = {
  ICON_TYPE: 'iconType',
  STROKE_COLOR: 'strokeColor',
  FILL_COLOR: 'fillColor',
  STROKE_WEIGHT: 'strokeWeight',
  STROKE_OPACITY: 'strokeOpacity',
  FILL_OPACITY: 'fillOpacity'
};

/**
 * 验证图层配置
 * @param {Object} config 图层配置
 * @returns {Boolean} 是否有效
 */
export function validateLayerConfig(config) {
  if (!config || typeof config !== 'object') {
    return false;
  }

  // 检查必需字段
  if (!config.type || !Object.values(LAYER_TYPES).includes(config.type)) {
    return false;
  }

  return true;
}

/**
 * 创建默认图层配置
 * @param {Object} overrides 覆盖配置
 * @returns {Object} 图层配置
 */
export function createDefaultLayerConfig(overrides = {}) {
  return {
    ...DEFAULT_LAYER_CONFIG,
    ...overrides,
    id: overrides.id || `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  };
}

/**
 * 检查是否为有效的覆盖物类型
 * @param {String} type 类型
 * @returns {Boolean} 是否有效
 */
export function isValidOverlayType(type) {
  return Object.values(LAYER_TYPES).includes(type);
}

/**
 * 检查是否为有效的数据源类型
 * @param {String} type 类型
 * @returns {Boolean} 是否有效
 */
export function isValidDataSourceType(type) {
  return Object.values(DATA_SOURCE_TYPES).includes(type);
}

/**
 * 格式化错误信息
 * @param {String} type 错误类型
 * @param {String} message 错误消息
 * @param {Object} context 上下文信息
 * @returns {Object} 格式化的错误对象
 */
export function formatError(type, message, context = {}) {
  return {
    type,
    message,
    context,
    timestamp: new Date().toISOString()
  };
}
