import Marker from "../marker";
import Polygon from "../polygon";
import Polyline from "../Polyline";

/**
 * 图层渲染器
 * 负责处理图层的渲染逻辑，包括覆盖物的创建和管理
 */
export default class LayerRenderer {
  constructor(layer) {
    this.layer = layer;
  }

  /**
   * 加载图层数据并渲染
   */
  async loadLayerData() {
    const resourceData = await this.layer.dataManager.loadData();
    
    if (resourceData && resourceData.length > 0) {
      this._renderOverlays(resourceData);
    }
  }

  /**
   * 渲染覆盖物
   * @param {Array} resourceData 资源数据
   * @private
   */
  _renderOverlays(resourceData) {
    resourceData.forEach((data) => {
      const overlayData = this._prepareOverlayData(data);
      this.createOverlay(this.layer.type, overlayData);
    });
  }

  /**
   * 准备覆盖物数据
   * @param {Object} data 原始数据
   * @returns {Object} 处理后的覆盖物数据
   * @private
   */
  _prepareOverlayData(data) {
    const { data: tempData, rawData } = this.layer.dataManager.columns.translateData(data, "toLocal");
    
    switch (this.layer.type) {
      case "marker":
        return this._createMarkerData(tempData, rawData);
      case "polygon":
        return this._createPolygonData(tempData, rawData);
      case "polyline":
        return this._createPolylineData(tempData, rawData);
      default:
        throw new Error(`Unknown layer type: ${this.layer.type}`);
    }
  }

  /**
   * 创建标记点数据
   * @param {Object} tempData 临时数据
   * @param {Object} rawData 原始数据
   * @returns {Object} 标记点数据
   * @private
   */
  _createMarkerData(tempData, rawData) {
    return {
      id: tempData.id,
      name: tempData.name,
      point: {
        lng: tempData.lng,
        lat: tempData.lat,
      },
      extData: rawData,
      data: tempData,
      pointConfig: this.layer.layerConfig.styleConfig,
    };
  }

  /**
   * 创建多边形数据
   * @param {Object} tempData 临时数据
   * @param {Object} rawData 原始数据
   * @returns {Object} 多边形数据
   * @private
   */
  _createPolygonData(tempData, rawData) {
    return {
      id: tempData.id,
      name: tempData.name,
      pointList: tempData.pointList,
      centerPoint: tempData.centerPoint,
      extData: rawData,
      data: tempData,
      options: this.layer.layerConfig.styleConfig,
    };
  }

  /**
   * 创建折线数据
   * @param {Object} tempData 临时数据
   * @param {Object} rawData 原始数据
   * @returns {Object} 折线数据
   * @private
   */
  _createPolylineData(tempData, rawData) {
    return {
      id: tempData.id,
      name: tempData.name,
      pointList: tempData.pointList,
      extData: rawData,
      data: tempData,
      options: this.layer.layerConfig.styleConfig,
    };
  }

  /**
   * 创建覆盖物
   * @param {String} type 覆盖物类型
   * @param {Object} data 覆盖物数据
   * @returns {Object} 覆盖物实例
   */
  createOverlay(type, data) {
    switch (type) {
      case "marker":
        return this._drawMarker(data);
      case "polygon":
        return this._drawPolygon(data);
      case "polyline":
        return this._drawPolyline(data);
      default:
        throw new Error(`Unknown overlay type: ${type}`);
    }
  }

  /**
   * 绘制标记点
   * @param {Object} marker 标记点数据
   * @returns {Object} 标记点实例
   * @private
   */
  _drawMarker(marker) {
    const markerInstance = new Marker(this.layer, marker);
    this.layer.markers.push(markerInstance);
    this.layer.Map.addOverlay(markerInstance.marker);
    return markerInstance;
  }

  /**
   * 绘制多边形
   * @param {Object} polygon 多边形数据
   * @returns {Object} 多边形实例
   * @private
   */
  _drawPolygon(polygon) {
    const polygonInstance = new Polygon(this.layer, polygon);
    this.layer.polygons.push(polygonInstance);
    this.layer.Map.addOverlay(polygonInstance.polygon);
    this.layer.Map.addOverlay(polygonInstance.label);
    return polygonInstance;
  }

  /**
   * 绘制折线
   * @param {Object} polyline 折线数据
   * @returns {Object} 折线实例
   * @private
   */
  _drawPolyline(polyline) {
    const polylineInstance = new Polyline(this.layer, polyline);
    this.layer.polylines.push(polylineInstance);
    this.layer.Map.addOverlay(polylineInstance.polyline);
    return polylineInstance;
  }

  /**
   * 手动绘制覆盖物
   * @param {String} type 覆盖物类型
   * @param {Object} data 覆盖物数据
   * @returns {Object} 覆盖物实例
   */
  manualDraw(type, data) {
    if (type === "marker") {
      return this._drawMarker(data.marker);
    } else if (type === "polygon") {
      return this._drawPolygon(data.polygon);
    } else if (type === "polyline") {
      return this._drawPolyline(data.polyline);
    }
  }

  /**
   * 移除覆盖物
   * @param {String} type 覆盖物类型
   */
  removeLayerOverlay(type = "") {
    type = type || this.layer.type;
    if (type === "marker") {
      this.layer.markers.map((d) => this.layer.Map.removeOverlay(d.marker));
    } else if (type === "polygon") {
      this.layer.polygons.map((d) => {
        this.layer.Map.removeOverlay(d.polygon);
        this.layer.Map.removeOverlay(d.label);
      });
    } else if (type === "polyline") {
      this.layer.polylines.map((d) => this.layer.Map.removeOverlay(d.polyline));
    }
  }

  /**
   * 清除所有覆盖物
   */
  clearAllOverlays() {
    // 清除标记点
    this.layer.markers.forEach((marker) => {
      this.layer.Map.removeOverlay(marker.marker);
    });
    this.layer.markers = [];

    // 清除多边形
    this.layer.polygons.forEach((polygon) => {
      this.layer.Map.removeOverlay(polygon.polygon);
      this.layer.Map.removeOverlay(polygon.label);
    });
    this.layer.polygons = [];

    // 清除折线
    this.layer.polylines.forEach((polyline) => {
      this.layer.Map.removeOverlay(polyline.polyline);
    });
    this.layer.polylines = [];
  }

  /**
   * 获取所有覆盖物
   * @returns {Array} 所有覆盖物数组
   */
  getAllCover() {
    return [...this.layer.markers, ...this.layer.polygons, ...this.layer.polylines];
  }

  /**
   * 重新渲染图层
   */
  rerender() {
    this.clearAllOverlays();
    this.loadLayerData();
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.clearAllOverlays();
    this.layer = null;
  }
}
