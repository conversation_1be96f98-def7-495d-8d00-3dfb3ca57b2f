import { uuid } from "@/utils/com-function";

const PolylineOptions = {
  // 折线颜色
  strokeColor: "#40a9ff",
  // 折线的宽度，以像素为单位
  strokeWeight: 2,
  // 折线的透明度，取值范围0 - 1
  strokeOpacity: 1,
  // 折线的样式，solid或dashed
  strokeStyle: "solid",
  // 是否启用map.clearOverlays清除该覆盖物，默认为true
  enableMassClear: true,
  // 是否可编辑，默认为false
  enableEditing: false,
  // 是否响应点击事件，默认为true
  enableClicking: true,
  // 配置点标记的图标
  icons: [],
};
export const _DEFAULT_POLYLINE_CONFIG = PolylineOptions;

export default class Polyline {
  constructor(layer, param) {
    this.id = param.id || uuid();
    this.name = param.name === "" || param.name ? param.name : "未命名";
    this.extData = param.extData || {};
    this.data = param.data || {};

    this.mapFieldNames = layer.getMapFieldNames();
    this.layer = layer;

    this.initPolyline(param?.pointList, param?.options);
  }

  show() {
    this.polyline.show();
  }

  hide() {
    this.polyline.hide();
  }

  initPolyline(pointList, options) {
    let points = [];
    try {
      points = JSON.parse(pointList);
    } catch (error) {
      // ...
    }
    points = points.map((i) => {
      if(i instanceof BMap.Point) {
        return i;
      }

      return new BMap.Point(i[0], i[1]);
    });
    this.polyline = new BMap.Polyline(points, options || PolylineOptions);
  }
}
