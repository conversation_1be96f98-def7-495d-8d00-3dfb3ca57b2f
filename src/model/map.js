import DrawingTool from "./drawingTool";
import InfoWindow from "./info-window";
import MapSettings from "./MapSettings";
import ProvincePolygon from "./ProvincePolygon";

export default class Map {
  constructor(dom, drawCallback, settings) {
    this._map = new BMap.Map(dom);

    this.settings = new MapSettings(settings);
    this.provincePolygon = new ProvincePolygon(this._map, this.settings?.provincePolygonConfig);

    // this._point = new BMap.Point(112.104044, 24.348883);
    // this._point = new BMap.Point(this.settings.center.lng, this.settings.center.lat);
    this._map.centerAndZoom(
      new BMap.Point(this.settings.center.lng, this.settings.center.lat),
      this.settings.zoom,
    ); // 初始化地图，设置中心点坐标和地图级别
    // this._map.setCurrentCity("广州"); // 设置地图显示的城市 此项是必须设置的
    this._map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放

    this._infoWindow = this._initInfoWindow();
    this.infoWindow = this._infoWindow._infoWindow;

    this.drawingTool = null;
    this.drawCallback = drawCallback;
    this.initDrawingTool();
    this.initEvent();
  }

  // 初始化事件
  initEvent() {
    this._map.addEventListener("click", (ev) => {
      this.provincePolygon.cancelHightLight(ev);
    });
  }

  initDrawingTool() {
    this.drawingTool = new DrawingTool(this._map, {
      drawCallback: this.drawCallback,
    });
  }

  _initInfoWindow() {
    return new InfoWindow();
  }

  openInfoWindow(position, options = {}) {
    if (!position) throw Error("没有传入位置实例");

    const { confirm, cancel, title, content } = options;
    confirm && this._infoWindow.addEventListener("confirm", confirm);
    cancel && this._infoWindow.addEventListener("cancel", cancel);
    title && this._infoWindow.updateTitle(title);
    content && this._infoWindow.updateContent(content);

    this._map.openInfoWindow(this.infoWindow, position);
  }

  closeInfoWindow() {
    this._infoWindow.removeEventListener("confirm");
    this._infoWindow.removeEventListener("cancel");
    this._map.closeInfoWindow();
  }

  // 初始化设置
  initSettings(settings) {
    this.settings = new MapSettings(settings);
    this.setZoom(this.settings.zoom);
    this.targetDragging(this.settings.dragEnabled);
    this.setCenter(this.settings.center);
    this.provincePolygon = new ProvincePolygon(this._map, this.settings?.provincePolygonConfig);
  }

  // 管理拖拽
  targetDragging(flag) {
    if (flag === undefined) {
      this.settings.change("dragEnabled", !this.settings.dragEnabled);
    } else {
      this.settings.change("dragEnabled", flag);
    }
    this.settings.dragEnabled ? this._map.enableDragging() : this._map.disableDragging();
  }

  // 确认修改地图中心点
  confirmChangeCenter(isCancel = false) {
    this.setCenter(isCancel ? this.settings.center : this.tempClickPoint);
    this._map.removeEventListener("click", this.openCenterModalCb);
    this._map.removeOverlay(this.tempCenterPoint);
    this.isOpenCenterModal = false;
    this.openCenterModalCb = null;
    this.tempClickPoint = null;
    this.tempCenterPoint = null;
  }

  // 标记地图中心点
  markCenterPoint(center) {
    if (this.tempCenterPoint) {
      this.tempCenterPoint.setPosition(center);
      return;
    }

    this.tempCenterPoint = new BMap.Marker(center, {
      id: "default-center-point",
      name: "地图中心点",
    });
    this._map.addOverlay(this.tempCenterPoint);
  }

  // 点击选择地图中心
  _clickSelectCenter({ point }) {
    this.tempClickPoint = point;
    this.markCenterPoint(point);
  }

  // 点击选择地图中心
  openCenterModal() {
    if (this.isOpenCenterModal) return;
    this.isOpenCenterModal = true;
    this.openCenterModalCb = this._clickSelectCenter.bind(this);
    this._map.addEventListener("click", this.openCenterModalCb);
    this.markCenterPoint(this.settings.center);
  }

  // 设置地图中心
  setCenter(point) {
    if (!(point instanceof BMap.Point)) {
      point = new BMap.Point(point.lng, point.lat);
    }
    this._map.setCenter(point);
    this._map.panTo(point);
    // this.settings.center = point;
    this.settings.change("center", point);
  }

  getCenter() {
    return this._map.getCenter();
  }

  // 设置默认缩放
  setZoom(zoom) {
    this._map.setZoom(zoom);
    // this.settings.zoom = zoom;
    this.settings.change("zoom", zoom);
  }

  getZoom() {
    return this._map.getZoom();
  }

  // 标记一个省
  markProvince(
    province,
    options = {
      autoViewport: false,
    },
  ) {
    this.provincePolygon.setProvince(province, options);
    this.settings.markProvince = province;
  }

  // 清除标记的省
  clearMarkProvince() {
    this.provincePolygon.clearMarkProvince();
    this.settings.markProvince = null;
  }

  // 导出配置
  exportConfig() {
    this.settings.provincePolygonConfig = this.provincePolygon.exportConfig();
    return this.settings;
  }
}
