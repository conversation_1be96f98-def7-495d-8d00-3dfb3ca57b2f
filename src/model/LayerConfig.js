import { _DEFAULT_POLYGON_CONFIG } from "./polygon";
import { _DEFAULT_MARKER_CONFIG } from "./marker";
import { _DEFAULT_POLYLINE_CONFIG } from "./Polyline";
import Columns from "./Columns";

import { merge } from "lodash-es";

/**
 * 地图图层配置对象，可以被保存
 * mappingConfig 映射配置示例: [{
 *   name: "id",                // 字段名称
 *   displayName: "",          // 显示名称
 *   mapColumnName: "",        // 映射的列名
 *   columnType: ""           // 列类型
 * }]
 */

export default class LayerConfig {
  constructor({
    name,
    displayName,
    icon,
    type,
    resourceId,
    mappingConfig,
    styleConfig,
    coverCells,
  }) {
    this.name = name || ""; // 图层名称
    this.displayName = displayName || ""; // 图层显示名称
    this.icon = icon || ""; // 图层图标
    this.type = type || ""; // 图层类型: polygon(面)、marker(点)、polyline(线)

    // 列配置对象
    this.columns = new Columns();

    this.resourceId = resourceId; // 数据源ID
    this.mappingConfig = mappingConfig || []; // 字段映射关系配置

    // 映射关系对象,用于快速查找
    this.mappingObj = {};
    this.mappingConfig.forEach((i) => {
      this.mappingObj[i.name] = i.mapColumnName;
    });

    // 覆盖物配置
    this.coverCells = coverCells || [];
    // 覆盖物配置对象,用于快速查找
    this.coverCellsObj = this.coverCells.reduce((acc, cell) => {
      acc[cell.id] = cell;
      return acc;
    }, {});

    // 样式配置,marker(点)和polygon(面)共用一个配置
    this.styleConfigMapping = {
      marker: _DEFAULT_MARKER_CONFIG,
      polygon: _DEFAULT_POLYGON_CONFIG,
      polyline: _DEFAULT_POLYLINE_CONFIG,
    };
    this.initStyleConfig(styleConfig);
  }

  /**
   * 初始化样式配置
   *
   * @param {Object} config 样式配置对象
   */
  initStyleConfig(config) {
    switch (this.type) {
      case "marker":
        this.styleConfig = config || _DEFAULT_MARKER_CONFIG;
        break;
      case "polygon":
        this.styleConfig = config || _DEFAULT_POLYGON_CONFIG;
        break;
      case "polyline":
        this.styleConfig = config || _DEFAULT_POLYLINE_CONFIG;
        break;
    }

    this.styleConfigMapping[this.type] = this.styleConfig;
  }

  /**
   * 更新图层类型
   * @param {string} type 图层类型
   */
  changeType(type) {
    if (!type) return;
    this.type = type;

    this.initStyleConfig(this.styleConfigMapping[type]);
  }

  /**
   * 更新样式配置
   * @param {Object} config 样式配置对象
   */
  changeStyleConfig(config) {
    this.styleConfig = merge(this.styleConfig, config);
    this.styleConfigMapping[this.type] = this.styleConfig;
  }

  /**
   * 获取指定ID的覆盖物配置
   * @param {string} id 覆盖物ID
   * @returns {Object} 覆盖物配置对象
   */
  getCoverCellConfig(id) {
    return this.coverCellsObj[id];
  }

  /**
   * 更新字段映射配置
   * @param {Array} resourceColumns 资源列配置数组
   */
  changeMappingConfig(resourceColumns) {
    if (resourceColumns && resourceColumns.length > 0) {
      this.mappingConfig = [];
      for (let i = 0; i < resourceColumns.length; i++) {
        const mapColumnName = resourceColumns[i].mapColumnName;
        if (mapColumnName && mapColumnName != "") {
          this.mappingConfig.push(resourceColumns[i]);
          this.mappingObj[resourceColumns[i].name] = mapColumnName;
        }
      }
    }
  }
}
