// 静态方法，用于处理字段映射配置，和字段、数据转换相关的统一使用这个方法
export default class Columns {
  // 配置
  columnConfig = [];

  /**
   * 通过元数据的列配置信息初始化列配置
   *
   * @param {Array} columns 元数据的列配置
   * @param {Array} columnConfig 当前字段映射的基础配置
   */
  setConfigByResourceColumns(columns, columnConfig) {
    let cfg = JSON.parse(JSON.stringify(columnConfig));
    cfg = cfg.map((i) => [i.type, i]);
    cfg = new Map(cfg);

    columns.map((i) => {
      const { columnType } = i;
      let config = cfg.get(columnType);

      if (!config) return i;

      if (+i.isPrimaryKey === 1) {
        // 自动主键
        config.mapColumnName = i.name;
        config.pkColumnId = i.columnId;
      } else {
        // 映射
        config.mapColumnName = i.name;
      }

      cfg.set(columnType, config);
    });

    const _columnConfig = [];
    cfg.forEach((i) => _columnConfig.push(i));
    this.columnConfig = _columnConfig;
  }

  /**
   * 通过配置初始化，直接覆盖 columnConfig
   *
   * @param {Array} config 配置
   */
  setConfigByConfig(config) {
    this.columnConfig = config;
  }

  /**
   * 获取当前的配置
   *
   * @returns 配置
   */
  getConfig() {
    return this.columnConfig;
  }

  /**
   * 判断传入的 type 是否是允许的类型
   *
   * @param {columnType|name|mapColumnName} type 类型
   * @returns 是否符合类型
   */
  _checkType(type) {
    return ["columnType", "name", "mapColumnName"].includes(type);
  }

  /**
   * 通过 type 更新 mapColumnName
   *
   * @param {columnType|name|mapColumnName} type 类型
   * @param {string} key type对应的值
   * @param {any} value 值
   * @returns {boolean} 是否更新成功
   */
  updateConfig(type, key, value) {
    if (this._checkType(type)) {
      const _columnConfigIndex = this.columnConfig.findIndex((i) => i[type] === key);
      if (_columnConfigIndex !== -1) {
        this.columnConfig[_columnConfigIndex].mapColumnName = value;
        return true;
      }
    }

    return false;
  }

  /**
   * 通过 type 查找配置
   *
   * @param {columnType|name|mapColumnName} type 类型
   * @param {string} key 类型对应的值
   * @returns 对应的配置对象，type错误或没有找到返回 null
   */
  getColumnConfig(type, key) {
    if (this._checkType(type)) {
      const _columnConfigIndex = this.columnConfig.findIndex((i) => i[type] === key);

      if (_columnConfigIndex !== -1) {
        return this.columnConfig[_columnConfigIndex];
      }
    }

    return null;
  }

  /**
   * 转换数据
   *
   * @param {object} data 需要转换的数据
   * @param {toOnline|toLocal} type 转换类型，只支持 toOnline 和 toLocal 两种枚举，传入其他值则默认为 toLocal
   * @returns {data: {}, rawData: {}} 返回一个对象，data 是转换后的数据，rawData是转换前的数据
   */
  translateData(data, type = "toOnline") {
    const _type = type === "toOnline" ? "name" : "mapColumnName";
    const _translatKeyName = type === "toOnline" ? "mapColumnName" : "name";

    let _data = {};
    for (let i in data) {
      const _config = this.getColumnConfig(_type, i);
      const _key = _config?.[_translatKeyName] || i;
      _data[_key] = data[i];
    }

    return { data: _data, rawData: data };
  }
}
