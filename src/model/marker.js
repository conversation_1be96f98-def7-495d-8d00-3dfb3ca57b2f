import Icon, { _DEFAULT_SVG_CONFIG } from "./icon";
import { uuid } from "@/utils/com-function";
import store from "@/store";
import { setSelectedCell } from "@/utils/map-utils";

export const _DEFAULT_MARKER_CONFIG = {
  iconType: "svg",
  svg: _DEFAULT_SVG_CONFIG,
};

export default class Marker {
  get type() {
    return "marker";
  }

  #MENU_CONFIG = {
    show: {
      title: "查看数据",
      id: "menu-detail",
      callBack: () => {
        this._rightClickSetCurrentLayer(this.layer);
        this.markerEdit(this.layer, true);
      },
    },
    detail: {
      title: "查看实体详情",
      id: "menu-detail",
      callBack: () => {
        this.layer.showEntityDetail(this.id);
      },
    },
    move: {
      title: "移动标记点",
      id: "menu-move",
      callBack: () => {
        store.dispatch("setting/controlEdit", { object: this, flag: true });
      },
    },
    edit: {
      title: "编辑数据",
      id: "menu-edit",
      callBack: () => {
        this._rightClickSetCurrentLayer(this.layer);
        this.markerEdit(this.layer, true);
      },
    },
    style: {
      title: "设置样式",
      id: "menu-style",
      callBack: () => {
        // 设置选中
        setSelectedCell(this);
        store.commit("setting/setShowConifgModal", true);
        store.commit("setCurrentLayer", this.layer);
      },
    },
    delete: {
      title: "删除标记点",
      id: "menu-del",
      callBack: () => {
        this._rightClickSetCurrentLayer(this.layer);
        this.deleteMarker(this.id, this.layer);
      },
    },
  };

  constructor(layer, param) {
    this.id = param.id || uuid();
    this.name = param.name === "" || param.name ? param.name : "未命名";

    this.icon = this.createMarkerIcon(param.pointConfig);
    const { icon } = this.icon;

    this.point = new BMap.Point(param.point.lng, param.point.lat);
    this.marker = new BMap.Marker(this.point, { ...param.options, icon });

    this.label = null;

    // 初始化
    this.setMarkerLabel(this.name);
    this.addLeftClickEvent(this.marker);

    this.extData = param.extData || {};
    this.data = param.data || {};

    this.createContextMenu(layer);

    this.mapFieldNames = layer.getMapFieldNames();
    this._edit = false;
    this.layer = layer;
  }

  show() {
    this.marker.show();
    this.label.show();
  }

  hide() {
    this.marker.hide();
    this.label.hide();
  }

  createMarkerIcon(config) {
    const { iconType } = config || {};
    const _config = config[iconType];
    return new Icon(iconType, config ? _config : null);
  }

  getLabelWidth(text) {
    const name = text || text === "" ? text : this.label.content;
    const d = document.createElement("text");
    d.innerText = name;
    d.style.fontSize = 12;
    d.style.visibility = "hidden";
    document.body.appendChild(d);
    const len = d.offsetWidth;
    document.body.removeChild(d);
    return len;
  }

  setMarkerLabel(name) {
    const { size } = this.icon?.icon || {};
    const { width = 0, height = 32 } = size || {};

    const len = this.getLabelWidth(name);

    const offsetLeft = width / 2 - len / 2;
    let offsetTop = height;

    // 设置文本标签
    this.label = new BMap.Label(name);
    this.label.length = len;
    this.label.setStyle({
      color: "rgb(51 51 51)",
      border: "1px solid #e8e8e8",
      padding: "2px 6px",
      fontSize: "12px",
      font: "inherit",
      opacity: name ? 1 : 0,
    });
    this.label.setOffset(new BMap.Size(offsetLeft, offsetTop));

    this.marker.setLabel(this.label);
  }

  // 更新 label
  updateLabel(config) {
    const { name, offset, offsetX, offsetY, opacity } = config;

    if (name) {
      this.name = name;
      this.label.setContent(name);
    }

    if (offsetX || offsetY) {
      let offset = this.label.getOffset();
      if (offsetX) {
        const size = new BMap.Size(offsetX, offset.height);
        this.label.setOffset(size);
      }
      if (offsetY) {
        const size = new BMap.Size(offset.width, offsetY);
        this.label.setOffset(size);
      }
    }

    if (offset) this.label.setOffset(offset);

    if (opacity || opacity === 0) {
      // 拦截一下
      const flag = name || this.name;

      this.label.setStyle({
        opacity: flag ? opacity : 0,
      });
    }

    this.marker.setLabel(this.label);
  }

  // 创建右键事件
  createContextMenu(layer) {
    if (this._contentMenu) return;

    const pageMode = store.getters["setting/pageMode"];

    // const contextMenuData =
    //   pageMode === "view"
    //     ? [this.#MENU_CONFIG.show]
    //     : [
    //         this.#MENU_CONFIG.move,
    //         this.#MENU_CONFIG.edit,
    //         this.#MENU_CONFIG.style,
    //         this.#MENU_CONFIG.delete,
    //       ];
    const contextMenuData = [];

    // if (!layer.isStatic && pageMode !== "view") {
    //   contextMenuData.unshift(this.#MENU_CONFIG.detail);
    // }

    let contextMenu = new BMap.ContextMenu();
    contextMenuData.map((menu) => {
      contextMenu.addItem(new BMap.MenuItem(menu.title, menu.callBack, { id: menu.id }));
    });
    this._contentMenu = contextMenu;
    this.marker.addContextMenu(contextMenu);
  }

  // 开启移动
  openPointMove(layer) {
    this._rightClickSetCurrentLayer(layer);
    this.marker.enableDragging();
    this.addMarkerEvent(layer);
  }

  removeRightClickEvent() {
    this._contentMenu && this.marker.removeContextMenu(this._contentMenu);
    this._contentMenu = null;
  }

  _rightClickSetCurrentLayer(layer) {
    const { currentLayer } = store.getters;
    if (currentLayer.id !== layer.id) {
      store.commit("setCurrentLayer", layer);
    }
  }

  // 左键点击 Maker 事件
  addLeftClickEvent(marker) {
    marker.addEventListener("click", (e) => {
      // 阻止冒泡
      e.domEvent.stopPropagation();

      // 移动点
      if (this.layer.isEditMode) {
        // 切换点
        store.dispatch("setting/controlEdit", { object: this, flag: true });
      } else {
        // 编辑数据
        const obj = {
          id: this.id,
          extData: this.extData,
          config: {
            x: e.clientX,
            y: e.clientY,
          },
        };
        this.layer.openEdit(true, obj);
      }
    });
  }

  // 编辑事件
  async _editConfirm(layer) {
    this.marker.disableDragging();

    const point = this.marker.getPosition();
    this.point = point;

    this.markerEdit(layer, false);

    //数据转化
    let mapFieldNames = this.mapFieldNames; // layer.markers[0].mapFieldNames; //获取字段映射关系

    let data = this.extData;
    // eslint-disable-next-line no-prototype-builtins
    if (this.extData.hasOwnProperty(mapFieldNames.lat)) {
      data[mapFieldNames.lat] = this.point.lat;
    }

    // eslint-disable-next-line no-prototype-builtins
    if (this.extData.hasOwnProperty(mapFieldNames.lng)) {
      data[mapFieldNames.lng] = this.point.lng;
    }

    layer.isStatic
      ? layer.staticStore.update(data)
      : await this.layer.resourceStore.dispatch("data/updateData", { values: [data] });
  }

  // 编辑取消事件
  _editCancel() {
    return new Promise((resolve) => {
      this.marker.setPosition(this.point);
      this.marker.disableDragging();
      resolve();
    });
  }

  addMarkerEvent() {
    // store && store.dispatch("setting/controlEdit", { flag: true, object: this });
    this._edit = true;
  }

  //删除标记点
  async deleteMarker(id, layer) {
    layer.isStatic
      ? layer.staticStore.del(id)
      : await layer.resourceStore.dispatch("data/delData", { primaryIds: [id] });

    this.delete(id, layer);
  }

  /**
   * 打开编辑弹窗
   * @param {Boolean} isOpen 是否打开弹窗
   * @param {Object} obj 覆盖物
   */
  markerEdit(layer, isOpen) {
    let obj = {
      id: this.id,
      extData: this.extData,
    };
    layer.openEdit(isOpen, obj);
  }

  // 居中label
  setLabelCenter() {
    const { size } = this.icon?.icon || {};
    if (!size) return;

    const { width, height } = size;
    this.updateLabel({
      offset: new BMap.Size(width / 2 - this.label.length / 2, height),
    });
  }

  // 更新标记 Icon
  updateMarkerIcon(config) {
    const icon = this.icon.updateIcon(config);
    this.marker.setIcon(icon);
    this.setLabelCenter();
  }

  // 切换当前标记的类型
  changeIconType(type, config) {
    const icon = new Icon(type, config);
    this.icon = icon;

    this.setLabelCenter();
    this.marker.setIcon(icon.icon);
  }

  // 更新数据
  updateData(data) {
    this.extData = Object.assign(this.extData, data.rawData || data);
    this.data = data;

    const { name: label, lng, lat } = data;

    this.name = label || "未命名";

    this.label.setContent(label); //修改名称
    const width = this.getLabelWidth();
    this.label.length = width;

    this.updateLabel({
      opacity: label ? 1 : 0,
    });

    let point = new BMap.Point(lng, lat);
    this.marker.setPosition(point); //修改点坐标

    this.point = point;

    this.setLabelCenter();
  }

  // 删除当前
  delete(id, layer) {
    layer.delOverlay("marker", id);
    layer.Map.removeOverlay(this.marker);
    layer.stack.delete(this.id);
  }

  // 导出配置
  exportConfig() {
    return {
      id: this.id,
      name: this.name,
      point: JSON.parse(JSON.stringify(this.point)),
      icon: this.icon.exportConfig(),
    };
  }
}
