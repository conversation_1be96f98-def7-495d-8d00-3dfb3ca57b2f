const _MARKER_CONFIG = {
  displayName: "静态图层-点",
  type: "marker",
  columns: [
    {
      name: "id",
      displayName: "主键",
      mapColumnName: "id",
      columnType: "idColumn",
    },
    {
      name: "name",
      displayName: "名称",
      mapColumnName: "name",
      columnType: "nameColumn",
    },
    {
      name: "lng",
      displayName: "经度",
      columnType: "longitudeColumn",
      mapColumnName: "lng",
    },
    {
      name: "lat",
      displayName: "纬度",
      columnType: "latitudeColumn",
      mapColumnName: "lat",
    },
  ],
};

const _POLYGON_CONFIG = {
  displayName: "静态图层-面",
  type: "polygon",
  columns: [
    {
      name: "id",
      displayName: "主键",
      mapColumnName: "id",
      columnType: "idColumn",
    },
    {
      name: "name",
      displayName: "名称",
      mapColumnName: "name",
      columnType: "nameColumn",
    },
    {
      name: "pointList",
      displayName: "点集",
      mapColumnName: "pointList",
      columnType: "pointListColumn",
    },
    {
      name: "centerPoint",
      displayName: "中心点",
      mapColumnName: "centerPoint",
      columnType: "centerPointColumn",
    },
  ],
};

const _POLYLINE_CONFIG = {
  displayName: "静态图层-线",
  type: "polyline",
  columns: [
    {
      name: "id",
      displayName: "主键",
      mapColumnName: "id",
      columnType: "idColumn",
    },
    {
      name: "name",
      displayName: "名称",
      mapColumnName: "name",
      columnType: "nameColumn",
    },
    {
      name: "pointList",
      displayName: "点集",
      mapColumnName: "pointList",
      columnType: "pointListColumn",
    },
  ],
};

export const _TYPE_ENUMERATION = {
  marker: _MARKER_CONFIG,
  polygon: _POLYGON_CONFIG,
  polyline: _POLYLINE_CONFIG,
};

export default class StaticData {
  mockData = {};
  localData = new Map();

  constructor(opts = {}) {
    this.type = opts.type || "marker";
    this.init(opts.type);
  }

  init(type = "marker") {
    this.mockData = _TYPE_ENUMERATION[type];
  }

  initLocalData(data) {
    if (Array.isArray(data)) {
      this.localData = new Map(data.map((i) => [i.id, i]));
    }
  }

  add(data) {
    const { id } = data || {};
    if (!id) return;

    this.localData.set(id, data);
  }

  del(ids) {
    if (!ids) return;
    if (Array.isArray(ids)) {
      ids.map((i) => this.localData.delete(i));
    } else if (typeof ids === "string" || typeof ids === "number") {
      this.localData.delete(ids);
    }
  }

  update(data) {
    const { id } = data || {};
    if (!id) return;
    this.localData.set(id, data);
  }

  query(ids) {
    if (Array.isArray(ids)) {
      return ids.map((i) => this.localData.get(i));
    } else if (typeof ids === "string" || typeof ids === "number") {
      return [this.localData.get(ids)];
    } else return [];
  }

  getAllLocalData() {
    let res = [];
    this.localData.forEach((i) => {
      res.push(i);
    });
    return res;
  }
}
