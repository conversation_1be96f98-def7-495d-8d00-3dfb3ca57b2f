import { uuid } from "@/utils/com-function";
import _cloneDeep from "lodash/cloneDeep";

export const _DEFAULT_SVG_CONFIG = {
  type: "svg",
  iconType: "svg",
  svgId: "local",
  svgPath: `<svg t="1660786767685" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2390" width="200" height="200"><path d="M512 938.666667c-53.333333 0-384-257.258667-384-469.333334S299.925333 85.333333 512 85.333333s384 171.925333 384 384-330.666667 469.333333-384 469.333334z m0-352c64.8 0 117.333333-52.533333 117.333333-117.333334s-52.533333-117.333333-117.333333-117.333333-117.333333 52.533333-117.333333 117.333333 52.533333 117.333333 117.333333 117.333334z" p-id="2391"></path></svg>`,
  scale: 30,
  fillOpacity: 1,
  strokeWeight: 0,
  strokeOpacity: 1,
  fillColor: "#12345e",
  strokeColor: "#000000",
  anchor: new BMap.Size(470, 944),
};

export const _DEFAULT_IMAGE_CONFIG = {
  type: "image",
  imageUrl: `${process.env.BASE_URL}static/icons/local.png`,
  imageSize: {
    width: 32,
    height: 32,
  },
  anchorSize: {
    width: 32,
    height: 32,
  },
  visualWindowSize: {
    width: 32,
    height: 32,
  },
};

export default class Icon {
  constructor(type, config) {
    type = type || "svg";

    this._type = type;
    this._config = config;
    this._id = config?.id || uuid();
    this._initIcon(type, config);
  }

  // 图片类型
  #_DEFAULT_IMAGE_CONFIG = _cloneDeep(_DEFAULT_IMAGE_CONFIG);
  #_DEFAULT_UPDATE_IMAGE_FUN_CONFIG = {
    imageUrl: { funKey: "setImageUrl" },
    size: { funKey: "setSize", query: "imageSize" },
    width: { funKey: "setSize", query: "imageSize", type: "width" },
    height: { funKey: "setSize", query: "imageSize", type: "height" },
    imageOffset: { funKey: "setImageOffset", query: "imageOffset" },
    imageOffsetX: { funKey: "setImageOffset", query: "imageOffset", type: "width" },
    imageOffsetY: { funKey: "setImageOffset", query: "imageOffset", type: "height" },
    anchor: { funKey: "setAnchor", query: "anchor" },
    anchorX: { funKey: "setAnchor", query: "anchor", type: "width" },
    anchorY: { funKey: "setAnchor", query: "anchor", type: "height" },
  };

  // SVG 类型
  #_DEFAULT_SVG_CONFIG = _cloneDeep({
    ..._DEFAULT_SVG_CONFIG,
    scale: _DEFAULT_SVG_CONFIG.scale / 1000,
  });
  #_DEFAULT_UPDATE_SVG_FUN_CONFIG = {
    svgPath: "setPath",
    anchor: "setAnchor",
    rotation: "setRotation",
    scale: "setScale",
    strokeWeight: "setStrokeWeight",
    strokeColor: "setStrokeColor",
    strokeOpacity: "setStrokeOpacity",
    fillOpacity: "setFillOpacity",
    fillColor: "setFillColor",
  };

  _initIcon(type, config) {
    if (type === "image") return this._initImageIcon(config);
    return this._initSvgIcon(config);
  }

  _BmapSize({ width = 0, height = 0 }) {
    return new BMap.Size(width, height);
  }

  _initImageIcon(config) {
    try {
      config = config || this.#_DEFAULT_IMAGE_CONFIG;
      this._config = config;

      let { imageUrl, imageSize, anchorSize, visualWindowSize } = config;
      const _imageSize = this._BmapSize(imageSize);
      const anchor = this._BmapSize(anchorSize);
      const size = this._BmapSize(visualWindowSize);

      const icon = new BMap.Icon(imageUrl, _imageSize);
      icon.setAnchor(anchor);
      icon.setSize(size);
      icon.setImageSize(_imageSize);

      this.icon = icon;

      return icon;
    } catch (error) {
      throw Error(`初始化图片类型icon时出错: ${error}`);
    }
  }

  _initSvgIcon(config) {
    try {
      config = config || this.#_DEFAULT_SVG_CONFIG;
      this._config = config;

      const { svgPath = BMap.BMap_Symbol_SHAPE_CIRCLE, ..._config } = config;
      const { scale } = _config;
      if (scale && scale > 1) _config.scale = scale / 1000;
      const icon = new BMap.Symbol(svgPath, _config);
      this.icon = icon;

      return icon;
    } catch (error) {
      throw Error(`初始化 SVG 类型 icon 时出错: ${error}`);
    }
  }

  // 返回的是一个 BMap 的 ICON 类
  updateIcon(config) {
    if (this._type === "image") return this._updateImageIcon(config);
    return this._updateSvgIcon(config);
  }

  _updateImageIcon(config) {
    let icon = this.icon;

    for (let i in config) {
      const temp = this.#_DEFAULT_UPDATE_IMAGE_FUN_CONFIG[i];

      if (temp && config[i]) {
        const { funKey, query, type } = temp;
        if (i === "imageUrl") {
          icon[funKey](config[i]);
          this._config[i] = config[i];
        } else {
          const { width, height } = icon[query];
          const _width = type === "width" ? config[i] : width;
          const _height = type === "height" ? config[i] : height;

          const tempSize = new BMap.Size(_width, _height);

          if (funKey === "setSize") {
            icon.setSize(tempSize);
            icon.setImageSize(tempSize);
            icon.setAnchor(new BMap.Size(_width / 2, _height));
          } else {
            icon[funKey](tempSize);
          }
        }
      }
    }

    this.icon = icon;
    return icon;
  }

  _updateSvgIcon(config) {
    let icon = this.icon;

    for (let i in config) {
      const funKey = this.#_DEFAULT_UPDATE_SVG_FUN_CONFIG[i];
      if (funKey && config[i]) {
        icon[funKey](config[i]);
        this._config[i] = config[i];
      }
    }

    this.icon = icon;
    return icon;
  }

  // 获取图片类型默认配置
  getImageDefaultConfig() {
    return this.#_DEFAULT_IMAGE_CONFIG;
  }

  // 获取SVG类型默认配置
  getSvgDefaultConfig() {
    return this.#_DEFAULT_SVG_CONFIG;
  }

  // 导出配置
  exportConfig() {
    return {
      iconType: this._type,
      svg: this._config,
    };
  }
}
