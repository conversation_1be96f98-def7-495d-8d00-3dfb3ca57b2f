import { merge, debounce } from "lodash-es";
import eventBus, { eventMap } from "@/utils/event-bus";
import { getMapConfigByUrl } from "@/components/map-select/utils";

/**
 * 省份多边形类
 * 用于在地图上绘制和管理省份边界多边形
 */
export default class ProvincePolygon {
  // 默认省份
  province = "";

  // 默认样式配置
  style = {
    strokeColor: "#1890ff", // 边框颜色
    fillColor: "#bae7ff", // 填充颜色
    strokeOpacity: 0.8, // 边框透明度
    fillOpacity: 0.3, // 填充透明度
    strokeWeight: 1, // 边框宽度
    strokeStyle: "solid", // 边框样式
  };

  // 高亮样式
  hightLightStyle = {
    strokeColor: "#1890ff", // 边框颜色
    fillColor: "#40a9ff", // 填充颜色
    strokeOpacity: 1, // 边框透明度
    fillOpacity: 0.1, // 填充透明度
    strokeWeight: 2, // 边框宽度
    strokeStyle: "solid", // 边框样式
  };

  // 文字标签样式
  labelStyle = {
    color: "#000", // 文字颜色
    fontSize: "12px", // 文字大小
    textStrokeColor: undefined, // 文字描边颜色
    border: "0", // 边框
  };

  // 样式属性与对应的设置方法映射
  styleMap = {
    strokeColor: "setStrokeColor", // 设置边框颜色
    fillColor: "setFillColor", // 设置填充颜色
    strokeOpacity: "setStrokeOpacity", // 设置边框透明度
    fillOpacity: "setFillOpacity", // 设置填充透明度
    strokeWeight: "setStrokeWeight", // 设置边框宽度
    strokeStyle: "setStrokeStyle", // 设置边框样式
  };

  // 文字标签是否显示
  labelVisible = false;

  constructor(map, options) {
    this.bdary = new BMap.Boundary(); // 百度地图行政区域服务
    this.label = new BMap.Label(); // 文字标签
    this.markPolygons = []; // 存储省份边界多边形
    this.map = map; // 地图实例

    this.style = options?.style || this.style;
    this.hightLightStyle = options?.hightLightStyle || this.hightLightStyle;
    this.labelStyle = options?.labelStyle || this.labelStyle;
    this.labelVisible = options?.labelVisible || this.labelVisible;
    this.province = options?.province || this.province;
    // 当前是否高亮
    this.isHightLight = false;

    this._initLabel();
    this.province && this.setProvince(this.province);
  }

  /**
   * 初始化文字标签
   */
  _initLabel() {
    this.changeLabelStyle(this.labelStyle);
    this.label.hide();
    this.label.disableMassClear();
    this.map.addOverlay(this.label);
  }

  /**
   * 初始化多边形事件
   * @param {BMap.Polygon} ply 多边形对象
   */
  _initPolygonEvent(ply) {
    ply.addEventListener("click", this.hightLight.bind(this));
  }

  /**
   * 移除多边形事件
   * @param {BMap.Polygon} ply 多边形对象
   */
  _removePolygonEvent(ply) {
    ply.removeEventListener("click", this.hightLight.bind(this));
  }

  /**
   * 获取边界数据
   * @param {string} name 名称
   */
  async _getBoundary(name) {
    return new Promise((resolve) => {
      // 获取省份边界数据并绘制
      this.bdary.get(name, (rs) => {
        const count = rs.boundaries.length;
        for (let i = 0; i < count; i++) {
          // 创建多边形对象
          const ply = new BMap.Polygon(rs.boundaries[i], this.style);
          ply.disableMassClear();
          this.map.addOverlay(ply);
          this.markPolygons.push(ply);
          this._initPolygonEvent(ply);
        }
        resolve();
      });
    });
  }

  /**
   * 设置省份边界
   * @param {string} province 省份名称
   */
  async setProvince(
    province,
    options = {
      autoViewport: false,
    },
  ) {
    this.province = province;
    eventBus.emit(eventMap.startSetProvince);
    // 清除已有的多边形
    if (this.markPolygons) {
      this.markPolygons.forEach((ply) => {
        this.map.removeOverlay(ply);
        this._removePolygonEvent(ply);
      });
      this.markPolygons = [];
    }
    const config = await getMapConfigByUrl(province);
    if (config) {
      const regionsPromises = config.features.map((i) => this._getBoundary(i.properties.name));
      await Promise.all(regionsPromises);
    }

    // 获取省份边界数据
    this.bdary.get(province, (rs) => {
      const count = rs.boundaries.length;
      let pointArray = [];
      for (let i = 0; i < count; i++) {
        // 创建多边形对象
        const ply = new BMap.Polygon(rs.boundaries[i], this.style);
        pointArray = pointArray.concat(ply.getPath());
      }

      const viewport = this.map.getViewport(pointArray);
      this._setLabel(viewport, province);

      options.autoViewport && this.map.setViewport(pointArray); // 调整视野，使其只展示目标区域
      eventBus.emit(eventMap.endSetProvince);
    });
  }

  /**
   * 获取样式设置方法名
   * @param {string} key 样式属性名
   * @returns {string} 对应的设置方法名
   */
  getStyleFun(key) {
    return this.styleMap[key];
  }

  /**
   * 触发配置变化事件
   */
  _triggerConfigChange() {
    eventBus.emit(eventMap.provincePolygonConfigChange, {
      ...this.exportConfig(),
      isHightLight: this.isHightLight,
    });
  }

  /**
   * 应用样式到多边形
   * @param {Object} style 要应用的样式对象
   * @private
   */
  _applyStyle(style) {
    Object.keys(style).forEach((key) => {
      this.markPolygons.forEach((ply) => {
        const method = this.getStyleFun(key);
        if (method) {
          ply[method](style[key]);
        }
      });
    });
  }

  /**
   * 更新样式状态
   * @param {Object} style 新样式
   * @param {Object} targetStyle 目标样式对象
   * @private
   */
  _updateStyleState(style, targetStyle) {
    Object.keys(style).forEach((key) => {
      if (this.styleMap[key]) {
        targetStyle[key] = style[key];
      }
    });
  }

  /**
   * 更新多边形样式的基础方法
   * @param {Object} style 样式配置对象
   * @param {Object} targetStyle 目标样式对象
   * @param {boolean} condition 更新条件
   * @private
   */
  _updateStyle(style, targetStyle, condition) {
    this._updateStyleState(style, targetStyle);
    if (condition) {
      this._applyStyle(style);
    }
    this._triggerConfigChange();
  }

  /**
   * 更改多边形普通样式
   * @param {Object} style 样式配置对象
   */
  changeStyle = debounce((style) => {
    this._updateStyle(style, this.style, !this.isHightLight);
  }, 300);

  /**
   * 修改高亮样式
   * @param {Object} style 样式配置对象
   */
  changeHightLightStyle = debounce((style) => {
    this._updateStyle(style, this.hightLightStyle, this.isHightLight);
  }, 300);

  /**
   * 切换高亮状态
   * @param {boolean} isHightLight 是否高亮
   * @private
   */
  _toggleHightLight(isHightLight) {
    this.isHightLight = isHightLight;
    const targetStyle = isHightLight ? this.hightLightStyle : this.style;
    this._applyStyle(targetStyle);
    this._triggerConfigChange();
  }

  /**
   * 高亮当前省份
   */
  hightLight(ev) {
    ev.domEvent.stopPropagation();
    ev.domEvent.preventDefault();
    this._toggleHightLight(true);
  }

  /**
   * 取消高亮
   */
  cancelHightLight(ev) {
    ev.domEvent.stopPropagation();
    ev.domEvent.preventDefault();
    this._toggleHightLight(false);
  }

  /**
   * 设置文字标签
   * @param {Object} viewport 视口对象
   * @param {string} label 文字标签内容
   */
  _setLabel(viewport, label) {
    this.label.setContent(label);
    this.label.setPosition(viewport.center);
    this.label.setStyle(this.labelStyle);
    this.label.setZIndex(1000);
    this.label.show();
  }

  /**
   * 更改文字标签样式
   * @param {Object} style 样式配置对象
   */
  changeLabelStyle = debounce((style) => {
    this.labelStyle = merge(this.labelStyle, style);
    let _style = { ...this.labelStyle };
    _style.fontSize = `${_style.fontSize}px`;
    if (_style.textStrokeColor) {
      _style["-webkit-text-stroke"] = `1px ${_style.textStrokeColor}`;
      delete _style.textStrokeColor;
    }
    this.label.setStyle(_style);
    this._triggerConfigChange();
  }, 300);

  /**
   * 更改文字标签显示状态
   * @param {boolean} visible 是否显示
   */
  changeLabelVisible(visible) {
    this.labelVisible = visible;
    visible ? this.label.show() : this.label.hide();
    this._triggerConfigChange();
  }

  /**
   * 导出配置
   * @returns {Object} 配置对象
   */
  exportConfig() {
    return {
      province: this.province,
      labelVisible: this.labelVisible,
      style: this.style,
      labelStyle: this.labelStyle,
      hightLightStyle: this.hightLightStyle,
    };
  }

  /**
   * 清楚标记的省
   */
  clearMarkProvince() {
    this.markPolygons.forEach((ply) => {
      this.map.removeOverlay(ply);
      this._removePolygonEvent(ply);
    });
    this.markPolygons = [];
    this.label.hide();
  }
}
