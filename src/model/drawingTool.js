export default class DrawingTool {
  constructor(map, options = {}) {
    this._map = map;
    this._drawingManager = null;

    this.styleOptions = {
      strokeColor: "#096dd9", //边线颜色。
      fillColor: "#40a9ff7d", //填充颜色。当参数为空时，圆形将没有填充效果。
      strokeWeight: 2, //边线的宽度，以像素为单位。
      strokeOpacity: 0.8, //边线透明度，取值范围0 - 1。
      fillOpacity: 0.6, //填充的透明度，取值范围0 - 1。
      strokeStyle: "solid", //边线的样式，solid或dashed。
    };

    this.polygons = options.polygons || { pointList: [] };
    this.polylines = options.polylines || {};
    this.circles = options.circles || {};
    this.rectangles = options.rectangles || {};

    this.markers = options.markers || {};

    this.drawCallback = options.drawCallback;

    this.drawType = options.drawType || "marker";
    this.init();
  }

  init() {
    // eslint-disable-next-line no-undef
    this._drawingManager = new BMapLib.DrawingManager(this._map, {
      isOpen: false, //是否开启绘制模式
      enableDrawingTool: false, //是否显示工具栏
      // enableCalculate: true, //输出 面积 单位 米
      drawingToolOptions: {
        // eslint-disable-next-line no-undef
        anchor: BMAP_ANCHOR_TOP_RIGHT, //位置
        // eslint-disable-next-line no-undef
        offset: new BMap.Size(5, 5),
      },
      circleOptions: this.styleOptions, // 圆的样式
      polylineOptions: this.styleOptions, // 线的样式
      polygonOptions: this.styleOptions, // 多边形的样式
      rectangleOptions: this.styleOptions, // 矩形的样式
    });
    this._drawingManager &&
      this._drawingManager.addEventListener("overlaycomplete", (n, e) => {
        this.drawOverlayComplete(e);
      });
  }

  drawOverlayComplete(e) {
    if (!e.drawingMode) return;
    //立即清楚鼠标绘制的东西
    this._map && this._map.removeOverlay(e.overlay);
    this.emptyOverlay();
    const overlay = e.overlay;
    const drawPolygonTypeList = ["polygon", "polyline", "circle", "rectangle"];
    if (drawPolygonTypeList.includes(e.drawingMode)) {
      const overLayCenterPoint = this.getOverLayCenterPoint(e.overlay.getPath());
      const polygonOverlay = {
        pointList: overlay.getPath().map((p) => [p.lng, p.lat]),
        calculate: e.calculate,
        editing: false,
        centerPoint: overLayCenterPoint,
      };
      switch (e.drawingMode) {
        case "polygon":
          this.polygons = polygonOverlay;
          this.drawType = "polygon";
          break;
        case "polyline":
          this.polylines = polygonOverlay;
          this.drawType = "polyline";
          break;
        case "circle":
          this.circles = polygonOverlay;
          this.drawType = "circle";
          break;
        case "rectangle":
          this.rectangles = polygonOverlay;
          this.drawType = "rectangle";
          break;
        default:
          break;
      }
    } else {
      const { lat, lng } = overlay.point;
      this.markers = { point: { lat, lng } };
      this.drawType = "marker";
    }

    this.drawCallback(this.drawType, {
      marker: this.markers,
      polygon: this.polygons,
    });
  }

  getOverLayCenterPoint(path) {
    let x = 0.0;
    let y = 0.0;
    for (let i = 0; i < path.length; i++) {
      x = x + parseFloat(path[i].lng);
      y = y + parseFloat(path[i].lat);
    }
    x = x / path.length;
    y = y / path.length;
    return {
      lng: x,
      lat: y,
    };
  }

  emptyOverlay() {
    this.polygons = {};
    this.polylines = {};
    this.circles = {};
    this.rectangles = {};

    this.markers = {};
  }
}
