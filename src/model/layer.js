import Stack from "./stack.js";
import { toColumnObj } from "@/utils/utils";
import { randomGetid, uuid } from "@/utils/com-function";
import Marker, { _DEFAULT_MARKER_CONFIG } from "./marker";
import Polygon, { _DEFAULT_POLYGON_CONFIG } from "./polygon";
import Polyline, { _DEFAULT_POLYLINE_CONFIG } from "./Polyline";
import store from "@/store/index.js";
import LayerConfig from "./LayerConfig.js";
import { createStore } from "@daas/surface-web-lib/resource";
import { cloneDeep, merge } from "lodash-es";
import StaticData from "./StaticData.js";
import Columns from "./Columns.js";

// 字段映射列定义
export const MarkerColumns = [
  { name: "name", displayName: "名称", configure: true },
  { name: "lng", displayName: "经度", configure: true },
  { name: "lat", displayName: "纬度", configure: true },
];
export const PolygonColumns = [
  { name: "pointList", displayName: "点集", configure: true },
  { name: "centerPoint", displayName: "中心点", configure: true },
];

const DefaultColumns = [
  { name: "id", displayName: "主键", configure: true },
  ...MarkerColumns,
  ...PolygonColumns,
  { name: "time", displayName: "时间", configure: true },
];

// 缓存
let _CACHE = {};

export const ColRelation = new Map([
  ["idColumn", { column: "id", required: true }],
  ["nameColumn", { column: "name", required: true }],
  ["longitudeColumn", { column: "lng", required: true }],
  ["latitudeColumn", { column: "lat", required: true }],
  ["pointListColumn", { column: "pointList", required: true }],
  ["centerPointColumn", { column: "centerPoint", required: true }],
  ["timeColumn", { column: "time", required: false }],
  ["startColumn", { column: "startPoint", required: true }],
  ["endColumn", { column: "endPoint", required: true }],
]);

/**
 * 地图图层对象
 */
export default class Layer {
  constructor(
    Map,
    {
      id = randomGetid(),
      hide = false,
      isSelected = false,
      rightAction = true,
      markers = [],
      polygons = [],
      polylines = [],
      isStatic = true,
      showEntityDetailCallBack = null,
    },
  ) {
    this.id = id;
    this.Map = Map["_map"]; // 百度地图的实例
    this._map = Map; // 包装过的地图实例
    this.hide = hide; // 图层显示隐藏
    this.isSelected = isSelected; //被选中
    this.rightAction = rightAction; //右键操作显示
    this.status = false; //当前配置无法展示图层
    this.stack = null; //记录操作

    // 覆盖物
    this.markers = markers;
    this.polygons = polygons;
    this.polylines = polylines;

    this.resourceStore = createStore();
    this.layerConfig = {};

    // 列配置
    this.columns = new Columns();

    this.isStatic = isStatic;
    this.staticStore = null;

    this.numberOfTags = 0;

    this.showEntityDetailCallBack = showEntityDetailCallBack;
    this.hasTimeRange = false;
  }

  get type() {
    // 节点类型， "polygon", "marker"
    return this.layerConfig.type;
  }

  get resourceColumns() {
    return this.columns.columnConfig;
  }

  get markerConfig() {
    return this.layerConfig.styleConfig;
  }

  set markerConfig(config) {
    this.layerConfig.changeStyleConfig(config);
  }

  get polygonConfig() {
    return this.layerConfig.styleConfig;
  }

  set polygonConfig(config) {
    this.layerConfig.changeStyleConfig(config);
  }

  /**
   * 根据图层配置初始化图层
   * @param {图层配置} layerConfig
   */
  async init(layerConfig) {
    this.layerConfig = new LayerConfig(layerConfig);
    // 初始化图形样式配置
    // if (layerConfig.type) {
    //   const { styleConfig } = this.layerConfig;
    //   layerConfig.type === "marker"
    //     ? this.initMarkerConifg(styleConfig)
    //     : this.initPolygonConfig(styleConfig);
    // }

    this.isStatic
      ? this._initStatic(layerConfig)
      : await this._initResource(layerConfig.resourceId);
  }

  /**
   * 初始化静态数据
   * @param {Object} layerConfig 图层配置
   */
  _initStatic(layerConfig) {
    let _staticData = new StaticData({ type: layerConfig.type });

    const { localData = [], mappingConfig } = layerConfig;

    _staticData.initLocalData(localData);

    let format = true;
    if (mappingConfig) {
      format = false;
      _staticData.mockData.columns = mappingConfig;
    }

    const _mockResourceInfo = _staticData.mockData;
    this.resourceStore.commit("setResourceInfo", _mockResourceInfo);

    this.staticStore = _staticData;

    this._formatColumns(format);
    this.setStack(_mockResourceInfo.columns);
    this._setLayerType();
  }

  /**
   * 根据资源编码，初始化图层
   * @param {资源编码} resourceId
   */
  async _initResource(resourceId) {
    if (resourceId && resourceId != "") {
      const resourceInfo = await this.resourceStore.dispatch("getResourceInfo", {
        resourceId: resourceId,
      });
      this.resourceStore.commit("setResourceInfo", resourceInfo);
      this._formatParams();
      this._formatColumns();
      this.setStack(resourceInfo.columns);
      this._setLayerType();
      this._initDisplayName(resourceInfo);
    }
  }

  _initDisplayName(resourceInfo) {
    this.layerConfig.displayName = resourceInfo.resourceName;
  }

  /**
   * 初始化数据获取参数
   */
  _formatParams() {
    const modularType = store.getters["modularType"];
    const modularId = store.getters["modularId"];
    if (modularId && modularId != "") {
      this.resourceStore.commit("data/setExtendAuthParams", {
        modularType: modularType,
        modularId: modularId,
      });
    }
    this.resourceStore.commit("data/setPageParam", { pageIndex: 1, limit: 100 });
  }

  /**
   * 解析字段
   */
  _formatColumns(format = true) {
    if (format) {
      this._format(this.getResourceInfo().columns); // 配置了字段含义，属于ColRelation地图视图的这些字段
    }

    const cols = toColumnObj(this.layerConfig.mappingConfig);
    const _resourceColumns = DefaultColumns.map((r) => {
      if (cols[r.name]) return Object.assign(cols[r.name], r);
      else return r;
    });
    // 存储配置
    this.columns.setConfigByConfig(_resourceColumns);
  }

  _format(columns = []) {
    columns.map((item) => {
      let hased = false;
      if (this.layerConfig && this.layerConfig.mappingConfig) {
        const mapColumn = this.layerConfig.mappingConfig.filter((mapper) => {
          return mapper.mapColumnName == item.name;
        });
        if (mapColumn && mapColumn.length > 0) {
          mapColumn[0].displayName = item.displayName || mapColumn[0].displayName || item.name; //对应数据资源的列名
          mapColumn[0].columnType = item.columnType || mapColumn[0].columnType;
          hased = true;
        }
      }
      if (!hased) {
        if (item.isPrimaryKey == "1" && (!item.columnType || item.columnType == "")) {
          item.columnType = "idColumn";
        }
        const _col_relation = ColRelation.get(item.columnType);
        if (_col_relation) {
          this.layerConfig.mappingConfig.push({
            name: _col_relation.column,
            displayName: item.displayName || item.name, //对应数据资源的列名
            mapColumnName: item.name, // 对应数据资源的列名
            columnType: item.columnType,
          });
        }
      }
    });
  }

  /**
   * 获取数据
   */
  async loadData() {
    this.isStatic ? "" : await this.resourceStore.dispatch("data/getData"); //当指定 resourceid 的 dtasourece 为空，则请求接口获取数据
    this._loadLayerData(); // 加载图层数据
  }

  /**
   * 获取资源数据
   * @returns {Array} 资源数据
   */
  getResourceData() {
    const data = this.isStatic
      ? this.staticStore.getAllLocalData()
      : this.resourceStore.getters["data/resourceDataList"]; //资源数据
    this.getPageParam();
    this.updateTotal(data, "init");
    return data;
  }

  /**
   * 获取分页参数
   * @returns {Object} 分页参数
   */
  getPageParam() {
    let params = this.resourceStore.getters["data/pageParam"];

    this.numberOfTags = params.recordTotal;
    return params;
  }

  /**
   * 获取资源详情
   * @returns {Object} 资源详情
   */
  getResourceInfo() {
    return this.resourceStore.getters["resourceInfo"]; //资源详情
  }

  /**
   * 获取地图字段名称
   * @returns {Object} 地图字段名称
   */
  getMapFieldNames() {
    const names = {};

    DefaultColumns.forEach((item) => {
      names[item.name] = item.name;
    });
    (this.layerConfig.mappingConfig || []).forEach((item) => {
      if (item.mapColumnName) {
        names[item.name] = item.mapColumnName;
      }
    });
    return names;
  }

  /**
   * 获取所有覆盖物
   *
   * @returns
   */
  getAllCover() {
    switch (this.type) {
      case "marker":
        return this.markers;
      case "polygon":
        return this.polygons;
      case "polyline":
        return this.polylines;
    }
  }

  /**
   * 默认信任 resourceColumns 传进来的参数正确
   * @param {more} resourceColumns
   */
  changeResourceColumn(resourceColumns) {
    this.layerConfig.changeMappingConfig(resourceColumns);
    this.columns.setConfigByConfig(resourceColumns);

    this.removeLayerOverlay();
    this._setLayerType(true);
    this._loadLayerData(); //加载图层数据
  }

  /**
   * 设置图层类别
   *
   * @param {boolean} mandatory 是否强制更新 type
   * @returns void
   */
  _setLayerType(mandatory = false) {
    const { mappingConfig, type } = this.layerConfig;
    // 如果 type 存在则不需要识别
    if (type && !mandatory) return;

    const mappingStatus = {
      hasLat:
        mappingConfig.find((r) => r.name == "lat") &&
        !!mappingConfig.find((r) => r.name == "lat").mapColumnName,
      hasLng:
        mappingConfig.find((r) => r.name == "lng") &&
        !!mappingConfig.find((r) => r.name == "lng").mapColumnName,
      hasCenterPoint:
        mappingConfig.find((r) => r.name == "centerPoint") &&
        !!mappingConfig.find((r) => r.name == "centerPoint").mapColumnName,
      hasPointList:
        mappingConfig.find((r) => r.name == "pointList") &&
        !!mappingConfig.find((r) => r.name == "pointList").mapColumnName,
    };

    if (mappingStatus.hasLat && mappingStatus.hasLng) {
      this._changeLayerType("marker"); // marker
    }
    if (mappingStatus.hasPointList && mappingStatus.hasCenterPoint && !(mappingStatus.hasLat && mappingStatus.hasLng)) {
      this._changeLayerType("polygon"); // polygon
    }
    if (mappingStatus.hasPointList && !(mappingStatus.hasLat && mappingStatus.hasLng)) {
      this._changeLayerType("polyline"); // polyline
    }
  }

  // 改变图层类型
  _changeLayerType(value) {
    // switch (value) {
    //   case "marker":
    //     this.initMarkerConifg(this.markerConfig || _DEFAULT_MARKER_CONFIG);
    //     break;
    //   case "polygon":
    //     this.initPolygonConfig(this.polygonConfig || _DEFAULT_POLYGON_CONFIG);
    //     break;
    //   case "polyline":
    //     this.initPolylineConfig(this.polylineConfig || _DEFAULT_POLYLINE_CONFIG);
    //     break;
    // }

    this.layerConfig.changeType(value);

    store.commit("setting/modifyDrawActionStatus", {
      drawType: value,
      type: "isDisabled",
      value: false,
    });
  }

  _createMark(data) {
    const { data: tempData, rawData } = this.columns.translateData(data, "toLocal");

    const coverCellConfig = this.layerConfig.getCoverCellConfig(tempData.id);
    const pointConfig = coverCellConfig?.icon || this.markerConfig;

    const flag = !tempData.lng || !tempData.lat;

    return {
      id: tempData.id,
      point: { lng: tempData.lng, lat: tempData.lat },
      name: tempData.name,
      pointConfig,
      useless: flag,
      extData: rawData,
      data: tempData,
    };
  }

  _createPolygon(data) {
    const { data: tempData, rawData } = this.columns.translateData(data, "toLocal");

    const coverCellConfig = this.layerConfig.getCoverCellConfig(tempData.id);
    const polygonConfig = coverCellConfig?.options || this.polygonConfig;
    let useless = false;

    let pointList = tempData.pointList;
    if (pointList) {
      try {
        pointList = JSON.parse(pointList).map((p) => [p[0], p[1]]);
      } catch (error) {
        useless = true;
      }
    }

    return {
      id: tempData.id,
      pointList: pointList || [],
      name: tempData.name,
      centerPoint: tempData.centerPoint,
      polygonConfig,
      useless,
      extData: rawData,
      data: tempData,
    };
  }

  _createPolyline(data) {
    const { data: tempData, rawData } = this.columns.translateData(data, "toLocal");

    return {
      id: tempData.id,
      name: tempData.name,
      pointList: tempData.pointList,
      extData: rawData,
      data: tempData,
      options: this.layerConfig.styleConfig,
    };
  }

  /**
   * 加载图层数据
   */
  _loadLayerData() {
    const resourceData = this.getResourceData();

    resourceData.map((data) => {
      if (this.type == "marker") {
        const markData = this._createMark(data);
        if (!markData.useless) {
          const overlay = new Marker(this, markData);
          this.Map.addOverlay(overlay.marker);
          this.markers.push(overlay);
        }
      } else if (this.type == "polygon") {
        // 过滤掉不合规的数据
        const polygonData = this._createPolygon(data);
        if (!polygonData.useless) {
          const overlay = new Polygon(this, polygonData);
          this.Map.addOverlay(overlay.polygon);
          this.polygons.push(overlay);
        }
      } else if (this.type == "polyline") {
        const polylineData = this._createPolyline(data);
        const overlay = new Polyline(this, polylineData);
        this.Map.addOverlay(overlay.polyline);
        this.polylines.push(overlay);
      }
    });
  }

  /**
   * 修改图层的显示和隐藏
   */
  changeShow() {
    this.hide = !this.hide;
    if (this.hide) {
      this._hideLayer();
    } else {
      this._showLayer();
    }
  }

  // 隐藏图层
  _hideLayer() {
    this.markers.map((d) => d.marker.hide());
    this.polygons.map((d) => {
      d.polygon.hide();
      d.label.hide();
    });
    this.polylines.map((d) => d.polyline.hide());
  }

  //显示图层
  _showLayer() {
    this.markers.map((d) => d.marker.show());
    this.polygons.map((d) => {
      d.polygon.show();
      d.label.show();
    });
    this.polylines.map((d) => d.polyline.show());
  }

  // 删除图层
  removeLayer() {
    delete _CACHE[this.id];
    this.markers.map((d) => this.Map.removeOverlay(d.marker));
    this.polygons.map((d) => this.Map.removeOverlay(d.polygon));
  }

  // 设置记录操作数组
  setStack(columns) {
    this.stack = new Stack(columns, {});
  }

  // 更新数量
  updateTotal(temp, type) {
    let params = this.resourceStore.getters["data/pageParam"];

    if (this.isStatic) {
      params.recordTotal = temp?.length || 0;
    } else {
      const number = type === "add" ? 1 : type === "remove" ? -1 : 0;
      this.numberOfTags += number;
      params.recordTotal = this.numberOfTags || 0;
    }

    this.resourceStore.commit("data/setPageParam", params);
  }

  // 操作打点画图等绘制
  manualDraw(type, { marker, polygon }) {
    if (type == "marker") {
      return this._drawMarker(marker);
    } else if (type == "polygon") {
      return this._drawPolygon(polygon);
    }
  }

  // 处理新标记
  _drawMarker(marker) {
    if (!marker) return null;

    const { lat, lng } = marker.point;
    const newData = {
      id: uuid(),
      name: "未命名",
      lat,
      lng,
    };

    const { data } = this.columns.translateData(newData, "toOnline");

    // 配置
    let markerInstance = new Marker(this, {
      id: newData.id,
      name: newData.name,
      point: { lng: newData.lng, lat: newData.lat },
      data: newData,
      extData: data,
      pointConfig: this.markerConfig,
    });

    this.markers.push(markerInstance);
    this.Map.addOverlay(markerInstance.marker);

    this.updateTotal(this.markers, "add");

    return markerInstance;
  }

  // 处理新多边形
  _drawPolygon(polygon) {
    const { centerPoint, pointList } = polygon;
    if (!centerPoint || !pointList) return;

    const newData = {
      id: uuid(),
      name: "未命名",
      pointList: JSON.stringify(pointList),
      centerPoint: JSON.stringify(centerPoint),
    };

    const { data } = this.columns.translateData(newData, "toOnline");

    let polygonInstance = new Polygon(this, {
      id: newData.id,
      name: newData.name,
      pointList,
      centerPoint,
      data: newData,
      extData: data,
    });

    this.polygons.push(polygonInstance);
    this.Map.addOverlay(polygonInstance.polygon);

    this.updateTotal(this.polygons, "add");

    return polygonInstance;
  }

  // 处理新折线
  _drawPolyline(polyline) {
    const polylineInstance = new Polyline(this, polyline);
    this.polylines.push(polylineInstance);
    this.Map.addOverlay(polylineInstance.polyline);
    this.updateTotal(this.polylines, "add");
    return polylineInstance;
  }

  /**
   * 打开编辑弹窗
   * @param {Boolean} openModal 是否打开弹窗
   * @param {Object} overlay 覆盖物
   */
  openEdit(openModal, overlay) {
    if (openModal) {
      // 如果存储的图层不是当前图层则切换图层
      const { currentLayer } = store.getters;
      if (currentLayer.id !== this.id) {
        store.commit("setCurrentLayer", this);
      }

      const modelData = {
        layer: this,
        ...overlay,
      };
      store.commit("setting/setUpdateModalData", modelData);
    } else {
      this.stack.update(overlay.extData); //添加到 stack
    }
    store.commit("setting/setUpdateModalOpen", openModal);
  }

  // 移除覆盖物
  removeLayerOverlay(type = "") {
    type = type || this.type;
    if (type == "marker") {
      this.markers.map((d) => this.Map.removeOverlay(d.marker));
    } else if (type == "polygon") {
      this.polygons.map((d) => {
        this.Map.removeOverlay(d.polygon);
        this.Map.removeOverlay(d.label);
      });
    }
  }

  // 初始化 marker 配置
  initMarkerConifg(config) {
    // 缓存
    !_CACHE[this.id] && (_CACHE[this.id] = {});

    this.markerConfig = config;

    // 存入缓存
    _CACHE[this.id].markerConfig = cloneDeep(this.markerConfig);
  }

  // 更新 marker 配置
  updateMarkerConfig(type, config = {}) {
    this.markerConfig = {
      iconType: type,
      [type]: {
        ...this.markerConfig[type],
        ...config,
      },
    };
  }

  // 重置 marker 配置
  resetMarkerConfig(id, config = null) {
    const markerConfig = cloneDeep(
      config || _CACHE[id || this.id]?.markerConfig || _DEFAULT_MARKER_CONFIG,
    );

    const { iconType } = markerConfig;

    const _config = markerConfig[iconType];
    this.changeMarkersType(iconType, _config);

    this.markerConfig = markerConfig;
    this._beforeEditMarkerConfig = null;

    return this.markerConfig;
  }

  // 更新覆盖物
  updateMarkers(type, config) {
    this.updateMarkerConfig(type, config);
    this.markers.map((i) => {
      i.updateMarkerIcon(config);
    });
  }

  /**
   * 切换所有 icon 的类型
   * @param {String} type 类型
   * @param {Object} config 配置
   */
  changeMarkersType(type, config = null) {
    this.updateMarkerConfig(type, config);

    this.markers.map((i) => {
      i.updateLabel({
        opacity: 1,
      });

      i.changeIconType(type, config);
      i.createContextMenu(this);
    });
  }

  /**
   * 初始化多边形配置
   * @param {Object} config 配置
   */
  initPolygonConfig(config) {
    !_CACHE[this.id] && (_CACHE[this.id] = {});

    this.polygonConfig = cloneDeep(config || _DEFAULT_POLYGON_CONFIG);

    _CACHE[this.id].polygonConfig = cloneDeep(this.polygonConfig);
  }

  /**
   * 更新多边形配置
   * @param {Object} config 配置
   */
  updatePolygonConfig(config) {
    this.polygonConfig = merge(this.polygonConfig, config);
  }

  /**
   * 更新图形配置
   * @param {Object} config 配置
   */
  updatePolygons(config) {
    this.updatePolygonConfig(config);
    this.polygons.map((i) => {
      i.updateStyle(config);
      i.createContextMenu(this);
      i.updateLabel({
        opacity: 1,
      });
    });
  }

  /**
   * 重置图形配置
   * @param {String|Number} id 覆盖物id
   * @param {Object} config 配置
   */
  resetPolygonConfig(id, config = null) {
    this.polygonConfig = cloneDeep(
      config || _CACHE[id || this.id]?.polygonConfig || _DEFAULT_POLYGON_CONFIG,
    );
    this.updatePolygons(this.polygonConfig);
    this._beforeEditPolygonConfig = null;
    return this.polygonConfig;
  }

  /**
   * 更新当前缓存
   *
   * @param {Object} config 配置
   */
  updateCache(config) {
    const typeKey = this.type === "polygon" ? "polygonConfig" : "markerConfig";
    _CACHE[this.id][typeKey] = cloneDeep(config);
  }

  /**
   * 当前图层的编辑模式
   * @param {String|Number} coveringId 覆盖物 id，通常是 marker 或 polygon 的 id
   */
  initEditMod(coveringId) {
    this.isEditMode = true;
    if (this.type === "marker") {
      // 缓存当前的配置标记配置
      if (!this._beforeEditMarkerConfig) {
        this._beforeEditMarkerConfig = cloneDeep(this.markerConfig);
      }

      this.markers.forEach((i) => {
        this._handlerMarkerEditMod(i, coveringId);
      });
    } else if (this.type === "polygon") {
      // 缓存当前的配置标记配置
      if (!this._beforeEditPolygonConfig) {
        this._beforeEditPolygonConfig = cloneDeep(this.polygonConfig);
      }

      this.polygons.forEach((i) => {
        this._handlerPolygonEditMod(i, coveringId);
      });
    }
  }

  /**
   * 处理单个标记点的编辑模式
   * @param {Marker} marker 标记点对象
   * @returns undefined
   */
  _handlerMarkerEditMod(marker, coveringId) {
    // 移除所有对象的右键事件
    marker.removeRightClickEvent();
    // 当前编辑的对象
    if (marker.id === coveringId) {
      marker.marker.setZIndex(10);
      marker.openPointMove(this);
      marker.updateMarkerIcon({
        strokeOpacity: 1,
        fillOpacity: 1,
      });
      marker.updateLabel({
        opacity: marker.name ? 1 : 0,
      });
      return;
    }
    // 其他对象
    marker.updateMarkerIcon({
      strokeOpacity: 0.2,
      fillOpacity: 0.2,
    });
    // 处理标记标签
    marker.updateLabel({
      opacity: 0.4,
    });
    marker.marker.setZIndex(5);
  }

  /**
   * 处理多边形编辑模式
   * @param {Polygon} polygon 多边形对象
   * @param {String|Number} coveringId 覆盖物 id
   */
  _handlerPolygonEditMod(polygon, coveringId) {
    polygon.removeRightClickEvent();
    if (polygon.id === coveringId) {
      polygon.updateStyle({
        strokeOpacity: 1,
        fillOpacity: 1,
      });
      polygon.openEditPolygon(this);
      polygon.updateLabel({
        opacity: polygon.name ? 1 : 0,
      });
      return;
    }
    polygon.updateStyle({
      strokeOpacity: 0.2,
      fillOpacity: 0.2,
    });
    polygon.updateLabel({
      opacity: 0.4,
    });
  }

  /**
   * 退出编辑模式
   */
  exitEditMod() {
    this.isEditMode = false;
    if (this.type === "marker") {
      this.resetMarkerConfig(null, this._beforeEditMarkerConfig || null);
    } else if (this.type === "polygon") {
      this.resetPolygonConfig(null, this._beforeEditPolygonConfig || null);
    }
  }

  /**
   * 清除所有标记点的右键事件
   */
  removeMarkersRightClickEvent() {
    this.markers.forEach((i) => i.removeRightClickEvent());
  }

  /**
   * 清除所有覆盖物的右键事件
   */
  removePolygonsRightClickEvent() {
    this.polygons.forEach((i) => i.removeRightClickEvent());
  }

  /**
   * 清除所有覆盖物的右键事件
   */
  removeAllCoverRightEvent() {
    this.removeMarkersRightClickEvent();
    this.removePolygonsRightClickEvent();
  }

  /**
   * 恢复所有标记点的右键事件
   */
  createMarkersRightClickEvent() {
    this.markers.forEach((i) => i.createContextMenu(this));
  }

  /**
   * 恢复所有覆盖物的右键事件
   */
  createPolygonsRightClickEvent() {
    this.polygons.forEach((i) => i.createContextMenu(this));
  }

  /**
   * 恢复所有覆盖物的右键事件
   */
  createAllCoverRightEvent() {
    this.createMarkersRightClickEvent();
    this.createPolygonsRightClickEvent();
  }

  /**
   * 删除一个覆盖物
   * @param {String} type 覆盖物类型
   * @param {String|Number} id 覆盖物id
   */
  delOverlay(type, id) {
    if (type === "marker") {
      this.markers = this.markers.filter((i) => i.id !== id);
      this.updateTotal(this.markers, "remove");
    } else {
      this.polygons = this.polygons.filter((i) => i.id !== id);
      this.updateTotal(this.polygons, "remove");
    }

    this.getPageParam();
  }

  /**
   * 查看实体详情
   *
   * @param {string|number} entityId 实体id
   */
  showEntityDetail(entityId) {
    if (this.showEntityDetailCallBack) {
      this.showEntityDetailCallBack(this.layerConfig.resourceId, entityId);
    }
  }

  /**
   * 更新图层中的数据
   *
   * @param {object} data 数据
   * @param {boolean} translateToLocal 是否需要翻译成本地数据
   */
  updateData(data = null, translateToLocal = false) {
    let _data = data;
    // 转换数据
    if (translateToLocal) {
      const { data: updateData, rawData } = this.columns.translateData(data, "toLocal");
      _data = {
        ...updateData,
        rawData,
      };
    }

    this.stack.update(_data); // 添加到stack

    const oldDatas =
      this.type === "marker" ? this.markers : this.type === "polygon" ? this.polygons : null;

    if (oldDatas) {
      const index = oldDatas.findIndex((marker) => marker.id === _data.id);
      if (index >= 0) {
        const current = oldDatas[index];
        current.updateData(_data);
      }
    }

    // 静态数据
    if (this.isStatic) {
      this.staticStore.update(_data);
    }
  }

  /**
   * 获取覆盖物的所有时间
   * @returns {Array} 时间范围
   */
  getTimeRange() {
    let coverCells = [];
    switch (this.type) {
      case "marker":
        coverCells = this.markers;
        break;
      case "polygon":
        coverCells = this.polygons;
        break;
      case "polyline":
        coverCells = this.polylines;
        break;
    }
    const timeRange = coverCells.map((i) => i.data.time);
    this.hasTimeRange = timeRange.some(Boolean);
    return timeRange;
  }

  /**
   * 通过时间范围控制图层中覆盖物是否显示
   *
   * @param {Array} timeRange 时间范围, 只包含最大值和最小值
   */
  filterCoverByTimeRange(timeRange) {
    const [start, end] = timeRange;
    const coverCells = this.getAllCover();

    if (!start || !end) {
      coverCells.forEach((i) => {
        i.show();
      });
      return;
    }

    coverCells.forEach((i) => {
      // 判断是时间是否在范围内
      let isInRange = false;

      if (!start) {
        isInRange = !i.data.time || new Date(i.data.time).valueOf() <= new Date(end).valueOf();
      } else if (!end) {
        isInRange =
          i.data.time == start || new Date(i.data.time).valueOf() >= new Date(start).valueOf();
      } else {
        isInRange =
          new Date(i.data.time).valueOf() >= new Date(start).valueOf() &&
          new Date(i.data.time).valueOf() <= new Date(end).valueOf();
      }

      if (isInRange) {
        i.show();
      } else {
        i.hide();
      }
    });
  }

  /**
   * 更新覆盖物的样式
   *
   * @param {Object} config 样式配置
   */
  updateCellsStyle(config) {
    switch (this.type) {
      case "marker":
        this.updateMarkers(config);
        break;
      case "polygon":
        this.updatePolygons(config);
        break;
    }
  }

  /**
   * 更新覆盖物的配置
   *
   * @param {Object} config 配置
   */
  updateCllConfig(config) {
    this.layerConfig.changeStyleConfig(config);
  }
}
