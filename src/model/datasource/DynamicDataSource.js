import DataSourceInterface from './DataSourceInterface.js';
import ErrorCenter, { ERROR_TYPES } from '@/utils/ErrorCenter.js';
import { createStore } from "@daas/surface-web-lib/resource";

/**
 * 动态数据源
 * 处理远程 API 数据，支持实时数据获取和更新
 */
export default class DynamicDataSource extends DataSourceInterface {
  constructor(config = {}) {
    super({
      ...config,
      type: 'dynamic'
    });
    
    this.resourceId = config.resourceId;
    this.apiEndpoint = config.apiEndpoint;
    this.resourceStore = createStore();
    this.cache = new Map();
    this.cacheTimeout = config.cacheTimeout || 5 * 60 * 1000; // 5分钟
    this.retryCount = 0;
    this.maxRetries = config.maxRetries || 3;
    this.retryDelay = config.retryDelay || 1000;
  }

  /**
   * 初始化动态数据源
   * @param {Object} config 配置参数
   */
  async init(config = {}) {
    try {
      this.setStatus('loading');
      
      // 合并配置
      Object.assign(this.config, config);
      
      if (config.resourceId) {
        this.resourceId = config.resourceId;
      }
      
      // 获取资源信息
      if (this.resourceId) {
        const resourceInfo = await this._getResourceInfo();
        this.resourceStore.commit("setResourceInfo", resourceInfo);
        
        // 设置元数据
        this.metadata = {
          resourceId: this.resourceId,
          resourceName: resourceInfo.resourceName,
          columns: resourceInfo.columns,
          totalCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      }
      
      this.lastUpdated = new Date().toISOString();
      this.setStatus('loaded');
      
      return this.metadata;
    } catch (error) {
      this.setError({
        type: ERROR_TYPES.RESOURCE_NOT_FOUND,
        message: 'Failed to initialize dynamic data source',
        originalError: error
      });
      
      ErrorCenter.handle(error, `DynamicDataSource.init[${this.id}]`);
      throw error;
    }
  }

  /**
   * 加载数据
   * @param {Object} params 查询参数
   */
  async loadData(params = {}) {
    try {
      this.setStatus('loading');
      
      // 检查缓存
      const cacheKey = this._getCacheKey(params);
      const cachedData = this._getFromCache(cacheKey);
      if (cachedData) {
        this.data = cachedData.data;
        this.setStatus('loaded');
        return this.data;
      }
      
      // 设置查询参数
      this._setQueryParams(params);
      
      // 获取数据
      const result = await this._fetchDataWithRetry();
      this.data = result;
      
      // 缓存数据
      this._setToCache(cacheKey, { data: result, timestamp: Date.now() });
      
      // 更新元数据
      this._updateMetadata();
      
      this.setStatus('loaded');
      return result;
    } catch (error) {
      this.setError({
        type: this._getErrorType(error),
        message: 'Failed to load dynamic data',
        originalError: error
      });
      
      ErrorCenter.handle(error, `DynamicDataSource.loadData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 添加数据
   * @param {Object} item 数据项
   */
  async addData(item) {
    try {
      // 验证数据
      const validation = this.validateData(item);
      if (!validation.valid) {
        throw new Error(`Data validation failed: ${validation.errors.join(', ')}`);
      }
      
      // 转换数据
      const transformedItem = this.transformData(item);
      
      // 发送到服务器
      const result = await this.resourceStore.dispatch("data/addData", {
        values: [transformedItem]
      });
      
      // 更新本地数据
      if (this.data) {
        this.data.push(result);
      }
      
      // 清除相关缓存
      this._clearCache();
      
      // 更新元数据
      this._updateMetadata();
      
      return result;
    } catch (error) {
      ErrorCenter.handle(error, `DynamicDataSource.addData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 更新数据
   * @param {String} id 数据ID
   * @param {Object} updates 更新内容
   */
  async updateData(id, updates) {
    try {
      // 验证更新数据
      const validation = this.validateData(updates);
      if (!validation.valid) {
        throw new Error(`Data validation failed: ${validation.errors.join(', ')}`);
      }
      
      // 转换数据
      const transformedUpdates = this.transformData(updates);
      
      // 发送到服务器
      const result = await this.resourceStore.dispatch("data/updateData", {
        values: [{ ...transformedUpdates, id }]
      });
      
      // 更新本地数据
      if (this.data) {
        const index = this.data.findIndex(item => item.id === id);
        if (index !== -1) {
          this.data[index] = { ...this.data[index], ...result };
        }
      }
      
      // 清除相关缓存
      this._clearCache();
      
      // 更新元数据
      this._updateMetadata();
      
      return result;
    } catch (error) {
      ErrorCenter.handle(error, `DynamicDataSource.updateData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 删除数据
   * @param {String} id 数据ID
   */
  async deleteData(id) {
    try {
      // 发送到服务器
      await this.resourceStore.dispatch("data/delData", {
        primaryIds: [id]
      });
      
      // 更新本地数据
      if (this.data) {
        const index = this.data.findIndex(item => item.id === id);
        if (index !== -1) {
          this.data.splice(index, 1);
        }
      }
      
      // 清除相关缓存
      this._clearCache();
      
      // 更新元数据
      this._updateMetadata();
      
      return { id };
    } catch (error) {
      ErrorCenter.handle(error, `DynamicDataSource.deleteData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 获取分页参数
   */
  getPageParams() {
    return this.resourceStore.getters["data/pageParam"];
  }

  /**
   * 设置分页参数
   * @param {Object} params 分页参数
   */
  setPageParams(params) {
    this.resourceStore.commit("data/setPageParam", params);
  }

  /**
   * 获取资源信息
   * @returns {Object} 资源信息
   * @private
   */
  async _getResourceInfo() {
    if (!this.resourceId) {
      throw new Error('Resource ID is required for dynamic data source');
    }
    
    return await this.resourceStore.dispatch("getResourceInfo", {
      resourceId: this.resourceId
    });
  }

  /**
   * 设置查询参数
   * @param {Object} params 参数
   * @private
   */
  _setQueryParams(params) {
    // 设置分页参数
    if (params.page || params.pageSize) {
      this.resourceStore.commit("data/setPageParam", {
        pageIndex: params.page || 1,
        limit: params.pageSize || 100
      });
    }
    
    // 设置扩展认证参数
    if (params.modularType && params.modularId) {
      this.resourceStore.commit("data/setExtendAuthParams", {
        modularType: params.modularType,
        modularId: params.modularId
      });
    }
  }

  /**
   * 带重试的数据获取
   * @returns {Array} 数据
   * @private
   */
  async _fetchDataWithRetry() {
    for (let i = 0; i <= this.maxRetries; i++) {
      try {
        await this.resourceStore.dispatch("data/getData");
        return this.resourceStore.getters["data/resourceDataList"];
      } catch (error) {
        if (i === this.maxRetries) {
          throw error;
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * (i + 1)));
        console.warn(`Retry attempt ${i + 1}/${this.maxRetries} for data fetch`);
      }
    }
  }

  /**
   * 获取缓存键
   * @param {Object} params 参数
   * @returns {String} 缓存键
   * @private
   */
  _getCacheKey(params) {
    return `${this.resourceId}_${JSON.stringify(params)}`;
  }

  /**
   * 从缓存获取数据
   * @param {String} key 缓存键
   * @returns {Object|null} 缓存数据
   * @private
   */
  _getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached;
    }
    return null;
  }

  /**
   * 设置缓存数据
   * @param {String} key 缓存键
   * @param {Object} data 数据
   * @private
   */
  _setToCache(key, data) {
    this.cache.set(key, data);
    
    // 限制缓存大小
    if (this.cache.size > 50) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  /**
   * 清除缓存
   * @private
   */
  _clearCache() {
    this.cache.clear();
  }

  /**
   * 获取错误类型
   * @param {Error} error 错误对象
   * @returns {String} 错误类型
   * @private
   */
  _getErrorType(error) {
    if (error.status === 404) {
      return ERROR_TYPES.RESOURCE_NOT_FOUND;
    }
    if (error.status === 403) {
      return ERROR_TYPES.PERMISSION_DENIED;
    }
    if (error.message && error.message.includes('network')) {
      return ERROR_TYPES.NETWORK_ERROR;
    }
    return ERROR_TYPES.UNKNOWN_ERROR;
  }

  /**
   * 更新元数据
   * @private
   */
  _updateMetadata() {
    if (this.metadata) {
      this.metadata = {
        ...this.metadata,
        totalCount: this.data ? this.data.length : 0,
        updatedAt: new Date().toISOString()
      };
    }
    this.lastUpdated = new Date().toISOString();
  }

  /**
   * 销毁数据源
   */
  destroy() {
    super.destroy();
    this._clearCache();
    this.resourceStore = null;
  }
}
