import StaticDataSource from './StaticDataSource.js';
import DynamicDataSource from './DynamicDataSource.js';
import ErrorCenter, { ERROR_TYPES } from '@/utils/ErrorCenter.js';

/**
 * 数据源类型枚举
 */
export const DATA_SOURCE_TYPES = {
  STATIC: 'static',
  DYNAMIC: 'dynamic',
  HYBRID: 'hybrid'
};

/**
 * 数据源工厂类
 * 负责创建和管理不同类型的数据源
 */
export class DataSourceFactory {
  static registry = new Map();
  static instances = new Map();

  /**
   * 注册数据源类型
   * @param {String} type 数据源类型
   * @param {Class} DataSourceClass 数据源类
   */
  static register(type, DataSourceClass) {
    if (typeof DataSourceClass !== 'function') {
      throw new Error('DataSourceClass must be a constructor function');
    }
    
    this.registry.set(type, DataSourceClass);
  }

  /**
   * 创建数据源
   * @param {String} type 数据源类型
   * @param {Object} config 配置参数
   * @returns {DataSourceInterface} 数据源实例
   */
  static create(type, config = {}) {
    try {
      const DataSourceClass = this.registry.get(type);
      
      if (!DataSourceClass) {
        throw new Error(`Unknown data source type: ${type}`);
      }
      
      // 创建实例
      const instance = new DataSourceClass(config);
      
      // 缓存实例
      if (config.id) {
        this.instances.set(config.id, instance);
      }
      
      return instance;
    } catch (error) {
      ErrorCenter.handle(error, 'DataSourceFactory.create', {
        type,
        config
      });
      throw error;
    }
  }

  /**
   * 创建静态数据源
   * @param {Object} config 配置参数
   * @returns {StaticDataSource} 静态数据源实例
   */
  static createStatic(config = {}) {
    return this.create(DATA_SOURCE_TYPES.STATIC, {
      ...config,
      type: DATA_SOURCE_TYPES.STATIC
    });
  }

  /**
   * 创建动态数据源
   * @param {Object} config 配置参数
   * @returns {DynamicDataSource} 动态数据源实例
   */
  static createDynamic(config = {}) {
    return this.create(DATA_SOURCE_TYPES.DYNAMIC, {
      ...config,
      type: DATA_SOURCE_TYPES.DYNAMIC
    });
  }

  /**
   * 创建混合数据源
   * @param {Object} config 配置参数
   * @returns {HybridDataSource} 混合数据源实例
   */
  static createHybrid(config = {}) {
    // 混合数据源可以在后续扩展中实现
    throw new Error('Hybrid data source not implemented yet');
  }

  /**
   * 获取数据源实例
   * @param {String} id 数据源ID
   * @returns {DataSourceInterface|null} 数据源实例
   */
  static getInstance(id) {
    return this.instances.get(id) || null;
  }

  /**
   * 销毁数据源实例
   * @param {String} id 数据源ID
   */
  static destroyInstance(id) {
    const instance = this.instances.get(id);
    if (instance) {
      instance.destroy();
      this.instances.delete(id);
    }
  }

  /**
   * 销毁所有数据源实例
   */
  static destroyAll() {
    for (const [id, instance] of this.instances) {
      try {
        instance.destroy();
      } catch (error) {
        console.error(`Error destroying data source ${id}:`, error);
      }
    }
    this.instances.clear();
  }

  /**
   * 获取所有数据源实例
   * @returns {Map} 数据源实例映射
   */
  static getAllInstances() {
    return new Map(this.instances);
  }

  /**
   * 获取数据源统计信息
   * @returns {Object} 统计信息
   */
  static getStats() {
    const stats = {
      totalInstances: this.instances.size,
      byType: {},
      byStatus: {}
    };

    for (const instance of this.instances.values()) {
      const type = instance.type;
      const status = instance.getStatus();

      stats.byType[type] = (stats.byType[type] || 0) + 1;
      stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
    }

    return stats;
  }

  /**
   * 验证数据源配置
   * @param {String} type 数据源类型
   * @param {Object} config 配置参数
   * @returns {Object} 验证结果
   */
  static validateConfig(type, config) {
    const errors = [];

    // 基本验证
    if (!type) {
      errors.push('Data source type is required');
    }

    if (!this.registry.has(type)) {
      errors.push(`Unknown data source type: ${type}`);
    }

    // 类型特定验证
    switch (type) {
      case DATA_SOURCE_TYPES.STATIC:
        if (!config.localData && !Array.isArray(config.localData)) {
          errors.push('Static data source requires localData array');
        }
        break;

      case DATA_SOURCE_TYPES.DYNAMIC:
        if (!config.resourceId && !config.apiEndpoint) {
          errors.push('Dynamic data source requires resourceId or apiEndpoint');
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 从配置创建数据源
   * @param {Object} config 完整配置
   * @returns {DataSourceInterface} 数据源实例
   */
  static fromConfig(config) {
    const { type, ...restConfig } = config;
    
    // 验证配置
    const validation = this.validateConfig(type, restConfig);
    if (!validation.valid) {
      throw new Error(`Invalid config: ${validation.errors.join(', ')}`);
    }

    return this.create(type, restConfig);
  }

  /**
   * 克隆数据源
   * @param {String} sourceId 源数据源ID
   * @param {Object} overrides 覆盖配置
   * @returns {DataSourceInterface} 克隆的数据源实例
   */
  static clone(sourceId, overrides = {}) {
    const sourceInstance = this.getInstance(sourceId);
    if (!sourceInstance) {
      throw new Error(`Data source ${sourceId} not found`);
    }

    const config = {
      ...sourceInstance.serialize(),
      ...overrides,
      id: overrides.id || `${sourceId}_clone_${Date.now()}`
    };

    return this.fromConfig(config);
  }

  /**
   * 批量创建数据源
   * @param {Array} configs 配置数组
   * @returns {Array} 数据源实例数组
   */
  static batchCreate(configs) {
    const results = [];
    
    for (const config of configs) {
      try {
        const instance = this.fromConfig(config);
        results.push({ success: true, instance });
      } catch (error) {
        results.push({ success: false, error: error.message, config });
      }
    }
    
    return results;
  }

  /**
   * 检查数据源健康状态
   * @returns {Object} 健康状态报告
   */
  static checkHealth() {
    const report = {
      healthy: true,
      issues: [],
      stats: this.getStats()
    };

    for (const [id, instance] of this.instances) {
      if (instance.hasError()) {
        report.healthy = false;
        report.issues.push({
          id,
          type: instance.type,
          error: instance.getError()
        });
      }
    }

    return report;
  }
}

// 注册默认数据源类型
DataSourceFactory.register(DATA_SOURCE_TYPES.STATIC, StaticDataSource);
DataSourceFactory.register(DATA_SOURCE_TYPES.DYNAMIC, DynamicDataSource);

// 在页面卸载时清理所有数据源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    DataSourceFactory.destroyAll();
  });
}

export default DataSourceFactory;
