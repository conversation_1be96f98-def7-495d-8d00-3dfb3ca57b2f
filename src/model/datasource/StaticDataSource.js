import DataSourceInterface from './DataSourceInterface.js';
import ErrorCenter, { ERROR_TYPES } from '@/utils/ErrorCenter.js';
import { uuid } from '@/utils/com-function.js';

/**
 * 静态数据源
 * 处理本地静态数据，支持内存中的 CRUD 操作
 */
export default class StaticDataSource extends DataSourceInterface {
  constructor(config = {}) {
    super({
      ...config,
      type: 'static'
    });
    
    this.localData = config.localData || [];
    this.autoGenerateId = config.autoGenerateId !== false;
    this.idField = config.idField || 'id';
    this.schema = config.schema || null;
  }

  /**
   * 初始化静态数据源
   * @param {Object} config 配置参数
   */
  async init(config = {}) {
    try {
      this.setStatus('loading');
      
      // 合并配置
      Object.assign(this.config, config);
      
      // 初始化本地数据
      if (config.localData) {
        this.localData = Array.isArray(config.localData) ? config.localData : [];
      }
      
      // 处理数据
      this.data = this._processData(this.localData);
      
      // 设置元数据
      this.metadata = {
        totalCount: this.data.length,
        fields: this._extractFields(this.data),
        schema: this.schema,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      this.lastUpdated = new Date().toISOString();
      this.setStatus('loaded');
      
      return this.data;
    } catch (error) {
      this.setError({
        type: ERROR_TYPES.DATA_PARSE_ERROR,
        message: 'Failed to initialize static data source',
        originalError: error
      });
      
      ErrorCenter.handle(error, `StaticDataSource.init[${this.id}]`);
      throw error;
    }
  }

  /**
   * 加载数据
   * @param {Object} params 查询参数
   */
  async loadData(params = {}) {
    try {
      this.setStatus('loading');
      
      let result = [...this.data];
      
      // 应用过滤条件
      if (params.filter) {
        result = this._applyFilter(result, params.filter);
      }
      
      // 应用排序
      if (params.sort) {
        result = this._applySort(result, params.sort);
      }
      
      // 应用分页
      if (params.page && params.pageSize) {
        const pagedResult = this.getPagedData(params.page, params.pageSize);
        result = pagedResult.data;
      }
      
      this.setStatus('loaded');
      return result;
    } catch (error) {
      this.setError({
        type: ERROR_TYPES.DATA_PARSE_ERROR,
        message: 'Failed to load static data',
        originalError: error
      });
      
      ErrorCenter.handle(error, `StaticDataSource.loadData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 添加数据
   * @param {Object} item 数据项
   */
  async addData(item) {
    try {
      // 验证数据
      const validation = this.validateData(item);
      if (!validation.valid) {
        throw new Error(`Data validation failed: ${validation.errors.join(', ')}`);
      }
      
      // 转换数据
      const transformedItem = this.transformData(item);
      
      // 自动生成 ID
      if (this.autoGenerateId && !transformedItem[this.idField]) {
        transformedItem[this.idField] = uuid();
      }
      
      // 添加到数据中
      this.data.push(transformedItem);
      this.localData.push(transformedItem);
      
      // 更新元数据
      this._updateMetadata();
      
      return transformedItem;
    } catch (error) {
      ErrorCenter.handle(error, `StaticDataSource.addData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 更新数据
   * @param {String} id 数据ID
   * @param {Object} updates 更新内容
   */
  async updateData(id, updates) {
    try {
      const index = this.data.findIndex(item => item[this.idField] === id);
      if (index === -1) {
        throw new Error(`Data with id ${id} not found`);
      }
      
      // 验证更新数据
      const updatedItem = { ...this.data[index], ...updates };
      const validation = this.validateData(updatedItem);
      if (!validation.valid) {
        throw new Error(`Data validation failed: ${validation.errors.join(', ')}`);
      }
      
      // 转换数据
      const transformedItem = this.transformData(updatedItem);
      
      // 更新数据
      this.data[index] = transformedItem;
      
      // 同步更新本地数据
      const localIndex = this.localData.findIndex(item => item[this.idField] === id);
      if (localIndex !== -1) {
        this.localData[localIndex] = transformedItem;
      }
      
      // 更新元数据
      this._updateMetadata();
      
      return transformedItem;
    } catch (error) {
      ErrorCenter.handle(error, `StaticDataSource.updateData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 删除数据
   * @param {String} id 数据ID
   */
  async deleteData(id) {
    try {
      const index = this.data.findIndex(item => item[this.idField] === id);
      if (index === -1) {
        throw new Error(`Data with id ${id} not found`);
      }
      
      // 删除数据
      const deletedItem = this.data.splice(index, 1)[0];
      
      // 同步删除本地数据
      const localIndex = this.localData.findIndex(item => item[this.idField] === id);
      if (localIndex !== -1) {
        this.localData.splice(localIndex, 1);
      }
      
      // 更新元数据
      this._updateMetadata();
      
      return deletedItem;
    } catch (error) {
      ErrorCenter.handle(error, `StaticDataSource.deleteData[${this.id}]`);
      throw error;
    }
  }

  /**
   * 批量操作
   * @param {Array} operations 操作数组
   */
  async batchOperation(operations) {
    const results = [];
    
    for (const operation of operations) {
      try {
        let result;
        switch (operation.type) {
          case 'add':
            result = await this.addData(operation.data);
            break;
          case 'update':
            result = await this.updateData(operation.id, operation.data);
            break;
          case 'delete':
            result = await this.deleteData(operation.id);
            break;
          default:
            throw new Error(`Unknown operation type: ${operation.type}`);
        }
        results.push({ success: true, result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * 获取所有本地数据
   */
  getAllLocalData() {
    return [...this.localData];
  }

  /**
   * 设置本地数据
   * @param {Array} data 数据数组
   */
  setLocalData(data) {
    this.localData = Array.isArray(data) ? data : [];
    this.data = this._processData(this.localData);
    this._updateMetadata();
  }

  /**
   * 清空数据
   */
  clearData() {
    this.data = [];
    this.localData = [];
    this._updateMetadata();
  }

  /**
   * 处理数据
   * @param {Array} rawData 原始数据
   * @returns {Array} 处理后的数据
   * @private
   */
  _processData(rawData) {
    return rawData.map(item => {
      // 自动生成 ID
      if (this.autoGenerateId && !item[this.idField]) {
        item[this.idField] = uuid();
      }
      
      // 转换数据
      return this.transformData(item);
    });
  }

  /**
   * 提取字段信息
   * @param {Array} data 数据数组
   * @returns {Array} 字段信息
   * @private
   */
  _extractFields(data) {
    if (!data || data.length === 0) {
      return [];
    }
    
    const fields = new Set();
    data.forEach(item => {
      Object.keys(item).forEach(key => fields.add(key));
    });
    
    return Array.from(fields).map(field => ({
      name: field,
      type: this._inferFieldType(data, field)
    }));
  }

  /**
   * 推断字段类型
   * @param {Array} data 数据数组
   * @param {String} field 字段名
   * @returns {String} 字段类型
   * @private
   */
  _inferFieldType(data, field) {
    const values = data.map(item => item[field]).filter(val => val != null);
    if (values.length === 0) return 'unknown';
    
    const firstValue = values[0];
    if (typeof firstValue === 'number') return 'number';
    if (typeof firstValue === 'boolean') return 'boolean';
    if (firstValue instanceof Date) return 'date';
    if (Array.isArray(firstValue)) return 'array';
    if (typeof firstValue === 'object') return 'object';
    return 'string';
  }

  /**
   * 应用过滤条件
   * @param {Array} data 数据
   * @param {Object} filter 过滤条件
   * @returns {Array} 过滤后的数据
   * @private
   */
  _applyFilter(data, filter) {
    return data.filter(item => {
      return Object.keys(filter).every(key => {
        const filterValue = filter[key];
        const itemValue = item[key];
        
        if (typeof filterValue === 'object' && filterValue.operator) {
          return this._applyOperator(itemValue, filterValue.operator, filterValue.value);
        }
        
        return itemValue === filterValue;
      });
    });
  }

  /**
   * 应用操作符
   * @param {*} itemValue 项值
   * @param {String} operator 操作符
   * @param {*} filterValue 过滤值
   * @returns {Boolean} 是否匹配
   * @private
   */
  _applyOperator(itemValue, operator, filterValue) {
    switch (operator) {
      case 'eq': return itemValue === filterValue;
      case 'ne': return itemValue !== filterValue;
      case 'gt': return itemValue > filterValue;
      case 'gte': return itemValue >= filterValue;
      case 'lt': return itemValue < filterValue;
      case 'lte': return itemValue <= filterValue;
      case 'contains': return itemValue && itemValue.toString().includes(filterValue);
      case 'startsWith': return itemValue && itemValue.toString().startsWith(filterValue);
      case 'endsWith': return itemValue && itemValue.toString().endsWith(filterValue);
      default: return itemValue === filterValue;
    }
  }

  /**
   * 应用排序
   * @param {Array} data 数据
   * @param {Object} sort 排序配置
   * @returns {Array} 排序后的数据
   * @private
   */
  _applySort(data, sort) {
    const { field, order = 'asc' } = sort;
    
    return data.sort((a, b) => {
      const aValue = a[field];
      const bValue = b[field];
      
      if (aValue < bValue) return order === 'asc' ? -1 : 1;
      if (aValue > bValue) return order === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * 更新元数据
   * @private
   */
  _updateMetadata() {
    this.metadata = {
      ...this.metadata,
      totalCount: this.data.length,
      fields: this._extractFields(this.data),
      updatedAt: new Date().toISOString()
    };
    this.lastUpdated = new Date().toISOString();
  }
}
