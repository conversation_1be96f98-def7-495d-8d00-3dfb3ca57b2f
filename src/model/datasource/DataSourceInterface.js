/**
 * 数据源接口
 * 定义数据源的标准接口，所有数据源实现都应该遵循此接口
 */

/**
 * 数据源基类
 * 提供数据源的基本功能和接口定义
 */
export default class DataSourceInterface {
  constructor(config = {}) {
    this.config = config;
    this.id = config.id || `datasource_${Date.now()}`;
    this.type = config.type || 'unknown';
    this.status = 'idle'; // idle, loading, loaded, error
    this.data = null;
    this.metadata = {};
    this.lastUpdated = null;
    this.errorInfo = null;
  }

  /**
   * 初始化数据源
   * @param {Object} config 配置参数
   * @returns {Promise} 初始化结果
   */
  async init(config = {}) {
    throw new Error('init method must be implemented by subclass');
  }

  /**
   * 加载数据
   * @param {Object} params 查询参数
   * @returns {Promise<Array>} 数据数组
   */
  async loadData(params = {}) {
    throw new Error('loadData method must be implemented by subclass');
  }

  /**
   * 刷新数据
   * @param {Object} params 查询参数
   * @returns {Promise<Array>} 数据数组
   */
  async refreshData(params = {}) {
    return this.loadData(params);
  }

  /**
   * 获取数据
   * @returns {Array} 当前数据
   */
  getData() {
    return this.data;
  }

  /**
   * 获取元数据
   * @returns {Object} 元数据
   */
  getMetadata() {
    return this.metadata;
  }

  /**
   * 获取状态
   * @returns {String} 当前状态
   */
  getStatus() {
    return this.status;
  }

  /**
   * 设置状态
   * @param {String} status 状态
   */
  setStatus(status) {
    this.status = status;
  }

  /**
   * 是否已加载
   * @returns {Boolean} 是否已加载
   */
  isLoaded() {
    return this.status === 'loaded' && this.data !== null;
  }

  /**
   * 是否正在加载
   * @returns {Boolean} 是否正在加载
   */
  isLoading() {
    return this.status === 'loading';
  }

  /**
   * 是否有错误
   * @returns {Boolean} 是否有错误
   */
  hasError() {
    return this.status === 'error';
  }

  /**
   * 获取错误信息
   * @returns {Object} 错误信息
   */
  getError() {
    return this.errorInfo;
  }

  /**
   * 设置错误信息
   * @param {Object} error 错误信息
   */
  setError(error) {
    this.status = 'error';
    this.errorInfo = error;
  }

  /**
   * 清除错误
   */
  clearError() {
    this.errorInfo = null;
    if (this.status === 'error') {
      this.status = 'idle';
    }
  }

  /**
   * 添加数据
   * @param {Object} item 数据项
   * @returns {Promise} 添加结果
   */
  async addData(item) {
    throw new Error('addData method must be implemented by subclass');
  }

  /**
   * 更新数据
   * @param {String} id 数据ID
   * @param {Object} updates 更新内容
   * @returns {Promise} 更新结果
   */
  async updateData(id, updates) {
    throw new Error('updateData method must be implemented by subclass');
  }

  /**
   * 删除数据
   * @param {String} id 数据ID
   * @returns {Promise} 删除结果
   */
  async deleteData(id) {
    throw new Error('deleteData method must be implemented by subclass');
  }

  /**
   * 搜索数据
   * @param {Object} criteria 搜索条件
   * @returns {Array} 搜索结果
   */
  searchData(criteria) {
    if (!this.data) {
      return [];
    }

    return this.data.filter(item => {
      return Object.keys(criteria).every(key => {
        const value = criteria[key];
        const itemValue = item[key];
        
        if (typeof value === 'string') {
          return itemValue && itemValue.toString().toLowerCase().includes(value.toLowerCase());
        }
        
        return itemValue === value;
      });
    });
  }

  /**
   * 分页获取数据
   * @param {Number} page 页码
   * @param {Number} pageSize 页大小
   * @returns {Object} 分页结果
   */
  getPagedData(page = 1, pageSize = 10) {
    if (!this.data) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      };
    }

    const total = this.data.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const data = this.data.slice(startIndex, endIndex);

    return {
      data,
      total,
      page,
      pageSize,
      totalPages
    };
  }

  /**
   * 验证数据
   * @param {Object} item 数据项
   * @returns {Object} 验证结果
   */
  validateData(item) {
    return {
      valid: true,
      errors: []
    };
  }

  /**
   * 转换数据格式
   * @param {Object} item 原始数据
   * @returns {Object} 转换后的数据
   */
  transformData(item) {
    return item;
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      id: this.id,
      type: this.type,
      status: this.status,
      dataCount: this.data ? this.data.length : 0,
      lastUpdated: this.lastUpdated,
      hasError: this.hasError()
    };
  }

  /**
   * 销毁数据源
   */
  destroy() {
    this.data = null;
    this.metadata = {};
    this.errorInfo = null;
    this.status = 'destroyed';
  }

  /**
   * 克隆数据源
   * @returns {DataSourceInterface} 克隆的数据源
   */
  clone() {
    const CloneClass = this.constructor;
    return new CloneClass(this.config);
  }

  /**
   * 序列化数据源配置
   * @returns {Object} 序列化结果
   */
  serialize() {
    return {
      id: this.id,
      type: this.type,
      config: this.config,
      metadata: this.metadata
    };
  }

  /**
   * 从序列化数据恢复数据源
   * @param {Object} serialized 序列化数据
   * @returns {DataSourceInterface} 恢复的数据源
   */
  static deserialize(serialized) {
    return new this(serialized.config);
  }
}
