import Vue from "vue";
export default class Stack {
  constructor(columns, params) {
    this.currentData = params.currentData || []; //加载资源进来初始
    this.addData = params.addData || []; //记录新增的
    this.deleteData = params.deleteData || []; //记录删除的  (删除前判断，当存在于add则在add中删除此，当存在于current则push到delete中进来////删除前判断，当在update或者)
    this.updateData = params.updateData || []; //记录修改的  (更新前判断，当存在于add则在add中更新此，当存在于current且不存在于update则push到update中进来，当存在于current且存在于update则在update更新此)

    this.primaryId = params.primaryId || "m_id"; //主键
    this.init(columns);
  }

  init(columns) {
    this.currentData = columns.map((d) => d);
  }

  add(data) {
    this.addData.push(data);
  }

  /**
   * 删除
   * 1、删除前判断,当存在于addData则在addData中删除此，没有则push到delete中进来
   * 2、删除前判断，当存在于在updateData中，则在updateData中删除此项目，然后再添加到deleteData
   * @param {*} id
   */
  delete(id) {
    const hasIndex = this.addData.findIndex((d) => d[this.primaryId] === id);
    if (hasIndex >= 0) this.addData.splice(hasIndex, 1);
    else this.deleteData.push(id);

    const hasInUpdataIndex = this.updateData.findIndex((d) => d[this.primaryId] === id);
    if (hasInUpdataIndex >= 0) this.updateData.splice(hasInUpdataIndex, 1);
  }
  /**
   * (更新前判断，当存在于add则在add中更新此，当存在于current且不存在于update则push到update中进来，当存在于current且存在于update则在update更新此)
   * @param {*} data
   */
  update(data) {
    const hasIndex = this.addData.findIndex((d) => d[this.primaryId] === data[this.primaryId]);
    const hasIndexInCurrent = this.currentData.findIndex(
      (d) => d[this.primaryId] === data[this.primaryId],
    );
    const hasIndexInUpdate = this.updateData.findIndex(
      (d) => d[this.primaryId] === data[this.primaryId],
    );
    //当存在于adddata则在adddata更新
    if (hasIndex >= 0) Vue.set(this.addData, hasIndex, data);
    //当存在于currentdata，且不存在于updatedata则push进来
    else if (hasIndexInCurrent >= 0 && hasIndexInUpdate < 0) this.updateData.push(data);
    //当不存在于currentdata，且存在于updatedata则在updatedata中更新
    else if (hasIndexInCurrent >= 0 && hasIndexInUpdate >= 0)
      Vue.set(this.updateData, hasIndexInUpdate, data);
  }

  empty(type) {
    switch (type) {
      case "delete":
        this.deleteData.splice(0);
        break;
      case "add":
        this.addData.map((a) => this.currentData.push(a));
        this.addData.splice(0);
        break;
      case "update":
        this.updateData.splice(0);
        break;
    }
  }
}

// 多个资源
// {
//   'sdf-asbqa-56-id1':new Opt(),
//   'vsf-vsfss-fa-id2':new Opt(),
// }
