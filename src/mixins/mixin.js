export default {
  provide() {
    return {
      emitCheckSaveResourceData: this.emitCheckSaveResourceData,
    };
  },
  data() {
    return {
      filterParams: null,
      pageParam: { limit: 999, pageIndex: 1 },

      dataSource: [],
    };
  },

  methods: {
    emitCheckSaveResourceData() {
      let promise = {};
      this.onInsertDataAction(promise);
      this.onDeleteDataAction(promise);
      this.onUpdateDataAction(promise);
      return Promise.all(Object.values(promise));
    },

    async onInsertDataAction(promise) {
      let layerList = this.options.layerList;
      const loadedResourceStore = this.$store.getters["loadedResourceStore"];
      const hasInsertData = layerList.find((layer) => layer.stack.addData.length > 0);
      if (hasInsertData) {
        layerList.map((layer) => {
          if (layer.stack.addData.length > 0) {
            const resourceId = layer.resourceId;
            const currenResourceStore = loadedResourceStore[resourceId]; //获取指定resourceid的store
            promise["add-" + layer.id] = currenResourceStore.dispatch("data/addData", {
              values: JSON.parse(JSON.stringify(layer.stack.addData)),
            });
            //清空stack
            layer.stack.empty("add");
          }
        });
      }
    },

    async onDeleteDataAction(promise) {
      let layerList = this.options.layerList;
      const loadedResourceStore = this.$store.getters["loadedResourceStore"];
      // delData	删除数据	{ primaryIds: string[] } 主键ID数组
      const hasDeleteData = layerList.find((layer) => layer.stack.deleteData.length > 0);
      if (hasDeleteData) {
        layerList.map((layer) => {
          if (layer.stack.deleteData.length > 0) {
            const resourceId = layer.resourceId;
            const currenResourceStore = loadedResourceStore[resourceId]; //获取指定resourceid的store
            promise["delData-" + layer.id] = currenResourceStore.dispatch("data/delData", {
              primaryIds: JSON.parse(JSON.stringify(layer.stack.deleteData)),
            });
            //清空stack
            layer.stack.empty("delete");
          }
        });
      } else {
        return Promise.resolve();
      }
    },
    async onUpdateDataAction(promise) {
      let layerList = this.options.layerList;
      const loadedResourceStore = this.$store.getters["loadedResourceStore"];
      // updateData	更新数据	{ values: object[] } 行数据对象
      const hasUpdateData = layerList.find((layer) => layer.stack.updateData.length > 0);
      if (hasUpdateData) {
        layerList.map((layer) => {
          if (layer.stack.updateData.length > 0) {
            const resourceId = layer.resourceId;
            const currenResourceStore = loadedResourceStore[resourceId]; //获取指定resourceid的store
            promise["updateData-" + layer.id] = currenResourceStore.dispatch("data/updateData", {
              values: JSON.parse(JSON.stringify(layer.stack.updateData)),
            });
            //清空stack
            layer.stack.empty("update");
          }
        });
      } else {
        return Promise.resolve();
      }
    },
  },
};
