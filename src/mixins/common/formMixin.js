/**
 * 表单通用 Mixin
 * 抽取表单组件的通用逻辑
 */

import { reactive, watch } from "vue";
import { merge, cloneDeep } from "lodash-es";
import ErrorCenter, { ERROR_TYPES } from "@/utils/ErrorCenter.js";

export const formMixin = {
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    validateOnChange: {
      type: Boolean,
      default: true
    }
  },

  emits: ['change', 'validate', 'reset'],

  data() {
    return {
      formData: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        form: {}
      },
      validationErrors: {},
      isValidating: false
    };
  },

  computed: {
    /**
     * 是否有验证错误
     */
    hasErrors() {
      return Object.keys(this.validationErrors).length > 0;
    },

    /**
     * 表单是否有效
     */
    isValid() {
      return !this.hasErrors && !this.isValidating;
    }
  },

  watch: {
    config: {
      handler(newConfig) {
        this.updateFormData(newConfig);
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    /**
     * 更新表单数据
     * @param {Object} config 配置对象
     */
    updateFormData(config) {
      try {
        if (this.initFormData && typeof this.initFormData === 'function') {
          this.formData.form = this.initFormData(this.formData.form, config);
        } else {
          this.formData.form = merge(cloneDeep(this.formData.form), config);
        }
      } catch (error) {
        ErrorCenter.handle(error, 'formMixin.updateFormData');
      }
    },

    /**
     * 处理表单字段变化
     * @param {String} key 字段名
     * @param {*} value 字段值
     */
    handleFieldChange(key, value) {
      try {
        // 更新表单数据
        this.formData.form[key] = value;

        // 清除该字段的验证错误
        if (this.validationErrors[key]) {
          this.$delete(this.validationErrors, key);
        }

        // 实时验证
        if (this.validateOnChange) {
          this.validateField(key, value);
        }

        // 触发变化事件
        this.$emit('change', { [key]: value });
      } catch (error) {
        ErrorCenter.handle(error, 'formMixin.handleFieldChange', { key, value });
      }
    },

    /**
     * 验证单个字段
     * @param {String} key 字段名
     * @param {*} value 字段值
     */
    validateField(key, value) {
      if (!this.fieldValidators || !this.fieldValidators[key]) {
        return true;
      }

      const validator = this.fieldValidators[key];
      const result = validator(value, this.formData.form);

      if (result === true) {
        // 验证通过
        if (this.validationErrors[key]) {
          this.$delete(this.validationErrors, key);
        }
        return true;
      } else {
        // 验证失败
        this.$set(this.validationErrors, key, result);
        return false;
      }
    },

    /**
     * 验证整个表单
     * @returns {Boolean} 验证结果
     */
    async validateForm() {
      this.isValidating = true;
      this.validationErrors = {};

      try {
        if (this.fieldValidators) {
          for (const [key, validator] of Object.entries(this.fieldValidators)) {
            const value = this.formData.form[key];
            const result = validator(value, this.formData.form);
            
            if (result !== true) {
              this.validationErrors[key] = result;
            }
          }
        }

        // 自定义验证逻辑
        if (this.customValidate && typeof this.customValidate === 'function') {
          const customErrors = await this.customValidate(this.formData.form);
          if (customErrors && Object.keys(customErrors).length > 0) {
            Object.assign(this.validationErrors, customErrors);
          }
        }

        const isValid = Object.keys(this.validationErrors).length === 0;
        this.$emit('validate', { isValid, errors: this.validationErrors });
        
        return isValid;
      } catch (error) {
        ErrorCenter.handle(error, 'formMixin.validateForm');
        return false;
      } finally {
        this.isValidating = false;
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      try {
        this.formData.form = this.getDefaultFormData ? this.getDefaultFormData() : {};
        this.validationErrors = {};
        this.$emit('reset');
      } catch (error) {
        ErrorCenter.handle(error, 'formMixin.resetForm');
      }
    },

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData() {
      return cloneDeep(this.formData.form);
    },

    /**
     * 设置表单数据
     * @param {Object} data 表单数据
     */
    setFormData(data) {
      this.formData.form = merge(cloneDeep(this.formData.form), data);
    },

    /**
     * 获取字段错误信息
     * @param {String} key 字段名
     * @returns {String} 错误信息
     */
    getFieldError(key) {
      return this.validationErrors[key] || '';
    },

    /**
     * 设置字段错误信息
     * @param {String} key 字段名
     * @param {String} error 错误信息
     */
    setFieldError(key, error) {
      if (error) {
        this.$set(this.validationErrors, key, error);
      } else {
        this.$delete(this.validationErrors, key);
      }
    },

    /**
     * 清除所有错误
     */
    clearErrors() {
      this.validationErrors = {};
    }
  }
};

/**
 * 样式配置表单 Mixin
 * 专门用于样式配置相关的表单
 */
export const styleFormMixin = {
  mixins: [formMixin],

  methods: {
    /**
     * 处理样式变化
     * @param {String} key 样式属性名
     * @param {*} value 样式值
     */
    handleStyleChange(key, value) {
      this.handleFieldChange(key, value);
      
      // 触发样式更新事件
      this.$emit('styleChange', { [key]: value });
    },

    /**
     * 批量更新样式
     * @param {Object} styles 样式对象
     */
    updateStyles(styles) {
      Object.keys(styles).forEach(key => {
        this.formData.form[key] = styles[key];
      });
      
      this.$emit('change', styles);
      this.$emit('styleChange', styles);
    },

    /**
     * 重置样式为默认值
     */
    resetToDefault() {
      if (this.getDefaultStyles && typeof this.getDefaultStyles === 'function') {
        const defaultStyles = this.getDefaultStyles();
        this.updateStyles(defaultStyles);
      } else {
        this.resetForm();
      }
    }
  }
};

/**
 * 创建表单配置 Hook (Vue 3 Composition API)
 * @param {Object} baseForm 基础表单数据
 * @param {Object} options 选项
 * @returns {Object} 表单相关的响应式数据和方法
 */
export function useFormConfig(baseForm = {}, options = {}) {
  const { props, emit, initCallback } = options;

  // 表单相关数据
  const formData = reactive({
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    form: cloneDeep(baseForm)
  });

  const validationErrors = reactive({});

  // 监听配置变化
  if (props) {
    watch(
      () => props.config,
      (config) => {
        try {
          if (initCallback && typeof initCallback === 'function') {
            formData.form = initCallback(formData.form, config);
          } else {
            formData.form = merge(cloneDeep(formData.form), config);
          }
        } catch (error) {
          ErrorCenter.handle(error, 'useFormConfig.watch');
        }
      },
      {
        immediate: true,
        deep: true
      }
    );
  }

  /**
   * 处理字段变化
   * @param {String} key 字段名
   * @param {*} value 字段值
   */
  function handleFieldChange(key, value) {
    formData.form[key] = value;
    
    if (emit) {
      emit('change', { [key]: value });
    }
  }

  /**
   * 批量更新表单
   * @param {Object} updates 更新数据
   */
  function updateForm(updates) {
    Object.assign(formData.form, updates);
    
    if (emit) {
      emit('change', updates);
    }
  }

  return {
    formData,
    validationErrors,
    handleFieldChange,
    updateForm
  };
}

export default {
  formMixin,
  styleFormMixin,
  useFormConfig
};
