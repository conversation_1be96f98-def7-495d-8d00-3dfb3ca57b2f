/**
 * 组件通信通用 Mixin
 * 统一组件间的通信规范和模式
 */

import { globalResourceManager } from "@/utils/ResourceManager.js";
import ErrorCenter from "@/utils/ErrorCenter.js";

export const communicationMixin = {
  data() {
    return {
      eventListeners: [],
      subscriptions: []
    };
  },

  beforeDestroy() {
    this.cleanupCommunication();
  },

  methods: {
    /**
     * 发送事件到父组件
     * @param {String} eventName 事件名
     * @param {*} payload 事件数据
     */
    emitToParent(eventName, payload) {
      try {
        this.$emit(eventName, payload);
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.emitToParent[${eventName}]`);
      }
    },

    /**
     * 发送事件到全局事件总线
     * @param {String} eventName 事件名
     * @param {*} payload 事件数据
     */
    emitGlobal(eventName, payload) {
      try {
        this.$root.$emit(eventName, payload);
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.emitGlobal[${eventName}]`);
      }
    },

    /**
     * 监听全局事件
     * @param {String} eventName 事件名
     * @param {Function} handler 处理函数
     */
    listenGlobal(eventName, handler) {
      try {
        this.$root.$on(eventName, handler);
        this.eventListeners.push({ target: this.$root, event: eventName, handler });
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.listenGlobal[${eventName}]`);
      }
    },

    /**
     * 监听父组件事件
     * @param {String} eventName 事件名
     * @param {Function} handler 处理函数
     */
    listenParent(eventName, handler) {
      try {
        if (this.$parent) {
          this.$parent.$on(eventName, handler);
          this.eventListeners.push({ target: this.$parent, event: eventName, handler });
        }
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.listenParent[${eventName}]`);
      }
    },

    /**
     * 向子组件广播事件
     * @param {String} eventName 事件名
     * @param {*} payload 事件数据
     */
    broadcastToChildren(eventName, payload) {
      try {
        this.$children.forEach(child => {
          if (child.$emit) {
            child.$emit(eventName, payload);
          }
        });
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.broadcastToChildren[${eventName}]`);
      }
    },

    /**
     * 通过 ref 调用子组件方法
     * @param {String} refName ref 名称
     * @param {String} methodName 方法名
     * @param {...*} args 参数
     */
    callChildMethod(refName, methodName, ...args) {
      try {
        const child = this.$refs[refName];
        if (child && typeof child[methodName] === 'function') {
          return child[methodName](...args);
        } else {
          console.warn(`Child component ${refName} or method ${methodName} not found`);
        }
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.callChildMethod[${refName}.${methodName}]`);
      }
    },

    /**
     * 通过 provide/inject 提供数据
     * @param {String} key 键名
     * @param {*} value 值
     */
    provideData(key, value) {
      if (!this._provided) {
        this._provided = {};
      }
      this._provided[key] = value;
    },

    /**
     * 订阅 Vuex store 变化
     * @param {String} path 状态路径
     * @param {Function} handler 处理函数
     */
    subscribeStore(path, handler) {
      try {
        const unsubscribe = this.$store.watch(
          (state) => this.getNestedValue(state, path),
          handler,
          { deep: true }
        );
        this.subscriptions.push(unsubscribe);
        return unsubscribe;
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.subscribeStore[${path}]`);
      }
    },

    /**
     * 提交 Vuex mutation
     * @param {String} type mutation 类型
     * @param {*} payload 载荷
     */
    commitMutation(type, payload) {
      try {
        this.$store.commit(type, payload);
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.commitMutation[${type}]`);
      }
    },

    /**
     * 分发 Vuex action
     * @param {String} type action 类型
     * @param {*} payload 载荷
     */
    async dispatchAction(type, payload) {
      try {
        return await this.$store.dispatch(type, payload);
      } catch (error) {
        ErrorCenter.handle(error, `communicationMixin.dispatchAction[${type}]`);
        throw error;
      }
    },

    /**
     * 获取嵌套对象的值
     * @param {Object} obj 对象
     * @param {String} path 路径
     * @returns {*} 值
     */
    getNestedValue(obj, path) {
      return path.split('.').reduce((current, key) => current && current[key], obj);
    },

    /**
     * 清理通信相关资源
     */
    cleanupCommunication() {
      // 清理事件监听器
      this.eventListeners.forEach(({ target, event, handler }) => {
        try {
          if (target && target.$off) {
            target.$off(event, handler);
          }
        } catch (error) {
          console.warn('Error removing event listener:', error);
        }
      });
      this.eventListeners = [];

      // 清理订阅
      this.subscriptions.forEach(unsubscribe => {
        try {
          if (typeof unsubscribe === 'function') {
            unsubscribe();
          }
        } catch (error) {
          console.warn('Error unsubscribing:', error);
        }
      });
      this.subscriptions = [];
    }
  }
};

/**
 * 表单通信 Mixin
 * 专门处理表单组件的通信模式
 */
export const formCommunicationMixin = {
  mixins: [communicationMixin],

  props: {
    value: {
      default: undefined
    }
  },

  model: {
    prop: 'value',
    event: 'input'
  },

  emits: ['input', 'change', 'focus', 'blur', 'validate'],

  computed: {
    /**
     * 内部值
     */
    internalValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.updateValue(val);
      }
    }
  },

  methods: {
    /**
     * 更新值
     * @param {*} value 新值
     */
    updateValue(value) {
      if (value !== this.value) {
        this.$emit('input', value);
        this.$emit('change', value);
      }
    },

    /**
     * 处理焦点事件
     * @param {Event} event 事件对象
     */
    handleFocus(event) {
      this.$emit('focus', event);
    },

    /**
     * 处理失焦事件
     * @param {Event} event 事件对象
     */
    handleBlur(event) {
      this.$emit('blur', event);
    },

    /**
     * 触发验证
     * @param {*} value 要验证的值
     */
    triggerValidation(value = this.value) {
      this.$emit('validate', value);
    }
  }
};

/**
 * 弹窗通信 Mixin
 * 处理弹窗组件的通信模式
 */
export const modalCommunicationMixin = {
  mixins: [communicationMixin],

  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:visible', 'open', 'close', 'confirm', 'cancel'],

  computed: {
    /**
     * 内部可见状态
     */
    internalVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.updateVisible(val);
      }
    }
  },

  watch: {
    visible: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (newVal) {
            this.handleOpen();
          } else {
            this.handleClose();
          }
        }
      },
      immediate: true
    }
  },

  methods: {
    /**
     * 更新可见状态
     * @param {Boolean} visible 可见状态
     */
    updateVisible(visible) {
      this.$emit('update:visible', visible);
    },

    /**
     * 打开弹窗
     */
    open() {
      this.updateVisible(true);
    },

    /**
     * 关闭弹窗
     */
    close() {
      this.updateVisible(false);
    },

    /**
     * 处理打开事件
     */
    handleOpen() {
      this.$emit('open');
    },

    /**
     * 处理关闭事件
     */
    handleClose() {
      this.$emit('close');
    },

    /**
     * 确认操作
     * @param {*} data 确认数据
     */
    confirm(data) {
      this.$emit('confirm', data);
      this.close();
    },

    /**
     * 取消操作
     */
    cancel() {
      this.$emit('cancel');
      this.close();
    }
  }
};

/**
 * 列表通信 Mixin
 * 处理列表组件的通信模式
 */
export const listCommunicationMixin = {
  mixins: [communicationMixin],

  emits: ['select', 'select-change', 'item-click', 'item-double-click', 'load-more'],

  data() {
    return {
      selectedItems: [],
      selectedKeys: []
    };
  },

  methods: {
    /**
     * 选择项目
     * @param {*} item 项目
     * @param {Boolean} selected 是否选中
     */
    selectItem(item, selected = true) {
      const key = this.getItemKey(item);
      
      if (selected) {
        if (!this.selectedKeys.includes(key)) {
          this.selectedItems.push(item);
          this.selectedKeys.push(key);
        }
      } else {
        const index = this.selectedKeys.indexOf(key);
        if (index > -1) {
          this.selectedItems.splice(index, 1);
          this.selectedKeys.splice(index, 1);
        }
      }
      
      this.$emit('select', item, selected);
      this.$emit('select-change', this.selectedItems, this.selectedKeys);
    },

    /**
     * 清除选择
     */
    clearSelection() {
      this.selectedItems = [];
      this.selectedKeys = [];
      this.$emit('select-change', this.selectedItems, this.selectedKeys);
    },

    /**
     * 全选
     * @param {Array} items 所有项目
     */
    selectAll(items) {
      this.selectedItems = [...items];
      this.selectedKeys = items.map(item => this.getItemKey(item));
      this.$emit('select-change', this.selectedItems, this.selectedKeys);
    },

    /**
     * 获取项目键值
     * @param {*} item 项目
     * @returns {String} 键值
     */
    getItemKey(item) {
      return item.id || item.key || JSON.stringify(item);
    },

    /**
     * 处理项目点击
     * @param {*} item 项目
     * @param {Event} event 事件对象
     */
    handleItemClick(item, event) {
      this.$emit('item-click', item, event);
    },

    /**
     * 处理项目双击
     * @param {*} item 项目
     * @param {Event} event 事件对象
     */
    handleItemDoubleClick(item, event) {
      this.$emit('item-double-click', item, event);
    },

    /**
     * 加载更多
     */
    loadMore() {
      this.$emit('load-more');
    }
  }
};

export default {
  communicationMixin,
  formCommunicationMixin,
  modalCommunicationMixin,
  listCommunicationMixin
};
