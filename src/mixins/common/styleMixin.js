/**
 * 样式和交互规范 Mixin
 * 统一组件的样式和交互行为
 */

export const styleMixin = {
  props: {
    size: {
      type: String,
      default: 'default',
      validator: value => ['small', 'default', 'large'].includes(value)
    },
    theme: {
      type: String,
      default: 'default',
      validator: value => ['default', 'dark', 'light'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    /**
     * 组件基础类名
     */
    baseClasses() {
      return [
        this.componentName || 'component',
        `${this.componentName || 'component'}--${this.size}`,
        `${this.componentName || 'component'}--${this.theme}`,
        {
          [`${this.componentName || 'component'}--disabled`]: this.disabled,
          [`${this.componentName || 'component'}--loading`]: this.loading
        }
      ];
    },

    /**
     * 组件样式对象
     */
    componentStyles() {
      return {
        ...this.baseStyles,
        ...this.customStyles
      };
    },

    /**
     * 基础样式
     */
    baseStyles() {
      const styles = {};
      
      // 尺寸相关样式
      switch (this.size) {
        case 'small':
          styles.fontSize = '12px';
          styles.padding = '4px 8px';
          break;
        case 'large':
          styles.fontSize = '16px';
          styles.padding = '12px 16px';
          break;
        default:
          styles.fontSize = '14px';
          styles.padding = '8px 12px';
      }
      
      // 主题相关样式
      switch (this.theme) {
        case 'dark':
          styles.backgroundColor = '#1f1f1f';
          styles.color = '#ffffff';
          break;
        case 'light':
          styles.backgroundColor = '#ffffff';
          styles.color = '#000000';
          break;
        default:
          styles.backgroundColor = '#f5f5f5';
          styles.color = '#333333';
      }
      
      // 状态相关样式
      if (this.disabled) {
        styles.opacity = 0.6;
        styles.cursor = 'not-allowed';
      }
      
      if (this.loading) {
        styles.cursor = 'wait';
      }
      
      return styles;
    },

    /**
     * 自定义样式（子组件可重写）
     */
    customStyles() {
      return {};
    }
  },

  methods: {
    /**
     * 获取 BEM 类名
     * @param {String} element 元素名
     * @param {String} modifier 修饰符
     * @returns {String} 类名
     */
    bem(element, modifier) {
      const base = this.componentName || 'component';
      let className = base;
      
      if (element) {
        className += `__${element}`;
      }
      
      if (modifier) {
        className += `--${modifier}`;
      }
      
      return className;
    },

    /**
     * 合并类名
     * @param {...String|Array|Object} classes 类名
     * @returns {Array} 合并后的类名数组
     */
    mergeClasses(...classes) {
      const result = [];
      
      classes.forEach(cls => {
        if (typeof cls === 'string') {
          result.push(cls);
        } else if (Array.isArray(cls)) {
          result.push(...cls);
        } else if (cls && typeof cls === 'object') {
          Object.keys(cls).forEach(key => {
            if (cls[key]) {
              result.push(key);
            }
          });
        }
      });
      
      return result;
    }
  }
};

/**
 * 响应式设计 Mixin
 */
export const responsiveMixin = {
  data() {
    return {
      screenSize: 'desktop',
      windowWidth: 0,
      windowHeight: 0
    };
  },

  computed: {
    /**
     * 是否为移动端
     */
    isMobile() {
      return this.screenSize === 'mobile';
    },

    /**
     * 是否为平板
     */
    isTablet() {
      return this.screenSize === 'tablet';
    },

    /**
     * 是否为桌面端
     */
    isDesktop() {
      return this.screenSize === 'desktop';
    },

    /**
     * 响应式类名
     */
    responsiveClasses() {
      return [
        `screen-${this.screenSize}`,
        {
          'is-mobile': this.isMobile,
          'is-tablet': this.isTablet,
          'is-desktop': this.isDesktop
        }
      ];
    }
  },

  mounted() {
    this.updateScreenSize();
    window.addEventListener('resize', this.handleResize);
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },

  methods: {
    /**
     * 处理窗口大小变化
     */
    handleResize() {
      this.updateScreenSize();
    },

    /**
     * 更新屏幕尺寸
     */
    updateScreenSize() {
      this.windowWidth = window.innerWidth;
      this.windowHeight = window.innerHeight;
      
      if (this.windowWidth < 768) {
        this.screenSize = 'mobile';
      } else if (this.windowWidth < 1024) {
        this.screenSize = 'tablet';
      } else {
        this.screenSize = 'desktop';
      }
    }
  }
};

/**
 * 动画效果 Mixin
 */
export const animationMixin = {
  props: {
    animated: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 300
    },
    animationType: {
      type: String,
      default: 'fade',
      validator: value => ['fade', 'slide', 'zoom', 'bounce'].includes(value)
    }
  },

  computed: {
    /**
     * 动画样式
     */
    animationStyles() {
      if (!this.animated) return {};
      
      return {
        transition: `all ${this.animationDuration}ms ease-in-out`
      };
    },

    /**
     * 动画类名
     */
    animationClasses() {
      if (!this.animated) return [];
      
      return [
        'animated',
        `animation-${this.animationType}`
      ];
    }
  },

  methods: {
    /**
     * 执行进入动画
     * @param {Element} element 元素
     */
    enterAnimation(element) {
      if (!this.animated) return;
      
      element.classList.add('animation-enter');
      
      setTimeout(() => {
        element.classList.remove('animation-enter');
        element.classList.add('animation-enter-active');
      }, 10);
      
      setTimeout(() => {
        element.classList.remove('animation-enter-active');
      }, this.animationDuration);
    },

    /**
     * 执行离开动画
     * @param {Element} element 元素
     * @param {Function} callback 完成回调
     */
    leaveAnimation(element, callback) {
      if (!this.animated) {
        callback && callback();
        return;
      }
      
      element.classList.add('animation-leave');
      
      setTimeout(() => {
        element.classList.remove('animation-leave');
        element.classList.add('animation-leave-active');
      }, 10);
      
      setTimeout(() => {
        element.classList.remove('animation-leave-active');
        callback && callback();
      }, this.animationDuration);
    }
  }
};

/**
 * 主题切换 Mixin
 */
export const themeMixin = {
  inject: {
    themeProvider: {
      from: 'themeProvider',
      default: null
    }
  },

  computed: {
    /**
     * 当前主题
     */
    currentTheme() {
      return this.themeProvider?.theme || this.theme || 'default';
    },

    /**
     * 主题变量
     */
    themeVars() {
      const themes = {
        default: {
          primaryColor: '#1890ff',
          successColor: '#52c41a',
          warningColor: '#faad14',
          errorColor: '#f5222d',
          textColor: '#333333',
          backgroundColor: '#ffffff',
          borderColor: '#d9d9d9'
        },
        dark: {
          primaryColor: '#177ddc',
          successColor: '#49aa19',
          warningColor: '#d89614',
          errorColor: '#dc4446',
          textColor: '#ffffff',
          backgroundColor: '#1f1f1f',
          borderColor: '#434343'
        }
      };
      
      return themes[this.currentTheme] || themes.default;
    },

    /**
     * 主题样式
     */
    themeStyles() {
      return {
        '--primary-color': this.themeVars.primaryColor,
        '--success-color': this.themeVars.successColor,
        '--warning-color': this.themeVars.warningColor,
        '--error-color': this.themeVars.errorColor,
        '--text-color': this.themeVars.textColor,
        '--background-color': this.themeVars.backgroundColor,
        '--border-color': this.themeVars.borderColor
      };
    }
  },

  methods: {
    /**
     * 切换主题
     * @param {String} theme 主题名
     */
    switchTheme(theme) {
      if (this.themeProvider && this.themeProvider.switchTheme) {
        this.themeProvider.switchTheme(theme);
      }
    }
  }
};

/**
 * 无障碍访问 Mixin
 */
export const accessibilityMixin = {
  props: {
    ariaLabel: {
      type: String,
      default: ''
    },
    ariaDescribedBy: {
      type: String,
      default: ''
    },
    tabIndex: {
      type: [Number, String],
      default: 0
    }
  },

  computed: {
    /**
     * 无障碍属性
     */
    accessibilityAttrs() {
      const attrs = {};
      
      if (this.ariaLabel) {
        attrs['aria-label'] = this.ariaLabel;
      }
      
      if (this.ariaDescribedBy) {
        attrs['aria-describedby'] = this.ariaDescribedBy;
      }
      
      if (this.disabled) {
        attrs['aria-disabled'] = 'true';
      }
      
      if (!this.disabled) {
        attrs['tabindex'] = this.tabIndex;
      }
      
      return attrs;
    }
  },

  methods: {
    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event 键盘事件
     */
    handleKeydown(event) {
      if (this.disabled) return;
      
      switch (event.key) {
        case 'Enter':
        case ' ':
          this.handleActivate(event);
          break;
        case 'Escape':
          this.handleEscape(event);
          break;
      }
    },

    /**
     * 处理激活事件（Enter 或 Space）
     * @param {KeyboardEvent} event 键盘事件
     */
    handleActivate(event) {
      // 子组件可重写此方法
      this.$emit('activate', event);
    },

    /**
     * 处理 Escape 事件
     * @param {KeyboardEvent} event 键盘事件
     */
    handleEscape(event) {
      // 子组件可重写此方法
      this.$emit('escape', event);
    }
  }
};

export default {
  styleMixin,
  responsiveMixin,
  animationMixin,
  themeMixin,
  accessibilityMixin
};
