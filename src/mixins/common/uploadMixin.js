/**
 * 文件上传通用 Mixin
 * 抽取文件上传相关的通用逻辑
 */

import FileService from "@/utils/file-service";
import ErrorCenter, { ERROR_TYPES } from "@/utils/ErrorCenter.js";

export const uploadMixin = {
  props: {
    accept: {
      type: String,
      default: '*'
    },
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    },
    multiple: {
      type: Boolean,
      default: false
    },
    autoUpload: {
      type: Boolean,
      default: true
    }
  },

  emits: ['upload-start', 'upload-progress', 'upload-success', 'upload-error', 'file-change'],

  data() {
    return {
      uploading: false,
      uploadProgress: 0,
      uploadedFiles: []
    };
  },

  computed: {
    /**
     * 是否可以上传
     */
    canUpload() {
      return !this.uploading;
    }
  },

  methods: {
    /**
     * 处理文件上传
     * @param {File|FileList} files 文件对象
     */
    async handleUpload(files) {
      try {
        if (!files) return;

        const fileList = Array.isArray(files) ? files : [files];
        
        // 验证文件
        const validationResult = this.validateFiles(fileList);
        if (!validationResult.valid) {
          throw new Error(validationResult.message);
        }

        this.uploading = true;
        this.uploadProgress = 0;
        this.$emit('upload-start', fileList);

        const results = [];

        for (let i = 0; i < fileList.length; i++) {
          const file = fileList[i];
          
          try {
            // 上传单个文件
            const result = await this.uploadSingleFile(file, i, fileList.length);
            results.push(result);
            
            // 更新进度
            this.uploadProgress = Math.round(((i + 1) / fileList.length) * 100);
            this.$emit('upload-progress', this.uploadProgress);
          } catch (error) {
            results.push({ file, error: error.message, success: false });
          }
        }

        this.uploadedFiles = results.filter(r => r.success);
        this.$emit('upload-success', results);
        this.$emit('file-change', this.uploadedFiles);

        return results;
      } catch (error) {
        this.$emit('upload-error', error);
        ErrorCenter.handle(error, 'uploadMixin.handleUpload');
        throw error;
      } finally {
        this.uploading = false;
        this.uploadProgress = 0;
      }
    },

    /**
     * 上传单个文件
     * @param {File} file 文件对象
     * @param {Number} index 文件索引
     * @param {Number} total 总文件数
     * @returns {Object} 上传结果
     */
    async uploadSingleFile(file, index, total) {
      try {
        // 预处理文件
        const processedFile = await this.preprocessFile(file);
        
        // 执行上传
        const uploadResult = await FileService.uploadFile(processedFile);
        
        // 后处理结果
        const result = await this.postprocessResult(uploadResult, file);
        
        return {
          file,
          result,
          success: true,
          index,
          total
        };
      } catch (error) {
        ErrorCenter.handle(error, 'uploadMixin.uploadSingleFile', { fileName: file.name });
        throw error;
      }
    },

    /**
     * 验证文件
     * @param {Array} files 文件数组
     * @returns {Object} 验证结果
     */
    validateFiles(files) {
      const errors = [];

      // 检查文件数量
      if (!this.multiple && files.length > 1) {
        errors.push('只能上传一个文件');
      }

      // 检查每个文件
      files.forEach((file, index) => {
        // 检查文件大小
        if (file.size > this.maxSize) {
          errors.push(`文件 ${file.name} 大小超过限制 (${this.formatFileSize(this.maxSize)})`);
        }

        // 检查文件类型
        if (this.accept !== '*' && !this.isAcceptedFileType(file)) {
          errors.push(`文件 ${file.name} 类型不支持`);
        }

        // 自定义验证
        if (this.customFileValidation) {
          const customResult = this.customFileValidation(file, index);
          if (customResult !== true) {
            errors.push(customResult);
          }
        }
      });

      return {
        valid: errors.length === 0,
        errors,
        message: errors.join('; ')
      };
    },

    /**
     * 检查文件类型是否被接受
     * @param {File} file 文件对象
     * @returns {Boolean} 是否被接受
     */
    isAcceptedFileType(file) {
      if (this.accept === '*') return true;

      const acceptTypes = this.accept.split(',').map(type => type.trim());
      
      return acceptTypes.some(acceptType => {
        if (acceptType.startsWith('.')) {
          // 扩展名匹配
          return file.name.toLowerCase().endsWith(acceptType.toLowerCase());
        } else {
          // MIME 类型匹配
          return file.type.match(new RegExp(acceptType.replace('*', '.*')));
        }
      });
    },

    /**
     * 预处理文件
     * @param {File} file 文件对象
     * @returns {File} 处理后的文件
     */
    async preprocessFile(file) {
      // 子类可以重写此方法进行文件预处理
      return file;
    },

    /**
     * 后处理上传结果
     * @param {Object} uploadResult 上传结果
     * @param {File} originalFile 原始文件
     * @returns {Object} 处理后的结果
     */
    async postprocessResult(uploadResult, originalFile) {
      // 子类可以重写此方法进行结果后处理
      return uploadResult;
    },

    /**
     * 格式化文件大小
     * @param {Number} bytes 字节数
     * @returns {String} 格式化后的大小
     */
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 删除已上传的文件
     * @param {String} fileId 文件ID
     */
    async deleteUploadedFile(fileId) {
      try {
        await FileService.deleteFile(fileId);
        this.uploadedFiles = this.uploadedFiles.filter(f => f.result.id !== fileId);
        this.$emit('file-change', this.uploadedFiles);
      } catch (error) {
        ErrorCenter.handle(error, 'uploadMixin.deleteUploadedFile', { fileId });
        throw error;
      }
    },

    /**
     * 清除所有已上传的文件
     */
    clearUploadedFiles() {
      this.uploadedFiles = [];
      this.$emit('file-change', this.uploadedFiles);
    },

    /**
     * 重新上传失败的文件
     * @param {Array} failedFiles 失败的文件数组
     */
    async retryFailedUploads(failedFiles) {
      const filesToRetry = failedFiles.map(f => f.file);
      return await this.handleUpload(filesToRetry);
    }
  }
};

/**
 * JSON 文件上传 Mixin
 * 专门处理 JSON 文件的上传和解析
 */
export const jsonUploadMixin = {
  mixins: [uploadMixin],

  props: {
    accept: {
      type: String,
      default: '.json,application/json'
    }
  },

  methods: {
    /**
     * 预处理 JSON 文件
     * @param {File} file 文件对象
     * @returns {File} 处理后的文件
     */
    async preprocessFile(file) {
      // 验证 JSON 格式
      const content = await this.readFileContent(file);
      
      try {
        JSON.parse(content);
      } catch (error) {
        throw new Error(`文件 ${file.name} 不是有效的 JSON 格式`);
      }
      
      return file;
    },

    /**
     * 后处理 JSON 上传结果
     * @param {Object} uploadResult 上传结果
     * @param {File} originalFile 原始文件
     * @returns {Object} 处理后的结果
     */
    async postprocessResult(uploadResult, originalFile) {
      // 读取并解析 JSON 内容
      const fileInfo = await FileService.getFileInfo(uploadResult.id);
      
      return {
        ...uploadResult,
        parsedContent: fileInfo.content ? JSON.parse(fileInfo.content) : null,
        originalName: originalFile.name
      };
    },

    /**
     * 读取文件内容
     * @param {File} file 文件对象
     * @returns {Promise<String>} 文件内容
     */
    readFileContent(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result);
        reader.onerror = e => reject(new Error('文件读取失败'));
        reader.readAsText(file);
      });
    }
  }
};

/**
 * 图片上传 Mixin
 * 专门处理图片文件的上传和预览
 */
export const imageUploadMixin = {
  mixins: [uploadMixin],

  props: {
    accept: {
      type: String,
      default: 'image/*'
    },
    maxWidth: {
      type: Number,
      default: 1920
    },
    maxHeight: {
      type: Number,
      default: 1080
    },
    quality: {
      type: Number,
      default: 0.8
    }
  },

  methods: {
    /**
     * 预处理图片文件
     * @param {File} file 文件对象
     * @returns {File} 处理后的文件
     */
    async preprocessFile(file) {
      // 压缩图片
      if (this.shouldCompressImage(file)) {
        return await this.compressImage(file);
      }
      
      return file;
    },

    /**
     * 判断是否需要压缩图片
     * @param {File} file 文件对象
     * @returns {Boolean} 是否需要压缩
     */
    shouldCompressImage(file) {
      return file.size > this.maxSize || this.maxWidth < 1920 || this.maxHeight < 1080;
    },

    /**
     * 压缩图片
     * @param {File} file 文件对象
     * @returns {File} 压缩后的文件
     */
    async compressImage(file) {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = () => {
          // 计算压缩后的尺寸
          const { width, height } = this.calculateCompressedSize(img.width, img.height);
          
          canvas.width = width;
          canvas.height = height;
          
          // 绘制压缩后的图片
          ctx.drawImage(img, 0, 0, width, height);
          
          // 转换为 Blob
          canvas.toBlob(resolve, file.type, this.quality);
        };
        
        img.src = URL.createObjectURL(file);
      });
    },

    /**
     * 计算压缩后的尺寸
     * @param {Number} originalWidth 原始宽度
     * @param {Number} originalHeight 原始高度
     * @returns {Object} 压缩后的尺寸
     */
    calculateCompressedSize(originalWidth, originalHeight) {
      let { width, height } = { width: originalWidth, height: originalHeight };
      
      // 按比例缩放
      if (width > this.maxWidth) {
        height = (height * this.maxWidth) / width;
        width = this.maxWidth;
      }
      
      if (height > this.maxHeight) {
        width = (width * this.maxHeight) / height;
        height = this.maxHeight;
      }
      
      return { width: Math.round(width), height: Math.round(height) };
    }
  }
};

export default {
  uploadMixin,
  jsonUploadMixin,
  imageUploadMixin
};
