/**
 * Mixins 统一导出
 * 提供所有通用 mixins 的统一入口
 */

// 表单相关 mixins
export {
  formMixin,
  styleFormMixin,
  useFormConfig
} from './common/formMixin.js';

// 文件上传相关 mixins
export {
  uploadMixin,
  jsonUploadMixin,
  imageUploadMixin
} from './common/uploadMixin.js';

// 组件通信相关 mixins
export {
  communicationMixin,
  formCommunicationMixin,
  modalCommunicationMixin,
  listCommunicationMixin
} from './common/communicationMixin.js';

// 样式和交互相关 mixins
export {
  styleMixin,
  responsiveMixin,
  animationMixin,
  themeMixin,
  accessibilityMixin
} from './common/styleMixin.js';

// 性能优化相关 mixins
export {
  performanceMixin,
  virtualScrollMixin,
  lazyLoadMixin,
  cacheMixin
} from './performance/performanceMixin.js';

/**
 * 组合 Mixin 工厂函数
 * 用于创建包含多个 mixin 的组合 mixin
 */
export function createCombinedMixin(mixins = []) {
  return {
    mixins
  };
}

/**
 * 常用组合 Mixins
 */

// 基础表单组件 mixin
export const baseFormComponentMixin = createCombinedMixin([
  formMixin,
  formCommunicationMixin,
  styleMixin,
  accessibilityMixin
]);

// 样式配置表单 mixin
export const styleConfigFormMixin = createCombinedMixin([
  styleFormMixin,
  formCommunicationMixin,
  styleMixin
]);

// 文件上传组件 mixin
export const fileUploadComponentMixin = createCombinedMixin([
  uploadMixin,
  communicationMixin,
  styleMixin
]);

// JSON 文件上传组件 mixin
export const jsonFileUploadComponentMixin = createCombinedMixin([
  jsonUploadMixin,
  communicationMixin,
  styleMixin
]);

// 图片上传组件 mixin
export const imageUploadComponentMixin = createCombinedMixin([
  imageUploadMixin,
  communicationMixin,
  styleMixin
]);

// 弹窗组件 mixin
export const modalComponentMixin = createCombinedMixin([
  modalCommunicationMixin,
  styleMixin,
  animationMixin,
  accessibilityMixin
]);

// 列表组件 mixin
export const listComponentMixin = createCombinedMixin([
  listCommunicationMixin,
  styleMixin,
  responsiveMixin
]);

// 响应式组件 mixin
export const responsiveComponentMixin = createCombinedMixin([
  responsiveMixin,
  styleMixin,
  themeMixin
]);

/**
 * Mixin 注册器
 * 用于全局注册常用 mixins
 */
export class MixinRegistry {
  static mixins = new Map();

  /**
   * 注册 mixin
   * @param {String} name mixin 名称
   * @param {Object} mixin mixin 对象
   */
  static register(name, mixin) {
    this.mixins.set(name, mixin);
  }

  /**
   * 获取 mixin
   * @param {String} name mixin 名称
   * @returns {Object} mixin 对象
   */
  static get(name) {
    return this.mixins.get(name);
  }

  /**
   * 获取所有 mixin
   * @returns {Map} 所有 mixin
   */
  static getAll() {
    return new Map(this.mixins);
  }

  /**
   * 批量注册 mixins
   * @param {Object} mixins mixin 对象集合
   */
  static registerBatch(mixins) {
    Object.keys(mixins).forEach(name => {
      this.register(name, mixins[name]);
    });
  }

  /**
   * 创建 Vue 插件
   * @returns {Object} Vue 插件对象
   */
  static createPlugin() {
    return {
      install(Vue) {
        // 全局注册所有 mixins
        for (const [name, mixin] of this.mixins) {
          Vue.mixin({
            name: `global-${name}`,
            ...mixin
          });
        }

        // 添加全局方法
        Vue.prototype.$getMixin = (name) => this.get(name);
        Vue.prototype.$getAllMixins = () => this.getAll();
      }
    };
  }
}

// 注册常用 mixins
MixinRegistry.registerBatch({
  form: formMixin,
  styleForm: styleFormMixin,
  upload: uploadMixin,
  jsonUpload: jsonUploadMixin,
  imageUpload: imageUploadMixin,
  communication: communicationMixin,
  formCommunication: formCommunicationMixin,
  modalCommunication: modalCommunicationMixin,
  listCommunication: listCommunicationMixin,
  style: styleMixin,
  responsive: responsiveMixin,
  animation: animationMixin,
  theme: themeMixin,
  accessibility: accessibilityMixin,
  baseFormComponent: baseFormComponentMixin,
  styleConfigForm: styleConfigFormMixin,
  fileUploadComponent: fileUploadComponentMixin,
  jsonFileUploadComponent: jsonFileUploadComponentMixin,
  imageUploadComponent: imageUploadComponentMixin,
  modalComponent: modalComponentMixin,
  listComponent: listComponentMixin,
  responsiveComponent: responsiveComponentMixin
});

/**
 * 默认导出
 */
export default {
  // 基础 mixins
  formMixin,
  styleFormMixin,
  uploadMixin,
  jsonUploadMixin,
  imageUploadMixin,
  communicationMixin,
  formCommunicationMixin,
  modalCommunicationMixin,
  listCommunicationMixin,
  styleMixin,
  responsiveMixin,
  animationMixin,
  themeMixin,
  accessibilityMixin,

  // 组合 mixins
  baseFormComponentMixin,
  styleConfigFormMixin,
  fileUploadComponentMixin,
  jsonFileUploadComponentMixin,
  imageUploadComponentMixin,
  modalComponentMixin,
  listComponentMixin,
  responsiveComponentMixin,

  // 工具函数
  createCombinedMixin,
  useFormConfig,
  MixinRegistry
};
