export default function readyMap() {
  const BMap_URL = "/daas/static/maps/bmap/map_load.js";
  return new Promise((resolve, reject) => {
    try {
      // 如果已加载直接返回
      if (typeof BMap !== "undefined") {
        resolve(BMap);
        return true;
      }

      // 百度地图异步加载回调处理
      window.onBMapCallback = function () {
        console.log("百度地图脚本初始化成功....");
        resolve(BMap);
      };

      // 插入script脚本
      let scriptNode = document.createElement("script");
      scriptNode.setAttribute("type", "text/javascript");
      scriptNode.setAttribute("src", BMap_URL);
      document.body.appendChild(scriptNode);
    } catch (error) {
      reject(`加载地图失败：${error}`);
    }
  });
}
