/**
 * 性能优化 Mixin
 * 为组件提供性能优化相关的功能
 */

import { 
  debounce, 
  throttle, 
  rafThrottle,
  performanceMonitor,
  componentCache 
} from '@/utils/PerformanceOptimizer.js';
import { globalResourceManager } from '@/utils/ResourceManager.js';

export const performanceMixin = {
  data() {
    return {
      performanceMetrics: {},
      isPerformanceMonitoringEnabled: process.env.NODE_ENV === 'development'
    };
  },

  created() {
    if (this.isPerformanceMonitoringEnabled) {
      this.startPerformanceMonitoring();
    }
  },

  beforeDestroy() {
    this.cleanupPerformanceMonitoring();
  },

  methods: {
    /**
     * 创建防抖函数
     * @param {Function} fn 要防抖的函数
     * @param {Number} delay 延迟时间
     * @param {Boolean} immediate 是否立即执行
     * @returns {Function} 防抖后的函数
     */
    createDebounced(fn, delay = 300, immediate = false) {
      const debouncedFn = debounce(fn.bind(this), delay, immediate);
      
      // 在组件销毁时清理
      this.$once('hook:beforeDestroy', () => {
        if (debouncedFn.cancel) {
          debouncedFn.cancel();
        }
      });
      
      return debouncedFn;
    },

    /**
     * 创建节流函数
     * @param {Function} fn 要节流的函数
     * @param {Number} limit 时间限制
     * @returns {Function} 节流后的函数
     */
    createThrottled(fn, limit = 100) {
      return throttle(fn.bind(this), limit);
    },

    /**
     * 创建 RAF 节流函数
     * @param {Function} fn 要节流的函数
     * @returns {Function} 节流后的函数
     */
    createRafThrottled(fn) {
      return rafThrottle(fn.bind(this));
    },

    /**
     * 测量方法执行时间
     * @param {String} name 测量名称
     * @param {Function} fn 要测量的方法
     * @returns {*} 方法返回值
     */
    measurePerformance(name, fn) {
      if (!this.isPerformanceMonitoringEnabled) {
        return fn();
      }

      const fullName = `${this.$options.name || 'Component'}.${name}`;
      return performanceMonitor.measure(fullName, fn);
    },

    /**
     * 测量异步方法执行时间
     * @param {String} name 测量名称
     * @param {Function} fn 要测量的异步方法
     * @returns {Promise} 方法返回值
     */
    async measureAsyncPerformance(name, fn) {
      if (!this.isPerformanceMonitoringEnabled) {
        return await fn();
      }

      const fullName = `${this.$options.name || 'Component'}.${name}`;
      return await performanceMonitor.measureAsync(fullName, fn);
    },

    /**
     * 开始性能监控
     * @private
     */
    startPerformanceMonitoring() {
      const componentName = this.$options.name || 'Component';
      
      // 监控组件创建时间
      performanceMonitor.start(`${componentName}.created`);
      
      this.$nextTick(() => {
        performanceMonitor.end(`${componentName}.created`);
        performanceMonitor.start(`${componentName}.mounted`);
      });
      
      this.$once('hook:mounted', () => {
        performanceMonitor.end(`${componentName}.mounted`);
      });
      
      this.$once('hook:beforeDestroy', () => {
        performanceMonitor.start(`${componentName}.destroyed`);
      });
      
      this.$once('hook:destroyed', () => {
        performanceMonitor.end(`${componentName}.destroyed`);
      });
    },

    /**
     * 清理性能监控
     * @private
     */
    cleanupPerformanceMonitoring() {
      // 清理组件相关的性能指标
      const componentName = this.$options.name || 'Component';
      performanceMonitor.clear(`${componentName}.created`);
      performanceMonitor.clear(`${componentName}.mounted`);
      performanceMonitor.clear(`${componentName}.destroyed`);
    },

    /**
     * 获取性能指标
     * @returns {Object} 性能指标
     */
    getPerformanceMetrics() {
      return performanceMonitor.getAllMetrics();
    },

    /**
     * 延迟执行函数
     * @param {Function} fn 要延迟执行的函数
     * @param {Number} delay 延迟时间
     * @returns {Number} 定时器ID
     */
    delayExecution(fn, delay = 0) {
      return globalResourceManager.setTimeout(fn.bind(this), delay);
    },

    /**
     * 在下一个事件循环中执行
     * @param {Function} fn 要执行的函数
     */
    nextTick(fn) {
      this.$nextTick(fn.bind(this));
    },

    /**
     * 在下一帧执行
     * @param {Function} fn 要执行的函数
     */
    nextFrame(fn) {
      requestAnimationFrame(fn.bind(this));
    }
  }
};

/**
 * 虚拟滚动 Mixin
 */
export const virtualScrollMixin = {
  props: {
    items: {
      type: Array,
      default: () => []
    },
    itemHeight: {
      type: Number,
      default: 50
    },
    containerHeight: {
      type: Number,
      default: 400
    },
    buffer: {
      type: Number,
      default: 5
    }
  },

  data() {
    return {
      scrollTop: 0,
      visibleRange: { start: 0, end: 0 }
    };
  },

  computed: {
    /**
     * 可见项
     */
    visibleItems() {
      return this.items.slice(this.visibleRange.start, this.visibleRange.end);
    },

    /**
     * 总高度
     */
    totalHeight() {
      return this.items.length * this.itemHeight;
    },

    /**
     * 偏移量
     */
    offsetY() {
      return this.visibleRange.start * this.itemHeight;
    },

    /**
     * 可见数量
     */
    visibleCount() {
      return Math.ceil(this.containerHeight / this.itemHeight);
    }
  },

  watch: {
    items: {
      handler() {
        this.updateVisibleRange();
      },
      immediate: true
    },

    scrollTop() {
      this.updateVisibleRange();
    }
  },

  methods: {
    /**
     * 处理滚动事件
     * @param {Event} event 滚动事件
     */
    handleScroll(event) {
      this.scrollTop = event.target.scrollTop;
    },

    /**
     * 更新可见范围
     * @private
     */
    updateVisibleRange() {
      const startIndex = Math.floor(this.scrollTop / this.itemHeight);
      
      this.visibleRange = {
        start: Math.max(0, startIndex - this.buffer),
        end: Math.min(this.items.length, startIndex + this.visibleCount + this.buffer)
      };
    },

    /**
     * 滚动到指定项
     * @param {Number} index 项索引
     */
    scrollToItem(index) {
      const scrollTop = index * this.itemHeight;
      this.$refs.container.scrollTop = scrollTop;
    }
  }
};

/**
 * 懒加载 Mixin
 */
export const lazyLoadMixin = {
  data() {
    return {
      intersectionObserver: null,
      lazyElements: new Set()
    };
  },

  mounted() {
    this.initLazyLoading();
  },

  beforeDestroy() {
    this.destroyLazyLoading();
  },

  methods: {
    /**
     * 初始化懒加载
     * @private
     */
    initLazyLoading() {
      if ('IntersectionObserver' in window) {
        this.intersectionObserver = new IntersectionObserver(
          this.handleIntersection.bind(this),
          {
            rootMargin: '50px',
            threshold: 0.1
          }
        );
      }
    },

    /**
     * 观察元素
     * @param {HTMLElement} element 要观察的元素
     */
    observeElement(element) {
      if (this.intersectionObserver && element) {
        this.intersectionObserver.observe(element);
        this.lazyElements.add(element);
      }
    },

    /**
     * 取消观察元素
     * @param {HTMLElement} element 要取消观察的元素
     */
    unobserveElement(element) {
      if (this.intersectionObserver && element) {
        this.intersectionObserver.unobserve(element);
        this.lazyElements.delete(element);
      }
    },

    /**
     * 处理交叉观察
     * @param {Array} entries 观察条目
     * @private
     */
    handleIntersection(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadElement(entry.target);
          this.unobserveElement(entry.target);
        }
      });
    },

    /**
     * 加载元素
     * @param {HTMLElement} element 要加载的元素
     * @private
     */
    loadElement(element) {
      // 子组件可以重写此方法
      if (element.dataset.src) {
        element.src = element.dataset.src;
      }
      
      element.classList.add('loaded');
    },

    /**
     * 销毁懒加载
     * @private
     */
    destroyLazyLoading() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
        this.intersectionObserver = null;
      }
      this.lazyElements.clear();
    }
  }
};

/**
 * 组件缓存 Mixin
 */
export const cacheMixin = {
  data() {
    return {
      cacheEnabled: true,
      cacheKey: null
    };
  },

  created() {
    this.cacheKey = this.generateCacheKey();
  },

  beforeDestroy() {
    if (this.cacheEnabled && this.cacheKey) {
      componentCache.set(this.cacheKey, this);
    }
  },

  methods: {
    /**
     * 生成缓存键
     * @returns {String} 缓存键
     * @private
     */
    generateCacheKey() {
      const componentName = this.$options.name || 'Component';
      const propsHash = this.hashProps(this.$props);
      return `${componentName}_${propsHash}`;
    },

    /**
     * 计算 props 哈希
     * @param {Object} props 属性对象
     * @returns {String} 哈希值
     * @private
     */
    hashProps(props) {
      return btoa(JSON.stringify(props)).slice(0, 8);
    },

    /**
     * 从缓存获取组件
     * @param {String} key 缓存键
     * @returns {Object} 组件实例
     */
    getFromCache(key) {
      return componentCache.get(key);
    },

    /**
     * 清除缓存
     * @param {String} key 缓存键
     */
    clearCache(key) {
      if (key) {
        componentCache.delete(key);
      } else {
        componentCache.clear();
      }
    }
  }
};

export default {
  performanceMixin,
  virtualScrollMixin,
  lazyLoadMixin,
  cacheMixin
};
