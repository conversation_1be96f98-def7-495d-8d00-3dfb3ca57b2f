<template>
  <MapLayout
    ref="mapBody"
    class="map-body"
    :loading="loading"
    :options="options"
    :top="top"
    @centerMap="centerMap"
  >
    <template slot="map">
      <div id="scaleContent"></div>
      <!-- <TimeSlider v-if="showSlider" :config="sliderConfig" @change="filterCoverByTimeRange" /> -->
    </template>
    <template slot="action">
      <!-- <UpdateModal
        :mode="pageMode"
        :show="store_updateModalOpen"
        @cancel="$store.commit('setting/setUpdateModalOpen', false)"
      /> -->
    </template>
    <template v-if="loadFailMsg" #error>
      <Error :failMsg="loadFailMsg" @reload="reload" />
    </template>
  </MapLayout>
</template>

<script>
import Error from "@daas/surface-web-lib/components/error";
import MapLayout from "./map-layout.vue";
import Map from "../model/map.js";
import Layer from "../model/layer.js";
import mixin from "../mixins/mixin";
import { loadJS } from "../utils/utils";
import StaticData from "@/model/StaticData";
// import UpdateModal from "./header/extra/update.vue";
// import * as service from "@/services";
// import TimeSlider from "@/components/time-slider/progress.vue";
import { ymdConfig } from "@/components/time-slider/slider-setting";
import Lifecycle from "@/utils/lifecycle";
import eventBus, { eventMap } from "@/utils/event-bus";

import { mapGetters } from "vuex";

export default {
  name: "MapMain",
  mixins: [mixin],
  provide() {
    return {
      getMap: () => this.M_Map,
      getMapOptions: () => this.options,
      getCurrentLayer: () => this.currentLayer,
      initLayerByResource: this.initLayerByResource,
      initLayerByConfig: this.initLayerByConfig,
      setCurrentLayer: this.setCurrentLayer,
      layerAction: this.handleLayerAction,
      getPageMode: this.pageMode,
      loadingStart: this.loadingStart,
      loadingDone: this.loadingDone,
      reload: this.reload,
    };
  },
  data() {
    return {
      M_Map: null,
      Map: null,
      loading: false,
      options: { layerList: [] },
      top: true, //是否展示头部
      layerTimeRange: [],

      sliderConfig: ymdConfig,
      showSlider: false,
    };
  },
  components: {
    MapLayout,
    // UpdateModal,
    Error,
    // TimeSlider,
  },
  computed: {
    currentLayer() {
      return this.$store.getters["currentLayer"];
    },
    resourceId() {
      return this.$route.query.resourceId;
    },
    store_updateModalOpen() {
      return this.currentLayer && this.$store.getters["setting/updateModalOpen"];
    },
    pageMode() {
      return this.$store.state["setting/pageMode"];
    },
    isStatic() {
      return this.$store.getters["setting/isStatic"];
    },
    ...mapGetters("setting", ["loadFailMsg"]),
  },
  watch: {
    resourceId: {
      handler(resourceId, oldVal) {
        // this.Map && this.Map.clearOverlays();
        if (
          this.isMounted &&
          resourceId &&
          resourceId !== oldVal &&
          !this.$onWorkplaceStateChange
        ) {
          this.tempResourceId = resourceId;
          this.initLayer();
          this.initLayerByResource(resourceId);
        }
      },
      immediate: true,
    },
    "$dataView.error.value": {
      handler(err) {
        if (err.load) {
          const response = err.load.response;
          this.$store.dispatch("setting/setLoadFailInfo", {
            failUrl: response.config.url,
            failMsg: response.data.message,
            failCode: response.data.code,
          });
        }
      },
      deep: true,
    },
    // "currentLayer.id": {
    //   handler(id, oldVal) {
    //     if (id !== oldVal) {
    //       this.layerTimeRange = this.currentLayer.getTimeRange();
    //     }
    //   },
    // },
  },
  async mounted() {
    await loadJS("/daas/static/maps/maplib/DrawingManager.js");
    if (window.BMap) {
      this.loading = "加载中...";
      this.initMap();

      const { start } = this.$dataView;
      start({ route: this.$route, scanDom: "scaleContent" }, this.dataViewCb);
    }
    this.isMounted = true;

    this.$nextTick(() => {
      eventBus.on(eventMap.startSetProvince, () => {
        this.loading = "正加载数据...";
      });
      eventBus.on(eventMap.endSetProvince, () => {
        this.loading = false;
      });
      this.$once("hook:beforeDestroy", () => {
        eventBus.off(eventMap.startSetProvince);
        eventBus.off(eventMap.endSetProvince);
      });
    });
  },
  methods: {
    centerMap(marker) {
      // eslint-disable-next-line no-undef
      this._point = new BMap.Point(marker.lng, marker.lat);
      const zoom = this.Map.getZoom() || 10;
      this.Map.centerAndZoom(this._point, zoom); //默认缩放级别为当前用户设置
    },
    initMap() {
      this.M_Map = new Map("scaleContent", this.drawCallback);
      this.Map = this.M_Map["_map"];

      Lifecycle.mapInit();
    },
    async usageEnter() {
      const res = await this.$http.get("/insightApp/app", {
        params: {
          appType: "Analyze",
          appSubType: "map",
          pageParam: {
            pageIndex: 1,
            limit: 1,
            sortField: "lastUpdateTime",
            sortType: "desc",
          },
        },
      });
      if (res.data?.[0]) {
        this.$router.replace({
          query: {
            ...this.$route.query,
            appId: res.data[0].appId,
          },
        });
        this.reload();
        return true;
      } else {
        return false;
      }
    },
    async dataViewCb(res) {
      const dataView = this.$dataView.dataView.value;
      this.top = res.top;

      if (res.fromType) {
        this.$store.commit("setting/setShowType", res.fromType);
      }

      if (this.$route.params.dataViewType === "usage" && !dataView.appId) {
        const flag = await this.usageEnter();
        if (flag) return;
      }

      let dataViewConfig = res.dataViewConfig;
      let mode = dataView.mode;
      if (dataView.permissions?.length && mode === "edit") {
        mode = dataView.permissions.includes("edit") ? "edit" : "view";
      }

      this.$store.dispatch("setting/changePageMode", {
        mode,
        Map: this.M_Map,
      });

      let { isStaticData = true } = dataViewConfig;
      if (isStaticData === undefined) isStaticData = true;

      this.$store.commit("setting/SET_IS_STATIC", isStaticData);

      if (dataViewConfig?.mapConfig?.settings) {
        this.M_Map.initSettings(dataViewConfig.mapConfig.settings);
      }

      let _config = {};
      if (res.resourceId) {
        _config.resourceId = res.resourceId;
      } else if (res.tplConfig) {
        const elements = res.tplConfig.elements;
        elements.reverse();
        if (elements && elements.length) {
          let tempElements = [];
          tempElements = elements.map((i) => i.entityDataResourceId);
          _config.mapConfig = {
            elements: tempElements,
          };
        }
      }

      dataViewConfig = { ...dataViewConfig, ..._config };
      this.render(dataViewConfig);
    },
    render(config) {
      const { resourceId, isStaticData } = config;
      this.initLayer();

      if (!resourceId && !isStaticData) this.changeLoading(false);

      this.$nextTick(() => {
        config?.mapConfig?.elements?.length || isStaticData
          ? this.initByPageConfig(config)
          : resourceId && this.initLayerByResource(resourceId);
      });
    },
    initLayer() {
      this.$store.commit("setLayer", []);
      this.Map && this.Map.clearOverlays();
    },
    async initLayerByResource(resourceId, options) {
      this.initLayerByConfig({ resourceId: resourceId, ...options });
    },
    async initLayerByConfig(layerConfig) {
      try {
        this.changeLoading("初始化图层...");
        const layer = new Layer(this.M_Map, {
          isStatic: this.isStatic,
          showEntityDetailCallBack: this.showEntityDetailCallBack,
        });
        await layer.init(layerConfig);
        this.changeLoading("装载数据中...");
        await layer.loadData();
        this.$store.commit("addLayer", layer);
        this.setCurrentLayer(layer);
        this.changeLoading(false);
        layer.getPageParam();
      } catch (error) {
        // error?.message && this.$message.warning(error.message);
        this.changeLoading(false);
        console.error(error);
        this.$store.commit("setting/setLoadFailInfo", {
          failUrl: error.url,
          failMsg: error.message,
          failCode: error.resultCode,
        });
      }
    },
    setCurrentLayer(currentLayer) {
      currentLayer.isSelected = true;
      this.$store.commit("setCurrentLayer", currentLayer);
      this.$store.dispatch("setting/initSetting");
      const drawType = (currentLayer && currentLayer.type) || "marker";
      //设置完选中图层，右上角绘制图标改变
      this.$store.commit("setting/modifyDrawActionStatus", {
        drawType: drawType,
        type: "isOpen",
        value: false,
      });
      this.$store.commit("setting/modifyDrawActionStatus", {
        drawType: drawType,
        type: "isDisabled",
        value: false,
      });
    },
    // loadLayer(resourceDataList, currentLayer) {
    //   currentLayer.setDataSource(resourceDataList);
    //   currentLayer.setDefaultMappingConifg();
    //   currentLayer.setStack(resourceDataList);
    //   currentLayer.setLayerType();
    //   currentLayer.loadLayerData(); //加载图层数据
    // },
    changeLoading(value) {
      this.loading = value;
    },
    // 画图回调
    async drawCallback(drawType, drawData) {
      const currCovering = this.currentLayer.manualDraw(drawType, drawData);
      if (!currCovering) return;

      this.isStatic ? this.drawStaticCb(currCovering) : this.drawSourceCb(currCovering);

      if (drawType === "polygon") {
        this.$store.commit("setting/setOpenDrawPolygon", false);
      }

      this.currentLayer.getPageParam();
    },
    // 静态资源回调--新建
    drawStaticCb(currCovering) {
      this.currentLayer.staticStore.add(currCovering.extData);
    },
    // 绑定资源的回调
    async drawSourceCb(currCovering) {
      try {
        // 实时保存
        if (
          !this.currentLayer?.resourceColumnsMap?.centerPoint &&
          currCovering?.extData?.centerPoint
        ) {
          delete currCovering.extData.centerPoint;
        }
        await this.$refs.mapBody.headerInsert(currCovering.extData);
      } catch (error) {
        console.error("file: index.vue:223 -> drawSourceCb -> error:", error);
        currCovering.delete(currCovering.id, this.currentLayer);
        error.response?.data?.message && this.$message.warning(error.response?.data?.message);
      }
    },
    handleLayerAction(action) {
      const { id, type, value } = action;
      const layerList = this.options.layerList;
      const index = layerList.findIndex((d) => d.id == id);
      const clickLayer = layerList[index];
      if (type == "visiable") {
        if (!value) {
          clickLayer.showLayer();
        } else {
          clickLayer.hideLayer();
        }
      }
    },
    /**
     * 通过页面配置来初始化
     * @param {Object} pageConfig 页面配置对象
     */
    async initByPageConfig(pageConfig) {
      // 来源于页面的配置
      const elements = (pageConfig && pageConfig?.mapConfig?.elements) || [];

      elements.reverse();

      if (elements && elements.length) {
        for (let i = 0; i < elements.length; i++) {
          // string： 兼容旧的保存逻辑
          if (typeof elements[i] == "string") {
            await this.initLayerByResource(elements[i]);
          } else {
            await this.initLayerByConfig(elements[i]);
          }
        }
      } else if (this.isStatic) {
        const _staticData = new StaticData({ type: "marker" });
        await this.initLayerByConfig(_staticData.mockData);
      }

      this.getAllLayerTimeRange();
    },
    // 开始全局loading
    loadingStart() {
      this.loading = true;
    },
    // 停止全局loading
    loadingDone() {
      this.loading = false;
    },
    // 查看
    showEntityDetailCallBack(resourceId, entityId) {
      this.$entityDetailModal.show({
        resourceId,
        entityId,
      });
    },
    // 获取所有图层的时间
    getAllLayerTimeRange() {
      let allTimeRange = [];
      this.$store.getters["layers"].forEach((layer) => {
        allTimeRange = [...allTimeRange, ...layer.getTimeRange()];
      });
      allTimeRange = allTimeRange
        .filter(Boolean)
        .sort((a, b) => new Date(a).valueOf() - new Date(b).valueOf());

      if (allTimeRange.length > 1) {
        this.sliderConfig.range = [
          this.sliderConfig.rawFormat(allTimeRange[0]),
          this.sliderConfig.rawFormat(allTimeRange.at(-1)),
        ];

        this.$nextTick(() => {
          this.showSlider = true;
        });
      }
    },
    // 通过时间范围控制图层中覆盖物是否显示
    filterCoverByTimeRange(timeRange) {
      this.$store.getters["layers"].forEach((layer) => {
        layer.filterCoverByTimeRange(timeRange);
      });
    },
    // 重新加载地图
    reload() {
      this.$store.dispatch("resetData");
      this.$store.dispatch("setting/clearLoadFailInfo");
      this.Map && this.Map.clearOverlays();
      this.M_Map && this.M_Map.clearMarkProvince();

      this.$dataView.reload(this, this.$route);
      Lifecycle.mapInit();
    },
  },
  destroyed() {
    this.Map && this.Map.clearOverlays();
  },
};
</script>

<style lang="less" scoped>
#scaleContent {
  width: 100%;
  height: 100%;
}

.map-body {
  :deep(.slider-container) {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    z-index: 99;

    width: auto !important;
  }
}
</style>
