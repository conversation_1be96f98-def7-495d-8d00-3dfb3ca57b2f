<template>
  <div class="map-content">
    <div class="left-content">
      <slot name="map" />
    </div>
    <div class="center-content" v-if="layerViewList">
      <ViewList
        @toLocation="toLocation"
        @onCloseTable="mapToViewList(false)"
        :isStatic="isStatic"
        ref="viewList"
      />
    </div>
    <div class="center-content" v-if="isShowConifgModal">
      <IconConfigModal v-if="currentLayer.type === 'marker'" />
      <PolygonConfigModal v-if="currentLayer.type === 'polygon'" />
    </div>
    <div class="right-content">
      <div class="info">
        <span> {{ layerNum }}个图层 </span>
        <!-- 添加按钮 -->
        <div v-show="canEditPage">
          <!-- 静态按钮 -->
          <!-- 上传图层 -->
          <a-dropdown class="laryer-type" style="margin-left: 8px">
            <a-icon v-show="canEditPage" type="download" />
            <a-menu slot="overlay">
              <template v-if="!isDomain && isStatic">
                <a-menu-item>
                  <!-- 上传 -->
                  <ReadLocalJson @fileChange="loadDataToLayer" />
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item @click="exportLayerTemplate">
                  <div class="layer-type-wrap">
                    <div class="type-text">下载数据模板</div>
                  </div>
                </a-menu-item>
                <a-menu-divider />
              </template>
              <a-menu-item @click="exporAlltLayer">
                <div class="layer-type-wrap">
                  <div class="type-text">导出所有图层数据</div>
                </div>
              </a-menu-item>
            </a-menu>
          </a-dropdown>

          <!-- 新增图层 -->
          <a-dropdown v-if="isStatic" class="laryer-type" style="margin-left: 8px">
            <a-icon v-show="canEditPage" type="plus" />
            <a-menu slot="overlay">
              <a-menu-item @click="createStatciLayer('marker')">
                <div class="layer-type-wrap">
                  <img src="~@/assets/imgs/marker.png" class="type-wrap" />
                  <div class="type-text">点标记图层</div>
                </div>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item @click="createStatciLayer('polygon')">
                <div class="layer-type-wrap">
                  <img src="~@/assets/imgs/polygon.png" class="type-wrap" />
                  <div class="type-text">图形标记图层</div>
                </div>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <!-- 动态按钮 -->
          <a-icon
            v-else-if="!isDomain"
            type="plus"
            @click="handleCreateLayer"
            style="margin-left: 8px"
          />
        </div>
      </div>

      <div class="layer-content" :style="{ height: layerContentHeight }">
        <slot name="layer" />
      </div>
    </div>
    <ResourceModal v-if="isShowResourceSelect"></ResourceModal>
    <LayerMapping v-if="currentLayer" />
    <StaticUpdate v-if="visibleEditStaticModal" />
  </div>
</template>

<script>
import ViewList from "./layer/list.vue";
import ResourceModal from "./resource-modal.vue";
import LayerMapping from "@pages/content/layer/layer-mapping.vue";
import IconConfigModal from "@/components/icon-config-modal/index.vue";
import PolygonConfigModal from "@/components/polygon-config-modal";
import { mapGetters } from "vuex";
import StaticData from "@/model/StaticData";
import StaticUpdate from "../header/extra/static-update.vue";
import ReadLocalJson from "@/components/read-local-json/index.vue";
import FileService from "@/utils/file-service";
import GeoUtil from "@/utils/Geo/GeoUtil";
import geoTemplate from "@/assets/data/geo-template";

export default {
  inject: ["initLayerByConfig", "initLayerByResource"],
  components: {
    ResourceModal,
    ViewList,
    LayerMapping,
    IconConfigModal,
    PolygonConfigModal,
    StaticUpdate,
    ReadLocalJson,
  },
  data() {
    return {
      showResource: false,
      layerViewList: false,
    };
  },
  computed: {
    ...mapGetters("setting", [
      "isShowConifgModal",
      "isStatic",
      "canEditPage",
      "visibleEditStaticModal",
      "isDomain",
    ]),
    layerContentHeight() {
      this.$store.getters[""];
      let miniToolBar = this.$store.getters["miniToolBar"];
      let height = 55;
      // const action = 44
      const bar = 36;
      if (miniToolBar) height = 35;
      let leftHegiht = bar + height + "px";
      return `calc(100% - ${leftHegiht})`;
    },
    layerNum() {
      return this.$store.getters["layers"].length;
    },
    isShowResourceSelect() {
      return this.$store.getters["setting/isShowResourceSelect"];
    },
    currentLayer() {
      return this.$store.state["currentLayer"];
    },
  },
  methods: {
    handleCreateLayer() {
      const _isStatic = this.$store.getters["setting/isStatic"];
      _isStatic ? this.handleStaticLayer() : this.showResourceSelect();
    },
    handleStaticLayer() {
      this.$store.commit("setting/SET_SHOWSELECTOR_LAYER_TYPEMODAL", true);
    },
    createStatciLayer(type) {
      const _staticData = new StaticData({ type });
      this.initLayerByConfig(_staticData.mockData);
    },
    showResourceSelect() {
      this.$store.commit("setting/setShowResourceSelect", true);
    },
    handleChangeResource(resourceId) {
      this.$store.dispatch("addLoadedResourceStore", { resourceId });
    },
    mapToViewList(sameLayer) {
      //点击隐藏点位列表
      this.layerViewList = sameLayer;
    },
    toLocation(point) {
      //点位居中
      this.$emit("toLocation", point);
    },
    async headerInsert(drawData) {
      // 保存新存入的覆盖物
      await this.currentLayer.resourceStore.dispatch("data/addData", {
        values: [drawData],
      });
    },
    // 读取本地数据生成图层
    loadDataToLayer(data) {
      data.forEach((i) => {
        if (i.resourceId) {
          const { resourceId, ...options } = i;
          this.initLayerByResource(resourceId, options);
        } else {
          this.initLayerByConfig(i);
        }
      });
    },
    // 下载地图数据模板
    exportLayerTemplate() {
      //导出成文件
      let fileName = (this.$dataView.dataView.appName || "未命名地图") + "_地图图层模板";
      if (!FileService.endWith(fileName, ".geojson")) {
        fileName = `${fileName}.geojson`;
      }

      // const content = `${layerTemplateTips}\n${JSON.stringify(layerTemplate, null, 2)}`;
      const content = JSON.stringify(geoTemplate, null, 2);

      FileService.downloadFile(fileName, content);
    },
    //  导出所有图层数据
    exporAlltLayer() {
      //导出成文件
      let fileName = (this.$dataView.dataView.appName || "未命名地图") + "_地图图层数据";

      if (!FileService.endWith(fileName, ".geojson")) {
        fileName = `${fileName}.geojson`;
      }

      const layers = this.$store.getters["layers"];

      const _data = layers.map((i) => {
        const localData = i.getResourceData();
        return {
          ...i.layerConfig,
          type: i.type,
          localData,
        };
      });

      const _res = GeoUtil.toGeoJson(_data);

      const content = JSON.stringify(_res, null, 2);
      FileService.downloadFile(fileName, content);
    },
  },
};
</script>

<style lang="less" scoped>
@desc-back: #f0f1f2;
@border-color: #ddd;
@right-content-width: 280px;
@center-content-width: 250px;

.map-content {
  position: relative;
  width: 100%;
  height: 100%;

  .left-content {
    position: absolute;
    z-index: 1;
    width: calc(100% - @right-content-width);
    height: 100%;
  }

  .center-content {
    position: absolute;
    right: @right-content-width;
    z-index: 2;
    width: @center-content-width;
    height: 100%;
    background-color: #fff;
  }

  .right-content {
    float: right;
    width: @right-content-width;
    height: 100%;
    border-left: 1px solid @border-color;

    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      padding: 0 10px;
      line-height: 36px;
      background: @desc-back;

      i {
        cursor: pointer;
      }
    }
  }

  .layer-list {
    position: absolute;
    bottom: 0;
    left: @right-content-width;
    width: calc(100% - @right-content-width);
    height: 260px;
    background: #fff;
    box-shadow: 2px 2px 1px 1px #eee;

    .close-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
    }
  }
}

:deep(.ant-dropdown-menu-item) {
  .layer-type-wrap {
    display: flex;
    align-items: center;
    .type-wrap {
      width: 32px;
      height: auto;
      margin-right: 8px;
    }
  }
}
</style>
