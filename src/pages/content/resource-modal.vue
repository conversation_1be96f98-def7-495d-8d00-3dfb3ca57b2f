<template>
  <ds-select-modal
    visible
    type="resource"
    :params="{ resourceType: ['table'], status: ['materialization', 'published'] }"
    @ok="handleResourceOk"
    @cancel="close"
  />
</template>
<script>
import { DsSelectModal } from "@daas/components";
export default {
  components: {
    DsSelectModal,
  },
  inject: ["initLayerByResource"],
  methods: {
    close() {
      this.$store.commit("setting/setShowResourceSelect", false);
    },
    handleResourceOk(keys) {
      this.close();
      keys.length && this.handleChangeResource(keys[0]);
    },
    handleChangeResource(resourceId) {
      this.initLayerByResource(resourceId);
    },
  },
};
</script>
