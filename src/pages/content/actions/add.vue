<template>
  <div>
    <a-button type="dashed" @click="modalVisiable = true"> 新建图层 </a-button>

    <AddModal v-if="modalVisiable" @addLayer="addLayer" @cancel="modalVisiable = false"></AddModal>
  </div>
</template>
<script>
import AddModal from "./add-modal";
export default {
  components: {
    AddModal,
  },
  data() {
    return {
      modalVisiable: false,
    };
  },
  methods: {
    addLayer() {
      this.modalVisiable = false;
    },
  },
};
</script>
