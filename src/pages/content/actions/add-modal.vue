<template>
  <a-modal :visible="true" @cancel="$emit('cancel')" v-bind="bindModal">
    <template slot="title">
      <div class="title">创建图层</div>
    </template>

    <div class="ul">
      <div class="li" @click="selected = 'marker'" :class="{ 'li-selected': selected == 'marker' }">
        <span class="icon">
          <img :src="`${BASE_URL}static/icons/marker.svg`" />
        </span>
        点
      </div>

      <div class="li" @click="selected = 'area'" :class="{ 'li-selected': selected == 'area' }">
        <span class="icon">
          <img :src="`${BASE_URL}static/icons/area.svg`" />
        </span>
        面
      </div>
    </div>

    <div class="layer-title">
      <h4>图层名称</h4>
      <div class="action">
        <a-input v-model="layerTitle" allowClear style="width: 278px" class="reset-radius" />
        <a-button
          type="primary"
          @click="addLayer"
          class="reset-radius"
          :loading="addLoading"
          style="margin-left: 6px"
        >
          创建图层
        </a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
const { BASE_URL } = process.env;
export default {
  data() {
    return {
      BASE_URL,
      layerTitle: "",
      addLoading: false,
      selected: "marker",
    };
  },
  computed: {
    bindModal() {
      return {
        footer: null,
        width: 420,
        // title:'创建图层'
      };
    },
  },
  methods: {
    addLayer() {
      this.addLoading = true;
      let self = this;
      setTimeout(() => {
        self.addLoading = false;
        self.$emit("addLayer");
      }, 1000);
    },
  },
};
</script>
<style lang="less" scoped>
@li-border: #ededed;
@active-color: #40a9ff;
.title {
  text-align: center;
}
.ul {
  display: flex;
  .li {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 90px;
    margin-right: 10px;
    padding-top: 8px;
    border: 1px solid @li-border;
    border-radius: 3px;
    cursor: pointer;
    .icon {
      margin-bottom: 4px;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
  .li-selected {
    border: 2px solid @active-color;
  }
}
.layer-title {
  margin-top: 10px;
  .action {
    display: flex;
    align-items: center;
  }
}

.reset-radius {
  border-radius: 2px;
}
</style>
