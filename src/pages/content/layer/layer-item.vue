<template>
  <div
    class="layer-item"
    :class="{ 'layer-item-selected': layer == currentLayer, 'layer-item-error': layer.type == '' }"
    @click.stop="clickLayer"
  >
    <div class="top">
      <div class="name">
        <img v-if="layer.type" :src="`${BASE_URL}static/icons/${layer.type}.svg`" />
        <!-- <img v-else :src="`${BASE_URL}static/icons/${layer.type}.svg`" /> -->
        <span v-else>-</span>
        {{ getDispalyType(layer.type) }}
      </div>
      <div class="action" ref="layerItem" @click.stop>
        <a-dropdown v-show="canEditPage" :getPopupContainer="() => $refs.layerItem">
          <a-icon type="unordered-list" style="margin-right: 8px" />
          <a-menu slot="overlay">
            <a-menu-item @click="openInstance">
              {{ layer.type === "marker" ? "点列表" : "多边形列表" }}
            </a-menu-item>

            <a-menu-item @click="handlerMarkerConfig">
              {{ layer.type === "marker" ? "标记样式" : "多边形样式" }}
            </a-menu-item>

            <a-menu-item v-show="!isStatic" @click="showLayerMapping"> 字段映射 </a-menu-item>

            <a-menu-item @click="exportLayerData"> 导出数据(geojson) </a-menu-item>

            <a-menu-item v-if="!isDomain" @click="handleRemoveLayer"> 移除图层 </a-menu-item>
          </a-menu>
        </a-dropdown>
        <a-icon :type="layer.hide ? 'eye-invisible' : 'eye'" @click.stop="clickVisiable" />
      </div>
    </div>
    <div class="content">
      <div class="list">
        <span class="list-item list-item-name" v-show="!isStatic">
          资源名称:{{ layer.layerConfig.displayName }}
        </span>

        <span class="list-item list-item-count">
          数量:
          <span>{{ numberOfTags || 0 }}</span>
          <!-- <span>{{ pageParam.recordTotal || 0 }}个</span> -->
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import FileService from "@/utils/file-service";
import GeoUtil from "@/utils/Geo/GeoUtil";
import { setSelectedCell } from "@/utils/map-utils";
const { BASE_URL } = process.env;

export default {
  inject: ["setCurrentLayer", "layerAction", "getMap"],
  props: {
    layer: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      BASE_URL,
      layerMappingVisible: false,
    };
  },
  computed: {
    Map() {
      return this.getMap();
    },
    drawingTool() {
      return this.Map.drawingTool;
    },
    currentLayer() {
      return this.$store.getters["currentLayer"];
    },
    pageParam() {
      return this.layer.getPageParam();
    },
    // 标记数量
    numberOfTags() {
      return this.layer.numberOfTags;
    },
    canEditPage() {
      return this.$store.getters["setting/canEditPage"];
    },
    isStatic() {
      return this.$store.getters["setting/isStatic"];
    },
    isDomain() {
      return this.$store.getters["setting/isDomain"];
    },
  },
  methods: {
    openInstance() {
      if (this.layer != this.currentLayer) {
        this.$store.commit("setCurrentLayer", this.layer);
      }
      this.clickLayer();
    },

    clickLayer() {
      this.drawingTool._drawingManager._close();
      let sameLayer = false;

      this.$nextTick(() => {
        if (this.layer != this.currentLayer) {
          this.$store.commit("setting/modifyDrawActionStatus", {
            drawType: this.layer.type,
            type: "isOpen",
            value: false,
          });
          this.$store.commit("setCurrentLayer", this.layer);
        } else {
          sameLayer = true;
        }
        this.$store.commit("setting/setShowConifgModal", false);

        this.$emit("itemClickViewList", sameLayer);
      });
    },
    clickVisiable() {
      this.layer.changeShow();
    },
    getDispalyType(type) {
      if (type == "marker") {
        return "点";
      } else if (type == "polygon") {
        return "面";
      } else {
        return "请先配置字段映射";
      }
    },
    handleRemoveLayer() {
      this.$confirm({
        title: "系统提示",
        content: "该操作将移除图层，是否继续该操作？",
        okText: "是",
        cancelText: "否",
        onOk: () => {
          this.$emit("delete", this.layer);
        },
      });
    },
    showLayerMapping() {
      if (this.layer != this.currentLayer) {
        this.$store.commit("setCurrentLayer", this.layer);
      }
      this.$store.commit("setting/setShowLayerMapping", true);
    },
    handlerMarkerConfig() {
      if (!this.layer.type) {
        this.$message.warning("操作前请先映射字段");
        return;
      }

      setSelectedCell(null);

      if (this.layer != this.currentLayer) {
        this.$store.commit("setCurrentLayer", this.layer);
      }

      this.$store.commit("setting/setShowConifgModal", true);
    },
    // 导出图层数据
    async exportLayerData() {
      //导出成文件
      let fileName = (this.$dataView.dataView.appName || "未命名地图") + "_地图图层数据";

      if (!FileService.endWith(fileName, ".geojson")) {
        fileName = `${fileName}.geojson`;
      }

      const localData = this.layer.getResourceData();
      const config = {
        ...this.layer.layerConfig,
        type: this.layer.type,
        localData,
      };

      const res = GeoUtil.toGeoJson([config]);

      const content = JSON.stringify(res, null, 2);
      FileService.downloadFile(fileName, content);
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
@layertop-background: #f5f7fa;
@active-color: #027aff;
@error-color: #ff4d4f;

.layer-item {
  height: 80px;
  margin-bottom: 12px;
  border: 1px solid var(--ds-border-color-split);
  border-radius: 3px;
  box-shadow: var(--ds-card-shadow);
  cursor: pointer;
  overflow: hidden;

  &-selected {
    border: 1px solid @active-color;
  }

  &-error {
    border: 1px solid @error-color;
  }

  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    padding: 0 8px;
    background-color: @layertop-background;

    .name {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
    }

    .action {
      i {
        cursor: pointer;
      }
    }
  }

  .content {
    display: flex;
    height: 38px;
    padding: 4px 8px;
    font-size: 12px;
    line-height: 38px;

    .list {
      display: flex;
      flex: 1;
      justify-content: space-between;
      width: 100%;
      cursor: pointer;

      &-item {
        white-space: nowrap;
        word-break: keep-all;

        &-name {
          flex: auto;
          margin-right: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &-count {
          flex: auto;
        }
      }
    }
  }
}
</style>
