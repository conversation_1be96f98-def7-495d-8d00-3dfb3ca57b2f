<template>
  <div class="layer-mapping">
    <a-modal :visible="isShowLayerMapping" @cancel="close" v-bind="bindModal">
      <a-form-model layout="vertical">
        <a-form-model-item style="margin-bottom: 16px; padding: 0">
          <template #label>
            <a-space :size="4" style="margin-bottom: 8px">
              图层：
              <a-select
                size="small"
                placeholder="请选择"
                style="width: 200px"
                :value="currentLayer.id"
                @change="hanldeChangeLayer"
              >
                <a-select-option :value="layer.id" :key="layer.id" v-for="layer in layerList">
                  {{ layer.layerConfig.displayName }}{{ getDispalyType(layer.type) }}
                </a-select-option>
              </a-select>
            </a-space>
            <a-space :size="4">
              图层可视化标记类型：
              <a-select
                size="small"
                placeholder="请选择"
                style="width: 200px"
                v-model="layerType"
                :default-value="currentLayerType"
                @change="layerTypeChange"
              >
                <a-select-option value="polygon"> 面 </a-select-option>
                <a-select-option value="marker"> 点 </a-select-option>
              </a-select>
            </a-space>
          </template>
          <a-spin :spinning="hide">
            <div class="mapping-table">
              <a-table
                v-if="!hide"
                :rowKey="(record, index) => index"
                :columns="columns"
                :data-source="resourceColumns"
                size="small"
                :pagination="false"
                :scroll="{ x: true, y: 400 }"
              >
                <template #mapColumnName="text, record">
                  <a-select
                    size="small"
                    placeholder="请选择"
                    style="width: 100%"
                    allowClear
                    :defaultValue="text"
                    @dropdownVisibleChange="dropdownVisibleChange"
                    @change="(v) => handleChangeColumn(v, record)"
                    @mouseenter="(v) => clearSelectValue(v, record)"
                  >
                    <a-select-option
                      :value="item.name"
                      :key="item.name"
                      :title="`${item.name}【${item.displayName}】`"
                      :disabled="mapColumnNames.includes(item.name)"
                      v-for="item in columnsDataDropDown"
                    >
                      <a-tooltip placement="right">
                        <template
                          slot="title"
                          v-if="mapColumnNames.includes(item.name) && selectIsOpen"
                        >
                          当前字段已被配置
                        </template>
                        {{ item.displayName }} - {{ item.name }}
                      </a-tooltip>
                    </a-select-option>
                  </a-select>
                </template>
              </a-table>
            </div>
          </a-spin>
        </a-form-model-item>
      </a-form-model>
      <template slot="footer">
        <a-button @click="close">取消</a-button>
        <a-button type="primary" @click="handleSubmit">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import _cloneDeep from "lodash/cloneDeep";

const columns = [
  { width: 200, dataIndex: "displayName", title: "显示名称" },
  {
    dataIndex: "mapColumnName",
    title: "映射字段",
    scopedSlots: {
      customRender: "mapColumnName",
    },
  },
];
// 类型枚举
const _TYPE_NAME = {
  marker: "点",
  polygon: "多边形",
};
// 需要隐藏的 name
const _HIDDEN_NAME_CONFIG = {
  marker: ["pointList", "centerPoint", "polygonConfig"],
  polygon: ["lng", "lat", "pointConfig"],
};

export default {
  inject: ["getMap", "getMapOptions"],
  data() {
    return {
      columns,
      resourceColumns: [],
      hide: false,
      hasChange: false,
      oldLayer: {},
      layerType: "polygon",
      selectIsOpen: false,
    };
  },
  computed: {
    isShowLayerMapping() {
      return this.$store.getters["setting/isShowLayerMapping"];
    },
    bindModal() {
      return {
        title: "字段映射",
        // footer: null,
        getContainer: () => document.getElementsByClassName("layer-mapping")[0],
      };
    },
    // 图层/列结构/列数据
    // options() {
    //   return this.getMapOptions();
    // },
    // 图层
    layerList() {
      return this.$store.getters["layers"];
    },
    drawingTool() {
      return this.getMap().drawingTool;
    },
    currentLayer() {
      return this.$store.getters["currentLayer"];
    },
    currentLayerType() {
      if (!this.currentLayer.type) return this.layerType;
      this.$nextTick(() => {
        this.layerTypeChange(this.currentLayer.type);
      });
      return this.currentLayer.type;
    },
    mapColumnNames() {
      return this.resourceColumns ? this.resourceColumns.map((i) => i.mapColumnName) : [];
    },
    columnsDataDropDown() {
      if (this.currentLayer) {
        const resourceInfo = this.currentLayer.getResourceInfo();
        if (resourceInfo) {
          return resourceInfo.columns;
        }
      }
      return [];
    },
  },
  methods: {
    setTableLayer(layer) {
      if (!layer) {
        layer = this.currentLayer;
      }
      if (this.oldLayer == layer) {
        return;
      }
      this.oldLayer = layer;

      this.hide = true;
      //进行json的操作是，弹窗未点保存，避免改变原来的对象
      this._resourceColumns = _cloneDeep(
        (layer.resourceColumns && JSON.parse(JSON.stringify(layer.resourceColumns))) || [],
      ); //资源列关系结构

      this.resourceColumns = this.filterResourceColumns(this._resourceColumns, this.layerType);

      setTimeout(() => {
        this.hide = false;
      }, 100);
    },
    handleChangeColumn(val, record) {
      let { name } = record;
      const index = this.resourceColumns.findIndex((d) => d.name == name);
      if (index >= 0) this.resourceColumns[index].mapColumnName = val;
      this.hasChange = true;
    },
    // 参数 (e, record)
    clearSelectValue() {
      // let clearDom = e.path[0].children[0].children[1];
      // let self = this;
      // clearDom.addEventListener("click", () => {
      //   let index = self..findIndex((d) => d.mapColumnName == record.mapColumnName);
      //   self.tableMappingConfig[index].mapColumnName = "";
      // });
    },
    hanldeChangeLayer(layerId) {
      const layer = this.layerList.find((layer) => {
        return layer.id == layerId;
      });
      if (layer) {
        if (this.hasChange) {
          const self = this;
          this.$confirm({
            title: "是否切换？",
            content: "当前图层配置修改未保存，切换后将失效",
            onOk() {
              self.$store.commit("setCurrentLayer", layer);
              self.setTableLayer(layer);
            },
            onCancel() {},
          });
          this.visible = true;
          this.nextLayer = layer;
        } else {
          this.$store.commit("setCurrentLayer", layer);
          this.setTableLayer(layer);
        }
      }
    },
    handleSubmit() {
      if (this.hasChange) {
        this.currentLayer.changeResourceColumn(this.resourceColumns);
        this.setDrawActionStatus(); //设置右上角操作栏状态
      }
      this.close();
    },
    getDispalyType(type) {
      const typeName = _TYPE_NAME[type] || "";
      return typeName ? `【${typeName}图层】` : "";
    },
    setDrawActionStatus() {
      this.drawingTool._drawingManager._close();
      this.$store.dispatch("setting/initSetting");
      if (this.currentLayer.type == "marker") {
        // 标记点类型
        this.$store.commit("setting/modifyDrawActionStatus", {
          drawType: "marker",
          type: "isDisabled",
          value: false,
        });
      } else if (this.currentLayer.type == "polygon") {
        // 图形类型
        this.$store.commit("setting/modifyDrawActionStatus", {
          drawType: "polygon",
          type: "isDisabled",
          value: false,
        });
      }
    },
    close() {
      this.$store.commit("setting/setShowLayerMapping", false);
    },
    // 过滤映射选项
    filterResourceColumns(columns, type = "marker") {
      return columns.filter((i) => !_HIDDEN_NAME_CONFIG[type].includes(i.name));
    },
    // 修改当前类型
    layerTypeChange(value) {
      if (!value) return;

      if (!this.hasChange && value !== this.currentLayerType) {
        this.hasChange = true;
      }

      this.layerType = value;
      this.hide = true;
      this.resourceColumns = this.filterResourceColumns(this._resourceColumns, value);
      this.$nextTick(() => {
        this.hide = false;
      });
    },
    // 展开下拉
    dropdownVisibleChange(open) {
      this.selectIsOpen = open;
    },
  },
  watch: {
    isShowLayerMapping(curVal) {
      if (curVal == true) {
        this.setTableLayer();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.layer-mapping {
  /deep/.ant-modal-body {
    // min-height: 300px;
    padding: 12px;
  }

  .mapping-table {
    width: 100%;
    // min-height: 300px;
  }
}
</style>
