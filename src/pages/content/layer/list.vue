<template>
  <StaticDataList v-if="isStatic" @onClose="onClose" @onClick="onClick" :layer="currentLayer" />

  <ds-entity-list
    v-else
    :resourceId="currentLayer.layerConfig.resourceId"
    :modularType="modularType"
    :modularId="modularId"
    :mode="pageMode"
    @onClose="onClose"
    @onClick="onClick"
    @submit="submit"
    ref="entityList"
  ></ds-entity-list>
</template>

<script>
import { DsEntityList } from "@daas/graph-next-lib";
import StaticDataList from "@/components/static-data-list";

export default {
  components: {
    DsEntityList,
    StaticDataList,
  },
  props: {
    isStatic: {
      type: <PERSON>olean,
      default: false,
    },
  },
  computed: {
    currentLayer() {
      return this.$store.getters["currentLayer"];
    },
    modularType() {
      return this.$store.getters["modularType"];
    },
    modularId() {
      return this.$store.getters["modularId"];
    },
    pageMode() {
      return this.$store.getters["setting/pageMode"];
    },
  },
  methods: {
    toLocation(point) {
      //点位居中方法  传点位经纬度即可
      this.$emit("toLocation", point);
    },
    onClose() {
      this.$emit("onCloseTable", true);
    },
    //刷新实体列表
    onReflesh() {
      this.$refs.entityList.onReflesh();
    },

    // 数据改变
    submit(data) {
      this.currentLayer.updateData(data.res, true);
    },

    onClick(item) {
      //根据现有点位判断是否存在并居中
      if (this.currentLayer.type == "marker") {
        if (0 != this.currentLayer.markers.length) {
          //有id映射直接取id，无映射不触发居中事件  id映射必填直接使用即可
          let markers = this.currentLayer.markers;
          let i;
          for (i = 0; i < markers.length; i++) {
            if (item.keyId == markers[i].id) {
              if (null != markers[i].point) {
                this.$emit("toLocation", markers[i].point);
              }
            }
          }
        }
      } else if (this.currentLayer.type == "polygon") {
        let polygons = this.currentLayer.polygons;
        let i;
        for (i = 0; i < polygons.length; i++) {
          if (item.keyId == polygons[i].id) {
            let centerPoint = polygons[i].centerPoint;
            if (typeof centerPoint === "string") {
              try {
                centerPoint = JSON.parse(centerPoint);
              } catch (error) {}
            }
            if (null != centerPoint.lat && null != centerPoint.lng) {
              this.$emit("toLocation", centerPoint);
            }
          }
        }
      }
    },
  },
};
</script>
