<template>
  <div class="layer">
    <draggable v-model="layerList">
      <LayerItem
        v-for="(item, i) in layerList"
        :key="i"
        :layer="item"
        @delete="removeLayer"
        @itemClickViewList="clickViewList"
      ></LayerItem>
    </draggable>
  </div>
</template>
<script>
import draggable from "vuedraggable";
import LayerItem from "./layer-item.vue";
export default {
  inject: ["getMap"],
  components: {
    LayerItem,
    draggable,
  },
  computed: {
    layerList: {
      get() {
        return this.$store.getters["layers"];
      },
      set(val) {
        this.$store.commit("setLayer", val);
      },
    },
    Map() {
      return this.getMap();
    },
    drawingTool() {
      return this.Map.drawingTool;
    },
  },
  methods: {
    removeLayer(layer) {
      let i = this.layerList.findIndex((d) => d.id === layer.id);
      if (i >= 0) {
        this.layerList.splice(i, 1);
        layer.removeLayer();
        this.$store.commit("setLayer", this.layerList);
      }
      const { currentLayer } = this.$store.getters;
      if (currentLayer && currentLayer.id === layer.id) {
        this.drawingTool._drawingManager._close();
        this.$store.commit("setting/setOpenDrawMarker", false);
        this.$store.commit("setting/setOpenDrawPolygon", false);
        this.$store.commit("setting/setShowConifgModal", false);
        this.clickViewList(false);

        this.$nextTick(() => {
          this.$store.commit("setCurrentLayer", undefined);
        });
      }
    },
    clickViewList(sameLayer) {
      this.$emit("clickViewList", sameLayer);
    },
  },
};
</script>
<style lang="less" scoped>
.layer {
  height: 100%;
  padding: 10px 10px;
  overflow-y: auto;
}
</style>
