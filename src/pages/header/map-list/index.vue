<template>
  <a-popover
    v-model="visible"
    trigger="click"
    placement="bottomLeft"
    destroyTooltipOnHide
    overlayClassName="map-list-popover-overlay"
    @visibleChange="handleVisibleChange"
  >
    <div class="map-list-popover-header">
      <a-button type="link" style="padding: 0; line-height: 24px" size="small"> 切换视图 </a-button>
      <!-- <a-button type="link" style="padding: 0"> {{ currMapName }} </a-button>
      <a-icon
        :type="visible ? 'up' : 'down'"
        style="color: var(--ds-primary-color); font-size: 10px"
      /> -->
    </div>

    <template #content>
      <div class="map-list-popover">
        <DsGlobalListPage
          ref="refList"
          avatarGrid
          v-bind="bindProps"
          @on-click-title="handleClickTitle"
        >
          <template #avatar="record">
            <div class="map-list-popover-avatar" @click="() => handleClickTitle(record)">
              <DsImage :src="record.appThumbnail" />
              <div v-if="record.appId === appId" class="map-list-popover-avatar-selected" />
            </div>
          </template>
          <!-- 不要删除 -->
          <template #toolbar></template>
        </DsGlobalListPage>
      </div>
    </template>
  </a-popover>
</template>

<script>
import { DsGlobalListPage, DsImage } from "@daas/components";

export default {
  props: {
    customFilterProps: { type: Object, default: () => ({}) },
    currMapName: { type: String, default: "" },
  },
  provide: {
    isShowMultiSelected: false,
  },
  inject: ["reload"],
  components: { DsGlobalListPage, DsImage },
  data() {
    return {
      visible: false,
      appId: "",
    };
  },
  computed: {
    bindProps() {
      const { customFilterProps: cfp } = this;
      // 列表单个卡片展示映射
      const columns = [
        {
          ellipsis: true,
          title: "视图名称",
          dataIndex: "appName",
          scopedSlots: { customRender: "tableTitle" },
        },
        { ellipsis: true, title: "视图类型", dataIndex: "appTypeName" },
        {
          ellipsis: true,
          title: "拥有者",
          dataIndex: "ownerName",
          scopedSlots: { customRender: "ownerName" },
        },
        { ellipsis: true, title: "上次修改时间", dataIndex: "lastUpdateTime", width: 160 },
      ];
      return {
        request: async (params) => {
          params.appType = params.appType || "Analyze";

          params.appSubType = "map";

          if (params.viewType && Array.isArray(params.viewType)) {
            params.appSubType = params.viewType.join(",");
          }

          if (params && !params.viewType) delete params.viewType;
          if (params && !params.appSubType) delete params.appSubType;

          const res = await this.$http.get("/insightApp/app", {
            params: { ...params, ...cfp.requestParams },
          });

          return res;
        },
        vmodeData: ["grid"],
        vmode: "grid",
        showSider: false,
        modular: "Application",
        columns,
        rowSelection: null,
        showImage: (record) => {
          const thumbnail = record.appThumbnail || "";
          let url = "";

          if (thumbnail.startsWith("FILED")) {
            url = "/daasDMS/download/DownloadFile?attachmentId=" + thumbnail.replace("FILED", "");
          } else if (thumbnail.includes("/daasPortal/file/download")) {
            url = thumbnail;
          } else {
            url = "/daasPortal/file/download/" + thumbnail;
          }

          record.appThumbnail = url;

          return url;
        },
        actions: () => [],
        fieldNames: {
          id: "appId",
          name: "appName",
          desc: "appDesc",
          type: "appSubType",
        },
        filterDictionary: { appType: "Analyze" },
        toolBarProps: {
          batchActions: () => [],
        },
        filterToolbarActions: {
          appType: "Analyze",
        },
      };
    },
  },
  methods: {
    handleClickTitle(record) {
      this.$router.replace({
        path: `/${this.$route.params.dataViewType}`,
        query: {
          ...this.$route.query,
          appId: record.appId,
        },
      });
      this.appId = record.appId;

      this.reload();
      this.visible = false;
    },

    handleVisibleChange(visible) {
      if (visible) {
        this.appId = this.$dataView.dataView.value.appId;
      }
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.map-list-popover {
  position: relative;
  width: 720px;
  height: 600px;
  overflow: auto;

  &-header {
    // margin-left: 12px;
    display: flex;
    align-items: center;
    column-gap: 4px;
  }

  &-avatar {
    cursor: pointer;
    width: 100%;
    height: 100%;

    &-selected {
      position: absolute;
      top: -17px;
      left: -17px;
      width: 158px;
      height: 216px;
      // border: 1px solid var(--ds-primary-2);
      box-shadow: 0px 0px 8px 2px var(--ds-primary-2);
      border-radius: 4px;
      z-index: 1;
    }
  }

  :deep(.ant-card) {
    border-radius: 4px;
    &:hover {
      box-shadow: 0px 0px 6px 1px var(--ds-primary-2);
    }
  }

  :deep(.ds-image-container) {
    position: relative;
    .ant-avatar {
      overflow: hidden;
      border-radius: 4px;
      & > img {
        border-radius: 4px;
      }
    }
  }

  :deep(.ant-list-item-meta-title) {
    margin-bottom: 0;
  }

  :deep(.ds-global-list-item-action) {
    display: none;
  }
}
</style>

<style lang="less">
.map-list-popover-overlay {
  .ant-popover-inner-content {
    padding: 4px;
    padding-left: 0;
  }
}
</style>
