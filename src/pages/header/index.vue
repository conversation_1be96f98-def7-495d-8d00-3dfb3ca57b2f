<script>
import { defineComponent, ref, inject, getCurrentInstance, computed, reactive, toRefs } from "vue";
import { DsWorkToolbar, DsModal } from "@daas/components";
import { message as Message } from "ant-design-vue";
import Store from "@/store";

import MapList from "./map-list/index.vue";

import { getAllLayerConfig } from "@/utils/map-utils";
import Settings from "./settings/index.vue";

import { mapGetters } from "vuex";

export default defineComponent({
  components: { DsWorkToolbar, Settings, MapList, DsModal },
  props: {},
  computed: {
    ...mapGetters("setting", ["loadFailMsg"]),
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const getMap = inject("getMap");
    const reload = inject("reload");
    const miniTopBar = ref(false);
    const loading = ref(false);
    const handleCollapse = () => {
      miniTopBar.value = !miniTopBar.value;
    };
    const pageMode = computed(() => Store.getters["setting/pageMode"]);
    const currMapName = computed(() => proxy.$dataView.dataView.value.appName);
    const routeMode = computed(() => proxy.$route.params.dataViewType);

    const nameState = reactive({
      nameModalVisible: false,
      nameForm: {
        appName: "",
      },
      nameRules: {
        appName: [{ required: true, message: "请输入视图名称" }],
      },
    });

    function handleNameModalOk() {
      proxy.$dataView.update({ appName: nameState.nameForm.appName });
      nameState.nameModalVisible = false;
      saveAs();
    }

    function handleNameModalCancel() {
      nameState.nameModalVisible = false;
    }

    async function handleSavePageConfig() {
      loading.value = true;
      const map = getMap();
      const config = getAllLayerConfig();
      const _isStatic = Store.getters["setting/isStatic"];

      let _relatedList = [];

      !_isStatic &&
        config?.elements?.forEach((i) => {
          _relatedList.push({
            objectId: i.resourceId,
            type: "resource",
          });
        });

      config.settings = JSON.parse(JSON.stringify(map.exportConfig()));

      proxy.$dataView.updateDataViewConfig({ mapConfig: config, isStaticData: _isStatic });
      proxy.$dataView.update({ relatedList: _relatedList, type: "map" });
      await proxy.$dataView.save();

      Message.success("保存成功");
      loading.value = false;
    }

    function handleSaveAs() {
      nameState.nameModalVisible = true;
    }

    async function saveAs() {
      proxy.$dataView.update({
        appId: "",
      });

      await handleSavePageConfig();

      proxy.$router.replace({
        path: `/${proxy.$route.params.dataViewType}`,
        query: {
          ...proxy.$route.query,
          appId: proxy.$dataView.dataView.value.appId,
        },
      });

      reload();
    }

    return {
      loading,
      pageMode,
      routeMode,
      currMapName,
      miniTopBar,
      handleCollapse,

      ...toRefs(nameState),
      handleNameModalOk,
      handleNameModalCancel,

      handleSavePageConfig,
      handleSaveAs,
    };
  },
});
</script>

<template>
  <DsWorkToolbar :miniTopBar="miniTopBar" @collapse="handleCollapse">
    <template #left>
      <div style="font-size: 16px">{{ currMapName || "地图分析" }}</div>
      <MapList v-if="routeMode === 'usage'" :currMapName="currMapName" />
    </template>
    <template v-if="!loadFailMsg" #right>
      <a-space>
        <Settings />
        <a-button
          v-if="pageMode === 'edit'"
          type="primary"
          :size="miniTopBar ? 'small' : 'middle'"
          :loading="loading"
          @click="handleSavePageConfig"
          >保存</a-button
        >
        <a-button
          v-else-if="routeMode === 'usage'"
          type="primary"
          :size="miniTopBar ? 'small' : 'middle'"
          :loading="loading"
          @click="handleSaveAs"
          >另存为</a-button
        >
      </a-space>
      <DsModal
        :visible="nameModalVisible"
        width="420px"
        title="另存为"
        @ok="handleNameModalOk"
        @cancel="handleNameModalCancel"
      >
        <a-form :model="nameForm" :rules="nameRules" layout="vertical">
          <a-form-item label="视图名称" name="appName">
            <a-input v-model="nameForm.appName" placeholder="请输入视图名称" />
          </a-form-item>
        </a-form>
      </DsModal>
    </template>
  </DsWorkToolbar>
</template>

<style lang="less" scoped></style>
