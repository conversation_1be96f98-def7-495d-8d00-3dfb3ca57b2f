<script>
import { defineComponent, reactive, toRefs } from "vue";
import ColorPicker from "@/components/color-picker/index.vue";

export default defineComponent({
  components: { ColorPicker },
  props: {},
  setup() {
    // 表单相关数据
    const formData = reactive({
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      form: {
        showBackground: false,
        backgroundColor: "",
        backgroundSize: 1,
        backgroundBlur: 0,
        backgroundPositionX: 0,
        backgroundPositionY: 0,
      },
    });

    return {
      ...toRefs(formData),
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="背景颜色">
      <ColorPicker v-model="form.backgroundColor" size="small" />
    </a-form-model-item>
    <a-form-model-item label="背景大小">
      <a-input-number v-model="form.backgroundSize" style="width: 100%" size="small" />
    </a-form-model-item>
    <a-form-model-item label="背景模糊">
      <a-input-number v-model="form.backgroundBlur" style="width: 100%" size="small" />
    </a-form-model-item>
    <a-form-model-item label="背景偏移">
      <div style="display: flex; align-items: center">
        <a-input-number v-model="form.backgroundPositionX" style="width: 100%" size="small" />
        <span>-</span>
        <a-input-number v-model="form.backgroundPositionY" style="width: 100%" size="small" />
      </div>
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
