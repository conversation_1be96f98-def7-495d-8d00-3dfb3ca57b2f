<template>
  <a-modal
    title="封面裁剪"
    :visible="visible"
    :width="760"
    :footer="null"
    :zIndex="2000"
    :bodyStyle="{
      'min-height': '500px',
      overflow: 'hidden',
      'overflow-y': 'auto',
    }"
    :centered="true"
    @cancel="$emit('cancel')"
    @ok="handleOk"
    @click.stop
  >
    <a-row class="row-top" style="margin: 0" :gutter="24">
      <!-- 图片 -->
      <a-col class="col-cropper" :span="16" style="padding-left: 0">
        <img ref="cropperimg" class="img" :src="img" />
      </a-col>
      <!-- 预览框 -->
      <a-col class="col-img" :span="8" style="padding-right: 0">
        <div class="img-ground">
          <div class="preview"></div>
        </div>
      </a-col>
    </a-row>

    <a-row style="margin: 0; margin-top: 24px" :gutter="24">
      <a-col class="col-icon" :span="16">
        <!-- 放大缩小 -->
        <a-button-group class="button-group">
          <a-tooltip placement="topLeft" title="放大" arrow-point-at-center>
            <a-button
              icon="zoom-in"
              type="primary"
              @click="zoomIn()"
              class="icon zoom-in"
            ></a-button>
          </a-tooltip>
          <a-tooltip placement="topLeft" title="缩小" arrow-point-at-center>
            <a-button
              icon="zoom-out"
              type="primary"
              @click="zoomOut()"
              class="icon zoom-out"
            ></a-button>
          </a-tooltip>
        </a-button-group>
        <!-- 上下左右 -->
        <a-button-group class="button-group">
          <a-tooltip placement="topLeft" title="向左" arrow-point-at-center>
            <a-button
              icon="arrow-left"
              type="primary"
              @click="moveLeft()"
              class="icon left"
            ></a-button>
          </a-tooltip>
          <a-tooltip placement="topLeft" title="向右" arrow-point-at-center>
            <a-button
              icon="arrow-right"
              type="primary"
              @click="moveRight()"
              class="icon right"
            ></a-button>
          </a-tooltip>
          <a-tooltip placement="topLeft" title="向上" arrow-point-at-center>
            <a-button icon="arrow-up" type="primary" @click="moveUp()" class="icon up"></a-button>
          </a-tooltip>
          <a-tooltip placement="topLeft" title="向下" arrow-point-at-center>
            <a-button
              icon="arrow-down"
              type="primary"
              @click="moveDown()"
              class="icon down"
            ></a-button>
          </a-tooltip>
        </a-button-group>

        <a-button-group class="button-group">
          <a-tooltip placement="topLeft" title="重置" arrow-point-at-center>
            <a-button icon="reload" type="primary" @click="reload()" class="icon reload"></a-button>
          </a-tooltip>
        </a-button-group>
      </a-col>
      <a-col :span="8" class="col-icon" style="padding-right: 0">
        <a-button class="handleOk" @click="handleOk()" type="primary">确认裁剪</a-button>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script>
import Cropper from "cropperjs";
import "cropperjs/dist/cropper.min.css";

export default {
  props: {
    img: { type: String, default: "" }, //裁剪的图片
    visible: { type: Boolean, default: false }, //是否可见******
    // value: {}, //此处的获取的value对应的是组件标签中的v-model
  },
  data() {
    return {
      previews: {},
      option: {
        img: "", //裁剪图片的地址
      },
      imgUrl: "",
      cropImage: null,
    };
  },
  mounted() {
    //获取图片
    this.$nextTick(() => {
      var $image = this.$refs.cropperimg;
      //Cropperjs配置
      this.cropImage = new Cropper($image, {
        aspectRatio: 2 / 1, //设置裁剪框的的宽高比例(默认随意改变大小)
        viewMode: 0, //定义cropper的视图模式(默认为0，值：0,1,2,3)
        dragMode: "move", //定义cropper的拖拽模式(默认为crop，值：crop，move，none)
        preview: ".preview", //预览框，type：element/Sting，默认：""
        responsive: true, //在调整窗口大小的时候重新渲染cropper
        checkCrossOrigin: true, //检查当前图像是否为跨域图像果
        modal: true, //是否显示裁剪时的黑遮罩层，类似mask效。
        guides: true, //是否在显示裁剪框内的九宫格虚线
        center: true, //是否显示裁剪框中心点
        highlight: false, //高亮裁剪框
        background: true, //是否显示容器的背景(就是图片后面的马赛克)
        autoCrop: true, //初始化时，是否自动显示裁剪框
        movable: true, //是否允许移动图片
        rotatable: false, //是否允许旋转图片
        scalable: true, //是否开启图片缩放功能，默认开启
        zoomable: true, //是否开启图片可缩放功能，默认开启，如果关闭后，鼠标滚轮，和触摸放大缩小将不可用
        zoomOnWheel: true, //是否可以通过移动鼠标来放大图片
        cropBoxMovable: true, //是否通过拖拽来移动裁剪框
        cropBoxResizable: true, //是否通过拖动来调整裁剪框的大小
        toggleDragModeOnDblclick: true, //双击鼠标可以在crop和move之间切换拖拽模式
        // autoCropArea: 0.8,		//定义自动裁剪面积大小(百分比)和图片进行对比
        // zoomOnTouch:true,		//是否可以通过拖动触摸来放大图像（移动端）
        // wheelZoomRatio: 0.1,	//用鼠标移动图像时，定义缩放比例，type：number
        // restore:false,			//在调整窗口大小后恢复裁剪的区域
        // minContainerHeight:200,	//容器的最小高度
        // minContainerWidth:200,	//容器的最小宽度
        // minCanvasWidth:0,		//Canvans的最小宽度
        // minCanvasHeight:0,		//Canvans的最小高度
        // minCropBoxHeight:0,		//裁剪框的最小高度
        // //ready:null,			//插件准备完成执行的函数（只执行一次）
        // cropstart:null,			//裁剪框开始移动执行的函数,type:function
        // cropmove:null,			//裁剪框移动时执行的函数
        // cropend:null,			//裁剪框移动结束执行的函数
        // crop:null,				//裁剪框发生变化执行的函数
        // zoom:null,				//裁剪框缩放的时候执行的函数

        // eslint-disable-next-line no-unused-vars
        crop(event) {
          // console.log(event.detail.x);
          // console.log(event.detail.y);
          // console.log(event.detail.width);
          // console.log(event.detail.height);
          // console.log(event.detail.rotate);
          // console.log(event.detail.scaleX);
          // console.log(event.detail.scaleY);
        },
      });
    });
  },
  methods: {
    //重置
    reload() {
      this.cropImage.reset();
      console.log("reload!");
    },
    //放大
    zoomIn() {
      this.cropImage.zoom(0.1);
    },
    //缩小
    zoomOut() {
      this.cropImage.zoom(-0.1);
    },
    //上移
    moveUp() {
      this.cropImage.move(0, -10);
    },
    //下移
    moveDown() {
      this.cropImage.move(0, 10);
    },
    //左移
    moveLeft() {
      this.cropImage.move(-10, 0);
    },
    //右移
    moveRight() {
      this.cropImage.move(10, 0);
    },
    //确认裁剪
    handleOk() {
      var croppedImage = this.cropImage.getCroppedCanvas(); //获取裁剪后的canvans
      if (croppedImage) {
        var base64url = croppedImage.toDataURL("image/jpg"); //转换为base64地址形式
        // var imageblob = croppedImage.toBlob()//生成blob格式
        this.$emit("imageOk", base64url);
      } else {
        this.$emit("imageOk");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.img {
  display: block;
  max-width: 100%;
}
.preview {
  width: 100%;
  height: 140px;
  overflow: hidden;
}
//以下是旧的
.row-top {
  width: 100%;
  margin: 0;
}
.img-ground {
  background-color: var(--ds-background-color-light);
}
.col-cropper {
  height: 400px;
  padding-left: 0;
}
.col-img {
  align-items: center;
  height: 400px;
}
.button-group {
  margin: 0 10px;
}
.col-icon {
  text-align: center;
}
/deep/ .cropper-point.point-se {
  width: 5px;
  height: 5px;
}
</style>
