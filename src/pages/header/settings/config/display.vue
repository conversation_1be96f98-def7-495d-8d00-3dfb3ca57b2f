<script>
import { inject, defineComponent, ref, onUnmounted } from "vue";
import { throttle } from "lodash-es";
import MapSelect from "@/components/map-select/index.vue";
import { mapGetters } from "vuex";

import eventBus, { eventMap } from "@/utils/event-bus";
import Lifecycle from "@/utils/lifecycle";

export default defineComponent({
  components: { MapSelect },
  props: {},
  computed: {
    ...mapGetters("setting", ["canEditPage"]),
  },
  setup() {
    const getMap = inject("getMap");
    let map = getMap();
    const form = ref({
      center: {
        lat: 0,
        lng: 0,
      },
      zoom: 6,
      markProvince: "",
    });

    function initMap() {
      map = getMap();
      form.value = {
        ...map.settings,
      };
    }

    if (map) {
      initMap();
    } else {
      Lifecycle.onMapInit(() => {
        initMap();
      });
    }

    eventBus.on(eventMap.provincePolygonConfigChange, () => {
      initMap();
    });
    eventBus.on(eventMap.mapSettingsChange, () => {
      form.value = {
        ...map.settings,
      };
    });

    const openCenterModal = () => {
      // map.openCenterModal();
      // store.commit("setting/setIsChangeCenter", true);
    };

    const handleDragChange = () => {
      map.targetDragging();
    };

    const handleZoomChange = throttle((val) => {
      map.setZoom(val);
    }, 1000);

    function handleMapTypeChange(value) {
      map.markProvince(value, {
        autoViewport: true,
      });
    }

    onUnmounted(() => {
      handleZoomChange?.cancel?.();
    });

    return {
      form,
      handleDragChange,
      handleZoomChange,
      openCenterModal,
      handleMapTypeChange,
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
    <a-form-model-item label="地图中心">
      <a-button type="link" @click="openCenterModal" style="padding: 0">
        {{ form.center.lng }}, {{ form.center.lat }}
      </a-button>
    </a-form-model-item>
    <a-form-model-item label="缩放层级">
      <a-input-number
        v-if="canEditPage"
        v-model="form.zoom"
        :min="3"
        :max="9"
        style="width: 100%"
        size="small"
        @change="handleZoomChange"
      />
      <span v-else>{{ form.zoom }}</span>
    </a-form-model-item>
    <a-form-model-item label="内置图形">
      <MapSelect v-model="form.markProvince" @change="handleMapTypeChange" />
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
