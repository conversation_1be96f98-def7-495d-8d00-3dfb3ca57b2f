<script>
import { defineComponent, reactive, toRefs, inject } from "vue";
import ColorPicker from "@/components/color-picker/index.vue";
import Lifecycle from "@/utils/lifecycle";

export default defineComponent({
  components: { ColorPicker },
  props: {},
  setup() {
    const getMap = inject("getMap");
    let map = getMap();
    let provincePolygon = null;
    // 表单相关数据
    const formData = reactive({
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      form: {
        strokeColor: "#ff0000", // 边框颜色
        fillColor: "#ff0000", // 填充颜色
        strokeOpacity: 1, // 边框透明度
        fillOpacity: 0.5, // 填充透明度
        strokeWeight: 1, // 边框宽度
        strokeStyle: "solid", // 边框样式
      },
    });

    function initMap() {
      map = getMap();
      provincePolygon = map.provincePolygon;
      formData.form = provincePolygon.hightLightStyle;
    }

    if (map) {
      initMap();
    } else {
      Lifecycle.onMapInit(() => {
        initMap();
      });
    }

    function styleChange(key, value) {
      formData.form[key] = value;
      provincePolygon.changeHightLightStyle({ [key]: value });
    }

    return {
      ...toRefs(formData),
      styleChange,
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="颜色">
      <ColorPicker
        v-model="form.fillColor"
        size="small"
        @change="(color) => styleChange('fillColor', color)"
      />
    </a-form-model-item>
    <a-form-model-item label="边框颜色">
      <ColorPicker
        v-model="form.strokeColor"
        size="small"
        @change="(color) => styleChange('strokeColor', color)"
      />
    </a-form-model-item>
    <a-form-model-item label="边框宽度">
      <a-input-number
        v-model="form.strokeWeight"
        style="width: 100%"
        size="small"
        :max="10"
        :min="1"
        @change="(value) => styleChange('strokeWeight', value)"
      />
    </a-form-model-item>
    <a-form-model-item label="边框透明">
      <a-input-number
        v-model="form.strokeOpacity"
        style="width: 100%"
        size="small"
        :max="1"
        :min="0"
        :step="0.1"
        @change="(value) => styleChange('strokeOpacity', value)"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
