<script>
import {
  defineComponent,
  toRefs,
  ref,
  getCurrentInstance,
  nextTick,
  watchEffect,
  computed,
  reactive,
  onUnmounted
} from "vue";
import { DsUpload } from "@daas/components";
import { debounce } from 'lodash-es'
import CropperModal from "./crpoperjs-modal.vue";
import domToImg from "dom-to-image";

export default defineComponent({
  components: {
    CropperModal,
    UploadFile: DsUpload.UploadFile,
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const uploadFileRef = ref(null);

    // 表单相关数据
    const formData = reactive({
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      form: {
        appName: "",
        iconId: "",
        imgUrl: "",
      },
    });

    // 裁剪相关数据
    const cropperData = {
      loading: ref(false),
      cropperImg: ref(""),
      cropperVisible: ref(false),
      popoverVisible: ref(true),
      cropperImgUrl: computed(() => cropperData.cropperImg.value),
    };

    // 初始化数据
    const initData = {
      // 初始化应用名称
      initAppName: () => {
        formData.form.appName = JSON.parse(JSON.stringify(proxy.$dataView.dataView.value.appName));
      },

      // 初始化图片URL
      initImgUrl: () => {
        const { appThumbnail } = proxy.$dataView.dataView.value;
        const regex = /^\/\w+(\/\w+)*(\?.*)?$/;
        formData.form.iconId = appThumbnail
          ? regex.test(appThumbnail)
            ? appThumbnail
            : `/daasPortal/file/download/${appThumbnail}`
          : null;

        nextTick(() => {
          fileHandler.setUploadRefUrl(formData.form.iconId);
        });
      },
    };

    // 文件处理相关方法
    const fileHandler = {
      // 移除图片
      removeImg: () => {
        formData.form.iconId = null;
        formData.form.imgUrl = null;
        nextTick(() => {
          dataHandler.updateIconData();
        });
      },

      // 设置上传组件URL
      setUploadRefUrl: (url) => {
        if (!url || url.includes("null") || !uploadFileRef.value) return;
        uploadFileRef.value.initImages([url]);
      },

      // 获取Base64
      getBase64: (img, callback) => {
        const reader = new FileReader();
        reader.addEventListener("load", () => callback(reader.result));
        reader.readAsDataURL(img);
      },

      // Base64转File
      dataURLtoFile: (dataUrl) => {
        const arr = dataUrl.split(",");
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = window.atob(arr[1]);
        const n = bstr.length;
        const u8arr = new Uint8Array(n);

        for (let i = 0; i < n; i++) {
          u8arr[i] = bstr.charCodeAt(i);
        }

        const fileName = `file_${Date.now()}.png`;
        return new File([u8arr], fileName, { type: mime });
      },
    };

    // 数据处理相关方法
    const dataHandler = {
      // 更新图标数据
      updateIconData: () => {
        const appThumbnail = formData.form.iconId || "";

        proxy.$dataView?.update({ appThumbnail });

        proxy.$setWorkplaceState?.(
          {
            key: "setPage",
            value: { appThumbnail },
          },
          {
            autoSave: false,
          },
        );
      },

      // 上传文件
      uploadFile: async (imgUrl) => {
        const _formData = new FormData();
        const file = fileHandler.dataURLtoFile(imgUrl);
        _formData.append("file", file);

        const res = await proxy.$http.post("/daasPortal/file/upload", _formData);
        formData.form.iconId = res.id;

        dataHandler.updateIconData();
      },
    };

    // 事件处理相关方法
    const eventHandler = {
      // 处理上传变更
      handleUploadChange: (fileList) => {
        if (!fileList?.length) return;

        fileHandler.getBase64(fileList[0].originFileObj, (imgUrl) => {
          dataHandler.uploadFile(imgUrl);
        });
        fileList.splice(0);
      },

      // 获取缩略图配置
      getConfigThumbnail: async (dom) => {
        return await domToImg.toPng(dom, {
          filter: (node) =>
            typeof node.className === "object" ||
            !node.className ||
            (!node.className.includes("BMap_noprint") && !node.className.includes("anchorBL")),
        });
      },

      // 处理截图
      handleScreenshot: async () => {
        if (cropperData.loading.value) cropperData.loading.value = true;

        cropperData.cropperImg.value = await eventHandler.getConfigThumbnail(
          document.getElementById("scaleContent"),
        );
        cropperData.cropperVisible.value = true;
        cropperData.popoverVisible.value = false;

        if (cropperData.loading.value) cropperData.loading.value = false;
      },

      // 处理图片确认
      imageOk: async (imgUrl) => {
        if (imgUrl) {
          await dataHandler.uploadFile(imgUrl);
        }

        cropperData.cropperVisible.value = false;
        cropperData.popoverVisible.value = true;
      },

      // 更新应用名称
      updateAppName: debounce((val) => {
        proxy.$dataView?.update({ appName: val || '未命名地图' });
      }, 500),
    };

    watchEffect(() => {
      eventHandler.updateAppName(formData.form.appName);
    });

    // 监听数据变化
    watchEffect(() => {
      if (proxy.$dataView.dataView.value) {
        initData.initAppName();
        initData.initImgUrl();
      }
    });

    onUnmounted(() => {
      eventHandler.updateAppName.cancel?.();
    })

    return {
      uploadFileRef,
      ...toRefs(formData),
      ...cropperData,
      removeImg: fileHandler.removeImg,
      handleUploadChange: eventHandler.handleUploadChange,
      handleScreenshot: eventHandler.handleScreenshot,
      imageOk: eventHandler.imageOk,
    };
  },
});
</script>

<template>
  <div class="view-config">
    <!-- 表单 -->
    <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-model-item label="视图名称：">
        <a-input v-model="form.appName" size="small" />
      </a-form-model-item>
      <a-form-model-item label="视图封面：">
        <UploadFile
          style="z-index: 10; position: relative; width: 106px; height: 100px; margin-top: 7px"
          ref="uploadFileRef"
          accept=".jpg, .jpeg, .png, .gif"
          :urls="[form.iconId]"
          :limit="1"
          :remove="removeImg"
          @change="handleUploadChange"
        />
        <a-button
          type="link"
          class="cutPage"
          style="z-index: 11"
          @click="() => handleScreenshot(true)"
        >
          截取封面
        </a-button>
      </a-form-model-item>
    </a-form-model>

    <!-- 图片裁剪工具 -->
    <CropperModal
      v-if="cropperVisible && cropperImgUrl"
      :visible="cropperVisible"
      :img="cropperImgUrl"
      @imageOk="imageOk"
      @cancel="cropperVisible = false"
    />
  </div>
</template>

<style lang="less" scoped>
.view-config {
  :deep(.ant-btn) {
    padding: 0;
  }
}
</style>
