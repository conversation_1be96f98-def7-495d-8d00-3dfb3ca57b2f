<script>
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
  components: {},
  props: {},
  setup() {
    // 表单相关数据
    const formData = reactive({
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      form: {
        timeType: "",
        timeRange: [],
      },
      timeTypeOptions: [
        { value: "day", label: "日" },
        { value: "month", label: "月" },
        { value: "", label: "季" },
        { value: "year", label: "年" },
      ],
    });

    return {
      ...toRefs(formData),
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="时间类型">
      <a-select v-model="form.timeType" :options="timeTypeOptions" size="small" />
    </a-form-model-item>
    <a-form-model-item label="开始时间">
      <a-date-picker v-model="form.timeRange[0]" type="date" size="small" />
    </a-form-model-item>
    <a-form-model-item label="结束时间">
      <a-date-picker v-model="form.timeRange[1]" type="date" size="small" />
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
