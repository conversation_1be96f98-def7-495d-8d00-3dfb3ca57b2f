<script>
import { defineComponent, reactive, toRefs, inject } from "vue";
import ColorPicker from "@/components/color-picker/index.vue";
import Lifecycle from "@/utils/lifecycle";
import eventBus, { eventMap } from "@/utils/event-bus";

export default defineComponent({
  components: { ColorPicker },
  props: {},
  setup() {
    const getMap = inject("getMap");
    let map = getMap();

    // 表单相关数据
    const formData = reactive({
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      form: {
        color: "",
        fontSize: "",
        textStrokeColor: "",
      },
    });

    function initMap() {
      map = getMap();
      formData.form = map.provincePolygon.labelStyle;
    }

    if (map) {
      initMap();
    } else {
      Lifecycle.onMapInit(() => {
        initMap();
      });
    }

    eventBus.on(eventMap.provincePolygonConfigChange, ({ labelStyle }) => {
      formData.form = labelStyle;
    });

    function changeLabelStyle(style) {
      map.provincePolygon.changeLabelStyle(style);
    }

    return {
      ...toRefs(formData),
      changeLabelStyle,
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-form-model-item label="颜色">
      <ColorPicker
        v-model="form.color"
        size="small"
        @change="(color) => changeLabelStyle({ color })"
      />
    </a-form-model-item>
    <a-form-model-item label="字体大小">
      <a-input-number
        v-model="form.fontSize"
        style="width: 100%"
        size="small"
        :min="10"
        @change="(fontSize) => changeLabelStyle({ fontSize })"
      />
    </a-form-model-item>
    <a-form-model-item label="文字边缘">
      <ColorPicker
        v-model="form.textStrokeColor"
        size="small"
        @change="(color) => changeLabelStyle({ textStrokeColor: color })"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
