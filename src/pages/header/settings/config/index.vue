<script>
import { defineComponent, ref, inject } from "vue";
import BaseView from "./view.vue";
import Display from "./display.vue";
import Time from "./time.vue";
import Layer from "./layer.vue";
import LayerHightlight from "./layer-hightlight.vue";
import Label from "./label.vue";
import lifecycle from "@/utils/lifecycle";
import eventBus, { eventMap } from "@/utils/event-bus";
import { mapGetters } from "vuex";

export default defineComponent({
  components: { BaseView, Display, Time, Layer, LayerHightlight, Label },
  props: {},
  computed: {
    ...mapGetters("setting", ["canEditPage"]),
  },
  setup() {
    const getMap = inject("getMap");
    let map = getMap();
    let isHLPorvince = ref(false);

    lifecycle.onMapInit(() => {
      map = getMap();
    });

    const activeKey = ref(["view", "display", "time"]);

    function changeLabelVisible(visible) {
      map.provincePolygon.changeLabelVisible(visible);
    }

    function initMapConfig() {
      const center = map.getCenter();
      const zoom = map.getZoom();
      map.setCenter(center);
      map.setZoom(zoom);
    }

    eventBus.on(eventMap.provincePolygonConfigChange, (config) => {
      isHLPorvince.value = config.isHightLight;
    });

    return {
      activeKey,
      isHLPorvince,
      changeLabelVisible,
      initMapConfig,
    };
  },
});
</script>

<template>
  <a-collapse class="config-collapse" :activeKey="activeKey" :bordered="false">
    <a-collapse-panel key="view" header="视图配置">
      <BaseView />
    </a-collapse-panel>
    <a-collapse-panel key="display" header="地图展示配置">
      <div v-if="canEditPage" slot="extra" @click.stop>
        <a-button type="link" size="small" @click="initMapConfig">初始化中心点、缩放</a-button>
      </div>
      <Display />
    </a-collapse-panel>
    <!-- <a-collapse-panel key="time" header="时间播放条">
      <div slot="extra" @click.stop="null">
        <a-switch checked-children="显示" un-checked-children="隐藏" size="small" default-checked />
      </div>
      <Time />
    </a-collapse-panel> -->
    <a-collapse-panel v-if="isHLPorvince" key="layer" header="图层样式">
      <Layer />
    </a-collapse-panel>
    <a-collapse-panel v-if="isHLPorvince" key="layer-hightlight" header="高亮样式">
      <LayerHightlight />
    </a-collapse-panel>
    <a-collapse-panel v-if="isHLPorvince" key="label" header="文字样式">
      <div slot="extra" @click.stop>
        <a-switch
          checked-children="显示"
          un-checked-children="隐藏"
          size="small"
          default-checked
          @change="changeLabelVisible"
        />
      </div>
      <Label />
    </a-collapse-panel>
  </a-collapse>
</template>

<style lang="less" scoped>
.config-collapse {
  :deep(.ant-form-item) {
    margin-bottom: 0px;
  }
  :deep(.ant-collapse-header) {
    padding: 8px 12px;
    padding-left: 26px;
    .ant-collapse-arrow {
      left: 8px;
    }
  }
  :deep(.ant-collapse-content-box) {
    padding-top: 0 !important;
    padding-bottom: 8px;
    padding-left: 26px;
  }
}
</style>
