<script>
import { defineComponent, ref, computed, getCurrentInstance } from "vue";
import List from "./list.vue";
import AddModal from "./add-modal/index.vue";
import Store from "@/store";

export default defineComponent({
  components: {
    List,
    AddModal,
  },
  props: {},
  computed: {
    layerNum() {
      return this.$store.getters["layers"].length;
    },
  },
  setup() {
    const { proxy } = getCurrentInstance();
    // 控制添加图层弹窗的显示状态
    const addModalVisible = ref(false);
    const layerConfig = ref();
    const canEditPage = computed(() => Store.getters["setting/canEditPage"]);
    const routeMode = computed(() => proxy.$route.params.dataViewType);

    // 打开添加图层弹窗
    function openAddModal() {
      addModalVisible.value = true;
    }

    // 关闭添加图层弹窗
    function closeAddModal() {
      addModalVisible.value = false;
      layerConfig.value = undefined;
    }

    function openLayerConfig(layer) {
      layerConfig.value = layer;
      openAddModal();
    }

    // 添加图层
    function handleAddOk() {
      closeAddModal();
    }

    return {
      routeMode,
      canEditPage,
      layerConfig, // 当前图层
      addModalVisible, // 弹窗显示状态
      openAddModal, // 打开弹窗方法
      closeAddModal, // 关闭弹窗方法
      handleAddOk, // 添加图层
      openLayerConfig, // 打开图层配置弹窗
    };
  },
});
</script>

<template>
  <div class="layer-wrapper">
    <div class="layer-header">
      <span>{{ layerNum }}个图层</span>
      <a-button v-if="canEditPage || routeMode === 'usage'" size="small" @click="openAddModal"
        >添加</a-button
      >
    </div>
    <List @openLayerConfig="openLayerConfig" />
    <!-- 添加图层弹窗 -->
    <AddModal
      v-if="addModalVisible"
      :layerConfig="layerConfig"
      @cancel="closeAddModal"
      @ok="handleAddOk"
    />
  </div>
</template>

<style lang="less" scoped>
.layer {
  &-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  &-header {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #f0f1f2;
    border-radius: 4px 4px 0 0;
  }
}
</style>
