<script>
import { defineComponent, ref, watchEffect } from "vue";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Polygon } from "@/components/setting-forms/index.js";

export default defineComponent({
  components: {
    <PERSON><PERSON><PERSON>,
    <PERSON>er,
    Polygon,
  },
  props: {
    layer: {
      type: Object,
      required: true,
      default: undefined,
    },
  },
  setup(props) {
    let config = {
      polyline: {
        comp: <PERSON><PERSON><PERSON>,
        name: "线",
      },
      marker: {
        comp: <PERSON><PERSON>,
        name: "点",
      },
      polygon: {
        comp: Polygon,
        name: "面",
      },
    };
    let comp = ref();
    let styleConfig = ref({});
    let layerTypeName = ref("");

    watchEffect(() => {
      const curr = config[props.layer?.type || "marker"];
      comp.value = curr.comp;
      layerTypeName.value = curr.name;
      styleConfig.value = props.layer.layerConfig.styleConfig;
    });

    function change(config) {
      props.layer.updateCllConfig(config);
    }

    function ok() {
      return props.layer;
    }

    return {
      comp,
      styleConfig,
      layerTypeName,
      change,
      ok,
    };
  },
});
</script>

<template>
  <div class="style-config-wrapper">
    <div class="style-config-label">样式配置-{{ layerTypeName }}</div>
    <component :is="comp" :layer="layer" :config="styleConfig" @change="change" />
  </div>
</template>

<style lang="less" scoped>
.style-config-wrapper {
  width: 40%;
  margin: 0 auto;
  .style-config-label {
    position: relative;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 24px;
    padding-left: 8px;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 2px;
      width: 4px;
      height: 60%;
      background-color: var(--ds-primary-color);
    }
  }
}
</style>
