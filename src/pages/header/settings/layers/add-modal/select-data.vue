<script>
import { ref, defineComponent, watchEffect } from "vue";
import { DsDataQuery } from "@daas/components";
import { message as Message } from "ant-design-vue";

export default defineComponent({
  name: "FilterConfig",
  components: {
    DsDataQuerySetting: DsDataQuery.Setting,
  },
  props: {
    resourceInfo: {
      type: Object,
      default: undefined,
    },
  },
  emits: ["selectDataSet"],
  setup(props, { emit }) {
    let localConfig = ref({});
    watchEffect(() => {
      if (props.resourceInfo?.resourceId && !localConfig.value?.resourceId) {
        localConfig.value = { resourceId: props.resourceInfo?.resourceId };
      }
    });

    function ok() {
      if (!localConfig.value) {
        Message.warning("请先选择数据");
        return false;
      }

      emit("selectDataSet", localConfig.value);
      return localConfig.value;
    }

    function selectDataSet(params) {
      localConfig.value = params?.tableObj || {};
    }

    return {
      localConfig,

      ok,
      selectDataSet,
    };
  },
});
</script>

<template>
  <div class="filter-outer">
    <DsDataQuerySetting
      isShowSelectDataSourceBtn
      isRefreshData
      :config="localConfig"
      :isShowLimit="false"
      :showTypes="['daas']"
      :extraParams="{isGeoData: '1'}"
      @selectDataSet="selectDataSet"
    />
  </div>
</template>

<style lang="less" scoped>
.filter-outer {
  border: 1px solid var(--ds-border-color-base);
  border-radius: 2px;
  height: 100%;
}
</style>
