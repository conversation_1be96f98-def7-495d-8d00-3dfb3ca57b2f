<script>
import { defineComponent, ref, computed, inject } from "vue";
import { DsModal } from "@daas/components";
import SelectData from "./select-data.vue";
import BindData from "./bind-data.vue";
import StyleConfig from "./style-config.vue";
import Lifecycle from "@/utils/lifecycle";
import Layer from "@/model/layer";
import { watchEffect } from "vue";

export default defineComponent({
  components: {
    DsModal,
    SelectData,
    BindData,
    StyleConfig,
  },
  props: {
    layerConfig: {
      type: Object,
      default: () => undefined,
    },
  },
  emits: ["ok", "cancel"],
  setup(props, { emit }) {
    // 注入从父组件提供的初始化图层方法
    const initLayerByResource = inject("initLayerByResource");
    // 注入获取地图实例的方法
    const getMap = inject("getMap");
    let map = getMap();
    // 当前图层实例
    let layer = ref();
    // 当前是否编辑
    const isEdit = computed(() => !!props.layerConfig);
    // 弹窗名称
    const modalTitle = computed(() => (isEdit.value ? "编辑图层" : "添加图层"));

    // 资源信息
    let resourceInfo = ref();
    // 临时存储资源ID,用于避免重复请求
    let tempResourceId = null;
    // 获取资源详情
    async function getResourceInfo(resourceId) {
      if (tempResourceId === resourceId) return true;

      tempResourceId = resourceId;
      // 初始化新的图层实例
      layer.value = new Layer(map, {
        isStatic: false,
        showEntityDetailCallBack: () => {},
      });
      await layer.value.init({ resourceId });
      resourceInfo.value = layer.value.getResourceInfo();
      layer.value.layerConfig.name = resourceInfo.value.resourceName;
      return true;
    }

    // 初始化地图实例
    function initMap() {
      map = getMap();

      watchEffect(() => {
        if (!props.layerConfig) return;
        layer.value = props.layerConfig;
        resourceInfo.value = layer.value.getResourceInfo();
        tempResourceId = resourceInfo.value.resourceId;
      });
    }

    // 如果地图实例不存在,等待地图初始化完成后再执行
    if (map) {
      initMap();
    } else {
      Lifecycle.onMapInit(async () => {
        initMap();

        // 测试
        // await getResourceInfo("02441b7e-6985-47e3-8b3c-1eea6a19c6ca");
        // currentStep.value += 1;

        // const testPolygonLayerConfig = {
        //   name: "测试地图视图",
        //   displayName: "测试地图视图",
        //   type: "polygon",
        //   columns: {
        //     columnConfig: [],
        //   },
        //   resourceId: "02441b7e-6985-47e3-8b3c-1eea6a19c6ca",
        //   mappingConfig: [
        //     {
        //       name: "id",
        //       displayName: "主键",
        //       mapColumnName: "m_id",
        //       columnType: "idColumn",
        //       configure: true,
        //     },
        //     {
        //       name: "name",
        //       displayName: "名称",
        //       mapColumnName: "m_name",
        //       columnType: "nameColumn",
        //       configure: true,
        //     },
        //     {
        //       name: "pointList",
        //       displayName: "点集",
        //       configure: true,
        //       mapColumnName: "m_ps",
        //       columnType: "pointListColumn",
        //     },
        //     {
        //       name: "centerPoint",
        //       displayName: "中心点",
        //       configure: true,
        //       mapColumnName: "m_center",
        //       columnType: "centerPointColumn",
        //     },
        //   ],
        //   mappingObj: {
        //     id: "m_id",
        //     name: "m_name",
        //     pointList: "m_ps",
        //     centerPoint: "m_center",
        //   },
        //   coverCells: [],
        //   coverCellsObj: {},
        //   styleConfig: {
        //     strokeColor: "#096dd9",
        //     fillColor: "#40a9ff",
        //     fillOpacity: 0.6,
        //     strokeWeight: 2,
        //     strokeOpacity: 0.8,
        //     strokeStyle: "solid",
        //   },
        // };
        // const testMarkerLayerConfig = {
        //   name: "测试地图视图-点",
        //   displayName: "测试地图视图-点",
        //   type: "marker",
        //   columns: {
        //     columnConfig: [],
        //   },
        //   resourceId: "2fd61413-b1a5-40f7-8aa9-3ba1f70c90de",
        //   mappingConfig: [
        //     {
        //       name: "id",
        //       displayName: "主键",
        //       mapColumnName: "m_id",
        //       columnType: "idColumn",
        //       configure: true,
        //     },
        //     {
        //       name: "name",
        //       displayName: "名称",
        //       configure: true,
        //       mapColumnName: "m_name",
        //       columnType: "nameColumn",
        //     },
        //     {
        //       name: "lng",
        //       displayName: "经度",
        //       configure: true,
        //       mapColumnName: "m_lat",
        //       columnType: "longitudeColumn",
        //     },
        //     {
        //       name: "lat",
        //       displayName: "纬度",
        //       configure: true,
        //       mapColumnName: "m_lng",
        //       columnType: "latitudeColumn",
        //     },
        //   ],
        //   mappingObj: {
        //     id: "m_id",
        //     name: "m_name",
        //     lng: "m_lat",
        //     lat: "m_lng",
        //   },
        //   coverCells: [],
        //   coverCellsObj: {},
        //   styleConfig: {
        //     iconType: "svg",
        //     svg: {
        //       type: "svg",
        //       iconType: "svg",
        //       svgId: "local",
        //       svgPath:
        //         '<svg t="1660786767685" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2390" width="200" height="200"><path d="M512 938.666667c-53.333333 0-384-257.258667-384-469.333334S299.925333 85.333333 512 85.333333s384 171.925333 384 384-330.666667 469.333333-384 469.333334z m0-352c64.8 0 117.333333-52.533333 117.333333-117.333334s-52.533333-117.333333-117.333333-117.333333-117.333333 52.533333-117.333333 117.333333 52.533333 117.333333 117.333333 117.333334z" p-id="2391"></path></svg>',
        //       scale: 30,
        //       fillOpacity: 1,
        //       strokeWeight: 0,
        //       strokeOpacity: 1,
        //       fillColor: "#12345e",
        //       strokeColor: "#000000",
        //       anchor: {
        //         width: 470,
        //         height: 944,
        //       },
        //     },
        //   },
        // };
        // const testPolylineLayerConfig = {
        //     "name": "测试地图视图",
        //     "displayName": "测试地图视图",
        //     "type": "polyline",
        //     "columns": {
        //         "columnConfig": []
        //     },
        //     "resourceId": "02441b7e-6985-47e3-8b3c-1eea6a19c6ca",
        //     "mappingConfig": [
        //         {
        //             "name": "id",
        //             "displayName": "主键",
        //             "mapColumnName": "m_id",
        //             "columnType": "idColumn",
        //             "configure": true
        //         },
        //         {
        //             "name": "name",
        //             "displayName": "名称",
        //             "mapColumnName": "m_name",
        //             "columnType": "nameColumn",
        //             "configure": true
        //         },
        //         {
        //             "name": "pointList",
        //             "displayName": "点集",
        //             "configure": true,
        //             "mapColumnName": "m_ps",
        //             "columnType": "pointListColumn"
        //         }
        //     ],
        //     "mappingObj": {
        //         "id": "m_id",
        //         "name": "m_name",
        //         "pointList": "m_ps"
        //     },
        //     "coverCells": [],
        //     "coverCellsObj": {},
        //     "styleConfigMapping": {
        //         "marker": {
        //             "iconType": "svg",
        //             "svg": {
        //                 "type": "svg",
        //                 "iconType": "svg",
        //                 "svgId": "local",
        //                 "svgPath": "<svg t=\"1660786767685\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2390\" width=\"200\" height=\"200\"><path d=\"M512 938.666667c-53.333333 0-384-257.258667-384-469.333334S299.925333 85.333333 512 85.333333s384 171.925333 384 384-330.666667 469.333333-384 469.333334z m0-352c64.8 0 117.333333-52.533333 117.333333-117.333334s-52.533333-117.333333-117.333333-117.333333-117.333333 52.533333-117.333333 117.333333 52.533333 117.333333 117.333333 117.333334z\" p-id=\"2391\"></path></svg>",
        //                 "scale": 30,
        //                 "fillOpacity": 1,
        //                 "strokeWeight": 0,
        //                 "strokeOpacity": 1,
        //                 "fillColor": "#12345e",
        //                 "strokeColor": "#000000",
        //                 "anchor": {
        //                     "width": 470,
        //                     "height": 944
        //                 }
        //             }
        //         },
        //         "polygon": {
        //             "strokeColor": "#096dd9",
        //             "fillColor": "#40a9ff",
        //             "fillOpacity": 0.6,
        //             "strokeWeight": 2,
        //             "strokeOpacity": 0.8,
        //             "strokeStyle": "solid"
        //         },
        //         "polyline": {
        //             "strokeColor": "#12345e",
        //             "strokeWeight": 2,
        //             "strokeOpacity": 1,
        //             "strokeStyle": "solid",
        //             "enableMassClear": true,
        //             "enableEditing": false,
        //             "enableClicking": true,
        //             "icons": []
        //         },
        //         "": {
        //             "strokeColor": "#096dd9",
        //             "fillColor": "#40a9ff",
        //             "fillOpacity": 0.6,
        //             "strokeWeight": 2,
        //             "strokeOpacity": 0.8,
        //             "strokeStyle": "solid"
        //         }
        //     },
        //     "styleConfig": {
        //         "strokeColor": "#096dd9",
        //         "fillColor": "#40a9ff",
        //         "fillOpacity": 0.6,
        //         "strokeWeight": 2,
        //         "strokeOpacity": 0.8,
        //         "strokeStyle": "solid"
        //     }
        // }

        // layer.value = new Layer(map, {
        //   isStatic: false,
        //   showEntityDetailCallBack: () => {},
        // });
        // await layer.value.init(testPolylineLayerConfig);
        // currentStep.value = 2;
      });
    }

    // 当前步骤组件的引用
    const currentCompRef = ref(null);
    // 当前步骤索引
    const currentStep = ref(0);
    // 弹窗显示状态
    const visible = ref(true);
    // 步骤配置
    const stepConfig = [
      {
        title: "选择数据",
        key: "select-data",
        comp: SelectData,
        nextFun: async () => {
          const detail = await currentCompRef.value.ok();
          return await getResourceInfo(detail.resourceId);
        },
      },
      {
        title: "绑定数据",
        key: "bind-data",
        comp: BindData,
        nextFun: async () => {
          const detail = await currentCompRef.value.ok();
          if (!detail) return false;

          layer.value = detail;
          return true;
        },
      },
      {
        title: "样式配置",
        key: "style-config",
        comp: StyleConfig,
        nextFun: async () => {
          const detail = await currentCompRef.value.ok();
          if (!detail) return false;

          layer.value = detail;

          !isEdit.value &&
            (await initLayerByResource(
              layer.value.getResourceInfo().resourceId,
              layer.value.layerConfig,
            ));
          return true;
        },
      },
    ];
    // 确定按钮文本
    const okText = computed(() => {
      return currentStep.value === stepConfig.length - 1 ? "完成" : "下一步";
    });

    // 下一步
    async function next() {
      const currStep = stepConfig[currentStep.value];
      const flag = await currStep.nextFun();

      if (currentStep.value === stepConfig.length - 1) {
        emit("ok", layer.value);
        return;
      }

      if (flag) {
        currentStep.value++;
      }
    }

    // 上一步
    function prev() {
      currentStep.value--;
    }

    // 取消
    function cancel() {
      emit("cancel");
    }

    // 步骤变化
    function onStepChange(current) {
      if (isEdit.value) {
        currentStep.value = current;
      }
    }

    // 步骤变化事件
    const stepOn = computed(() => {
      return isEdit.value ? { change: onStepChange } : {};
    });

    return {
      modalTitle,
      visible,

      next,
      prev,
      cancel,
      stepOn,
      okText,
      stepConfig,
      currentStep,

      layer,
      resourceInfo,
      currentCompRef,
    };
  },
});
</script>

<template>
  <DsModal :title="modalTitle" :visible="visible" :keyboard="false" @cancel="cancel">
    <div class="add-wrapper">
      <div class="add-steps-wrapper">
        <a-steps :current="currentStep" size="small" v-on="stepOn">
          <a-step v-for="item in stepConfig" :title="item.title" :key="item.key" />
        </a-steps>
      </div>
      <div class="add-content-wrapper">
        <component
          ref="currentCompRef"
          :is="stepConfig[currentStep].comp"
          :resourceInfo="resourceInfo"
          :layer="layer"
        />
      </div>
    </div>

    <template #footer>
      <a-button @click="cancel">取消</a-button>
      <a-button v-if="currentStep > 0" @click="prev">上一步</a-button>
      <a-button type="primary" @click="next">{{ okText }}</a-button>
    </template>
  </DsModal>
</template>

<style lang="less" scoped>
.add-wrapper {
  .add-steps-wrapper {
    padding-bottom: 24px;
  }
  .add-content-wrapper {
    height: calc(100vh - 520px);
  }
}
</style>
