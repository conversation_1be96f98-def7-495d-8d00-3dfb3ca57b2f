<script>
import { defineComponent, reactive, toRefs, watchEffect, ref } from "vue";
import SelectIcon from "@/components/select-icon/index.vue";

export default defineComponent({
  components: {
    SelectIcon,
  },
  props: {
    resourceInfo: {
      type: Object,
      default: undefined,
    },
    layer: {
      type: Object,
      default: undefined,
    },
  },
  setup(props) {
    const typeMap = {
      marker: ["id", "name", "lng", "lat", "time"],
      polyline: ["id", "name", "pointList", "time"],
      polygon: ["id", "name", "pointList", "centerPoint", "time"],
    };
    const formRef = ref(null);
    // 表单相关数据
    let tempColumns = null;
    // 表单相关数据
    const formData = reactive({
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      form: {
        layerName: undefined, // 图层名称
        layerType: "marker", // 图层类型：点/线/面
        layerIcon: undefined, // 图层icon

        idColumn: undefined, // 主键字段
        nameColumn: undefined, // 名称字段

        longitudeColumn: undefined, // 经度字段
        latitudeColumn: undefined, // 纬度字段

        pointListColumn: undefined, // 点集字段
        centerPointColumn: undefined, // 中心点字段

        timeColumn: undefined, // 时间字段
      },
      rules: {
        layerName: [{ required: true, message: "请输入图层名称" }],
        layerType: [{ required: true, message: "请选择图层类型" }],

        idColumn: [{ required: true, message: "请选择主键字段" }],
        nameColumn: [{ required: true, message: "请选择名称字段" }],
        longitudeColumn: [{ required: true, message: "请选择经度字段" }],
        latitudeColumn: [{ required: true, message: "请选择纬度字段" }],

        pointListColumn: [{ required: true, message: "请选择点集字段" }],
        centerPointColumn: [{ required: true, message: "请选择中心点字段" }],
      },
      layerTypes: [
        { label: "点", value: "marker" },
        { label: "线", value: "polyline" },
        { label: "面", value: "polygon" },
      ],
      columnOptions: [],
    });

    function autoSetField() {
      formData.form.layerName =
        props.layer.layerConfig?.displayName || props.resourceInfo.shortName;
      formData.form.layerType = props.layer?.layerConfig?.type || "marker";
      formData.form.layerIcon = props.layer?.layerConfig?.icon || undefined;
      formData.columnOptions = props.resourceInfo.columns.map((item) => {
        return {
          key: item.name,
          value: item.name,
          label: `${item.displayName} - ${item.name}`,
        };
      });
      props.layer.resourceColumns.forEach((col) => {
        if (col.columnType) {
          formData.form[col.columnType] = col.mapColumnName;
        }
      });

      // 只需要获取一次
      if (!tempColumns) {
        tempColumns = [...props.layer.resourceColumns];
      }
    }

    watchEffect(() => {
      if (!props.resourceInfo || !props.layer) return;

      autoSetField();
    });

    function columnChange(key, value) {
      const index = tempColumns.findIndex((i) => i.name === key);
      if (index !== -1) {
        tempColumns[index] = { ...tempColumns[index], ...value };
      }
    }

    function handleIconChange(value) {
      props.layer.layerConfig.icon = value;
    }

    async function ok() {
      const flag = await formRef.value.validate();
      if (!flag) return;

      props.layer.changeResourceColumn(
        tempColumns.filter((i) => typeMap[formData.form.layerType].includes(i.name)),
      );
      
      props.layer.layerConfig.displayName = formData.form.layerName;
      props.layer.layerConfig.type = formData.form.layerType;
      return props.layer;
    }

    return {
      formRef,
      columnChange,
      handleIconChange,
      
      ...toRefs(formData),
      ok,
    };
  },
});
</script>

<template>
  <div class="bind-data-wrapper">
    <a-form-model
      ref="formRef"
      :model="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :rules="rules"
    >
      <div class="bind-data-label">图层配置</div>
      <a-form-model-item label="图层名称" prop="layerName">
        <a-input v-model="form.layerName" placeholder="请输入图层名称" />
      </a-form-model-item>
      <a-form-model-item label="图层类型" prop="layerType">
        <a-radio-group v-model="form.layerType">
          <a-radio-button v-for="type in layerTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </a-radio-button>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="图层icon" prop="layerIcon">
        <SelectIcon v-model="form.layerIcon" @change="handleIconChange" />
      </a-form-model-item>

      <div class="bind-data-label">绑定字段</div>
      <a-form-model-item label="主键" prop="idColumn">
        <a-select
          v-model="form.idColumn"
          :options="columnOptions"
          placeholder="请选择主键字段"
          @change="(value) => columnChange('id', { mapColumnName: value, columnType: 'idColumn' })"
        />
      </a-form-model-item>
      <a-form-model-item label="名称字段" prop="nameColumn">
        <a-select
          v-model="form.nameColumn"
          :options="columnOptions"
          placeholder="请选择名称字段"
          @change="
            (value) => columnChange('name', { mapColumnName: value, columnType: 'nameColumn' })
          "
        />
      </a-form-model-item>

      <!-- 点 -->
      <template v-if="form.layerType === 'marker'">
        <a-form-model-item label="经度字段" prop="longitudeColumn">
          <a-select
            v-model="form.longitudeColumn"
            :options="columnOptions"
            placeholder="请选择经度字段"
            @change="
              (value) =>
                columnChange('lng', { mapColumnName: value, columnType: 'longitudeColumn' })
            "
          />
        </a-form-model-item>
        <a-form-model-item label="纬度字段" prop="latitudeColumn">
          <a-select
            v-model="form.latitudeColumn"
            :options="columnOptions"
            placeholder="请选择纬度字段"
            @change="
              (value) => columnChange('lat', { mapColumnName: value, columnType: 'latitudeColumn' })
            "
          />
        </a-form-model-item>
      </template>

      <!-- 线 -->
      <template v-if="form.layerType === 'polyline'">
        <a-form-model-item label="点集字段" prop="pointListColumn">
          <a-select
            v-model="form.pointListColumn"
            :options="columnOptions"
            placeholder="请选择点集字段"
            @change="
              (value) =>
                columnChange('pointList', { mapColumnName: value, columnType: 'pointListColumn' })
            "
          />
        </a-form-model-item>
      </template>

      <!-- 面 -->
      <template v-if="form.layerType === 'polygon'">
        <a-form-model-item label="点集字段" prop="pointListColumn">
          <a-select
            v-model="form.pointListColumn"
            :options="columnOptions"
            placeholder="请选择点集字段"
            @change="
              (value) =>
                columnChange('pointList', { mapColumnName: value, columnType: 'pointListColumn' })
            "
          />
        </a-form-model-item>
        <a-form-model-item label="中心点字段" prop="centerPointColumn">
          <a-select
            v-model="form.centerPointColumn"
            :options="columnOptions"
            placeholder="请选择中心点字段"
            @change="
              (value) =>
                columnChange('centerPoint', {
                  mapColumnName: value,
                  columnType: 'centerPointColumn',
                })
            "
          />
        </a-form-model-item>
      </template>

      <a-form-model-item label="时间字段" prop="timeColumn">
        <a-select
          v-model="form.timeColumn"
          :options="columnOptions"
          placeholder="请选择时间字段"
          @change="
            (value) => columnChange('time', { mapColumnName: value, columnType: 'timeColumn' })
          "
        />
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<style lang="less" scoped>
.bind-data {
  &-wrapper {
    width: 60%;
    margin: 0 auto;
  }
  &-label {
    position: relative;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    padding-left: 8px;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 2px;
      width: 4px;
      height: 60%;
      background-color: var(--ds-primary-color);
    }
  }
}
</style>
