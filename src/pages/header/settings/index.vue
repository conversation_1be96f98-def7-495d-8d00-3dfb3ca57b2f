<script>
import { defineComponent, ref } from "vue";
import Config from "./config/index.vue";
import Layers from "./layers/index.vue";
import { mapGetters } from "vuex";

export default defineComponent({
  components: { Config, Layers },
  computed: {
    ...mapGetters("setting", ["canEditPage"]),
  },
  watch: {
    canEditPage: {
      handler(can) {
        const routeMode = this.$route.params.dataViewType;
        this.activeKey = !can || routeMode === "usage" ? "data" : "config";
      },
      immediate: true,
    },
  },
  setup() {
    const visible = ref(true);
    const activeKey = ref("config");
    const close = () => {
      visible.value = false;
    };
    const getContainer = () => {
      return document.querySelector(".map-content");
    };
    const controlVisible = () => {
      visible.value = !visible.value;
    };
    return {
      visible,
      activeKey,
      close,
      controlVisible,
      getContainer,
    };
  },
});
</script>

<template>
  <div class="setting-wrapper">
    <a-icon v-if="canEditPage" type="setting" class="setting-icon" @click="controlVisible" />
    <a-drawer
      placement="right"
      wrapClassName="map-setting-drawer"
      :visible="visible"
      :closable="false"
      :mask="false"
      :getContainer="getContainer"
      :width="280"
      :wrap-style="{ position: 'absolute', overflow: 'visible' }"
      :body-style="{ padding: 0, height: 'calc(100% - 44px)', overflow: 'visible' }"
      :header-style="{ padding: 0, height: 0 }"
      @close="close"
    >
      <div class="setting-container">
        <div class="setting-container-control-wrapper" @click="controlVisible">
          <a-icon :type="visible ? 'double-right' : 'double-left'" style="font-size: 12px" />
        </div>

        <a-tabs v-if="canEditPage" default-active-key="config" v-model="activeKey">
          <a-tab-pane key="config" tab="地图配置" />
          <a-tab-pane key="data" tab="数据上图" force-render />
        </a-tabs>
        <div class="setting-content">
          <Config v-if="activeKey === 'config'" />
          <Layers v-if="activeKey === 'data'" />
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<style lang="less" scoped>
.setting {
  &-wrapper {
    height: 18px;
  }
  &-icon {
    font-size: 16px;
    cursor: pointer;
  }
  &-container {
    position: relative;
    height: 100%;
    width: 100%;

    &-control {
      &-wrapper {
        position: absolute;
        top: 170px;
        left: 0;
        transform: translateX(-100%);
        display: flex;
        align-items: center;
        justify-content: center;

        width: 16px;
        height: 45px;
        background: var(--ds-background-color-base);
        border-radius: 4px 0 0 4px;
        cursor: pointer;
        border: 1px solid var(--ds-border-color-base);
        box-shadow: -4px 0px 10px 2px var(--ds-shadow-color);
      }
    }

    :deep(.ant-tabs-nav-scroll) {
      display: flex;
      justify-content: center;
    }

    :deep(.ant-tabs-bar) {
      margin: 0;
    }

    :deep(.ant-collapse-borderless) {
      background-color: transparent;
    }
  }
  &-content {
    height: 100%;
    overflow-y: auto;
  }
}
</style>

<style lang="less">
.map-setting-drawer {
  .ant-drawer-content-wrapper {
    margin: 8px;
    height: calc(100% - 16px);
    border-radius: 4px;
    background-color: white;
  }
  .ant-drawer-body {
    height: 100% !important;
  }
  .ant-drawer-wrapper-body {
    overflow: visible !important;
  }
  .ant-drawer-content {
    overflow: visible !important;
    background-color: transparent !important;
  }
}
</style>
