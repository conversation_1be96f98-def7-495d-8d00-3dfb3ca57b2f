<script>
import { watchEffect, inject, defineComponent, ref, onUnmounted } from "vue";
import { throttle } from "lodash-es";
import store from "@/store";

export default defineComponent({
  components: {},
  props: {},
  setup() {
    const getMap = inject("getMap");
    const map = getMap();
    const form = ref({
      center: {
        lat: 0,
        lng: 0,
      },
      dragEnabled: true,
      zoom: 6,
    });
    watchEffect(() => {
      form.value = {
        ...map.settings,
      };
    });

    const openCenterModal = () => {
      map.openCenterModal();
      store.commit("setting/setIsChangeCenter", true);
    };

    const handleDragChange = () => {
      map.targetDragging();
    };

    const handleZoomChange = throttle((val) => {
      map.setZoom(val);
    }, 1000);

    onUnmounted(() => {
      handleZoomChange?.cancel?.();
    });

    return {
      form,
      handleDragChange,
      handleZoomChange,
      openCenterModal,
    };
  },
});
</script>

<template>
  <a-form-model :model="form" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
    <a-form-model-item label="地图中心">
      <a-button type="link" @click="openCenterModal" style="padding: 0">
        {{ form.center.lng }}, {{ form.center.lat }}
      </a-button>
    </a-form-model-item>
    <a-form-model-item label="默认缩放">
      <a-input-number v-model="form.zoom" :min="3" :max="9" @change="handleZoomChange" />
    </a-form-model-item>
    <a-form-model-item label="拖拽">
      <a-switch
        v-model="form.dragEnabled"
        checked-children="开"
        un-checked-children="关"
        @change="handleDragChange"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped></style>
