<template>
  <a-space :size="0">
    <template v-if="canEditPage">
      <DsWorkToolbarItem
        class="marker-tool-wrap"
        title="打点"
        :src="`${BASE_URL}static/icons/marker.svg`"
        :selected="isOpenDrawMarker"
        :disabled="isDisabledDrawMarker"
        @click="draw('marker')"
      />

      <DsWorkToolbarItem
        title="画图"
        tooltip="点击地图开始选点，双击结束画图"
        :src="`${BASE_URL}static/icons/polygon.svg`"
        :selected="isOpenDrawPolygon"
        :disabled="isDisabledPolygon"
        @click="draw('polygon')"
      />
      <DsWorkToolbarDivider class="interval-line" />
    </template>

    <div style="display: flex" v-show="!isStatic">
      <DsWorkToolbarItem
        title="刷新"
        icon="reload"
        :disabled="isDisabledRefresh"
        @click="handleRefresh"
      />
      <!-- <RelationModel v-if="isDomain" /> -->
    </div>

    <!-- 数据编辑模式 -->
    <div v-show="isEditing" class="button-wrap">
      <DsWorkToolbarDivider class="interval-line" />

      <DsWorkToolbarItem :style="{ maxWidth: 'none' }">
        <a-config-provider :autoInsertSpaceInButton="false">
          <a-button type="primary" ghost :loading="saveLoading" @click="saveEdit">
            保存修改
          </a-button>
        </a-config-provider>
      </DsWorkToolbarItem>

      <DsWorkToolbarItem :style="{ maxWidth: 'none' }">
        <a-config-provider :autoInsertSpaceInButton="false">
          <a-button :loading="saveLoading" @click="cancelEdit"> 取消编辑 </a-button>
        </a-config-provider>
      </DsWorkToolbarItem>
    </div>

    <!-- 修改地图中心点 -->
    <div v-show="isChangeCenter" class="button-wrap">
      <DsWorkToolbarDivider class="interval-line" />

      <DsWorkToolbarItem :style="{ maxWidth: 'none' }">
        <a-config-provider :autoInsertSpaceInButton="false">
          <a-button type="primary" ghost :loading="saveLoading" @click="saveChangeCenter">
            保存修改
          </a-button>
        </a-config-provider>
      </DsWorkToolbarItem>

      <DsWorkToolbarItem :style="{ maxWidth: 'none' }">
        <a-config-provider :autoInsertSpaceInButton="false">
          <a-button :loading="saveLoading" @click="cancelChangeCenter"> 取消修改 </a-button>
        </a-config-provider>
      </DsWorkToolbarItem>
    </div>

    <!-- 标记中的提示 -->
    <div class="button-wrap" v-show="isOpenDrawMarker || isOpenDrawPolygon">
      <DsWorkToolbarDivider class="interval-line" />
      <span style="margin-left: 8px">{{ drawingTypeText }}标记数据中</span>
      <DsWorkToolbarItem :style="{ maxWidth: 'none' }">
        <a-config-provider :autoInsertSpaceInButton="false">
          <a-button type="link" @click="close"> 结束{{ drawingTypeText }} </a-button>
        </a-config-provider>
      </DsWorkToolbarItem>
    </div>
  </a-space>
</template>

<script>
import { DsWorkToolbar } from "@daas/components";
import { mapActions, mapGetters } from "vuex";

const { BASE_URL } = process.env;

export default {
  inject: ["getMap", "getMapOptions", "emitCheckSaveResourceData"],
  components: {
    DsWorkToolbarItem: DsWorkToolbar.Item,
    DsWorkToolbarDivider: DsWorkToolbar.Divider,
  },
  data() {
    return {
      BASE_URL,
      saveLoading: false,
      isOpenDrawLine: false,
      drawingType: "",
    };
  },
  computed: {
    currentLayer() {
      return this.$store.getters["currentLayer"];
    },
    Map() {
      return this.getMap();
    },
    drawingTool() {
      return this.Map.drawingTool;
    },
    options() {
      return this.getMapOptions();
    },
    isOpenDrawMarker() {
      return this.$store.getters["setting/isOpenDrawMarker"];
    },
    isOpenDrawPolygon() {
      return this.$store.getters["setting/isOpenDrawPolygon"];
    },
    isDisabledDrawMarker() {
      if (this.currentLayer) {
        return this.currentLayer.type == "marker" ? false : true;
      }
      return true;
    },
    isDisabledPolygon() {
      if (this.currentLayer) {
        return this.currentLayer.type == "polygon" ? false : true;
      }
      return true;
    },
    isDisabledRefresh() {
      return this.currentLayer ? false : true;
    },
    isSaveableData() {
      const layerList = this.options.layerList;
      let hasStack = false;
      layerList.map((layer) => {
        if (layer.stack.addData.length) hasStack = true;
        if (layer.stack.deleteData.length) hasStack = true;
        if (layer.stack.updateData.length) hasStack = true;
      });

      return hasStack;
    },
    isRemoveLayerStatus() {
      return this.$store.state.isRemoveLayerStatus;
    },
    isSaveDisabled() {
      const val = !this.isSaveableData && !this.isRemoveLayerStatus;
      this.$emit("update:isSaveDisabled", val);
      return val;
    },
    drawingTypeText() {
      return this.drawingType === "marker" ? "打点" : "画图";
    },
    ...mapGetters("setting", [
      "isEditing",
      "editObject",
      "tagsInTheEdit",
      "isStatic",
      "canEditPage",
      "isDomain",
      "isChangeCenter",
    ]),
  },
  methods: {
    ...mapActions("setting", ["controlEdit"]),
    // handleSave() {
    //   this.saveLoading = true;
    //   this.$emit("update:saveLoading", true);
    //   this.emitCheckSaveResourceData().finally(() => {
    //     this.saveLoading = false;
    //     this.$emit("update:saveLoading", false);
    //     this.drawingTool._drawingManager._close();
    //     this.$store.commit("setting/modifyDrawActionStatus", {
    //       drawType: "marker",
    //       type: "isOpen",
    //       value: false,
    //     });
    //     this.$store.commit("setting/modifyDrawActionStatus", {
    //       drawType: "polygon",
    //       type: "isOpen",
    //       value: false,
    //     });
    //     // 存储到配置
    //     this.$store.commit("setting/setRemoveLayerStatus", false);

    //     const layerList = this.options.layerList;
    //     let elements = layerList.map((layer) => layer.resourceId);
    //     let pageConfig = { elements };

    //     // ！即将废弃
    //     // if (this.$setWorkplaceState) {
    //     //   this.$setWorkplaceState({ key: "setPageConfig", value: pageConfig });
    //     //   this.$message.success("已保存配置");
    //     // }

    //     // 新方法
    //     this.$dataView.updateDataViewConfig(pageConfig);
    //   });
    // },
    draw(drawingType) {
      this.drawingType = drawingType;

      this.drawingTool._drawingManager.setDrawingMode(drawingType);
      if (drawingType == "marker") {
        const openDrawMarker = !this.isOpenDrawMarker;
        if (openDrawMarker) {
          this.drawingTool._drawingManager._open();
        } else {
          this.drawingTool._drawingManager._close();
        }
        this.$store.commit("setting/setOpenDrawMarker", openDrawMarker);
      } else if (drawingType == "polygon") {
        const openDrawPolygon = !this.isOpenDrawPolygon;
        if (openDrawPolygon) {
          this.drawingTool._drawingManager._open();
        } else {
          this.drawingTool._drawingManager._close();
        }
        this.$store.commit("setting/setOpenDrawPolygon", openDrawPolygon);
      }
    },
    handleRefresh() {
      if (this.currentLayer) {
        this.currentLayer.removeLayer();
        this.currentLayer.loadData();
      }
    },
    // 取消编辑
    async cancelEdit() {
      if (this.tagsInTheEdit && this.tagsInTheEdit?.size > 0) {
        const promises = [];
        this.tagsInTheEdit.forEach((i) => promises.push(i._editCancel(this.currentLayer)));
        await Promise.all(promises);
        this.closeEditMod();
      }
    },
    // 保存配置
    async saveEdit() {
      try {
        this.saveLoading = true;
        if (this.tagsInTheEdit && this.tagsInTheEdit?.size > 0) {
          const promises = [];
          this.tagsInTheEdit.forEach((i) => promises.push(i._editConfirm(this.currentLayer)));
          await Promise.all(promises);
          this.closeEditMod();
        }
      } catch (error) {
        console.error("file: index.vue:231 -> saveEdit -> error:", error);
      } finally {
        this.saveLoading = false;
      }
    },
    // 关闭编辑模式
    closeEditMod() {
      this.currentLayer.exitEditMod();
      this.$store.dispatch("setting/clearEdittingTags");
      this.$nextTick(() => {
        this.controlEdit(false);
      });
    },
    // 关闭标记
    close() {
      if (this.drawingType) this.draw(this.drawingType);
    },
    // 保存修改地图中心点
    saveChangeCenter() {
      this.$store.commit("setting/setIsChangeCenter", false);
      this.Map.confirmChangeCenter();
    },
    // 取消修改地图中心点
    cancelChangeCenter() {
      this.$store.commit("setting/setIsChangeCenter", false);
      this.Map.confirmChangeCenter(true);
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
@import "~@styles/themes/default";

.sider-toggle-min-btn {
  padding: 4px;
  color: @text-color-secondary;
  cursor: pointer;
  &:hover {
    background-color: @border-color-split;
    border-radius: 2px;
  }
}

:deep(.ant-space-item) {
  & + .ant-space-item {
    margin-left: 4px;
  }

  .selected {
    background-color: #1890ff;
    .icon {
      background-color: #1890ff !important;
      img {
        transform: translate(-1000px);
        filter: drop-shadow(1000px 0 0 #ffffff);
      }
    }
    .text {
      color: #fff;
    }
  }
}

.interval-line {
  margin: 4px;
}

.button-wrap {
  display: flex;
  align-items: center;
}
</style>
