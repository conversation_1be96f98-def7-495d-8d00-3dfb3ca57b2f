<template>
  <div>
    <DsWorkToolbarItem
      :title="title"
      :tooltip="tooltip"
      icon="line-chart"
      @click="handleShowRelationDrawer"
    ></DsWorkToolbarItem>
    <Drawer
      :visible="visible"
      :type="showType"
      @close="visible = false"
      :relatedProps="relatedProps"
    />
  </div>
</template>

<script>
import { DsWorkToolbar } from "@daas/components";
import Drawer from "@daas/surface-web-lib/relation-model-drawer";
import { getAllLayerConfig } from "@/utils/map-utils";

export default {
  components: {
    DsWorkToolbarItem: DsWorkToolbar.Item,
    Drawer,
  },
  computed: {
    showType() {
      return this.$store.getters["setting/showType"];
    },
    isDomain() {
      return this.$store.getters["setting/isDomain"];
    },
    title() {
      return this.isDomain ? "关联模型" : "可视化";
    },
    tooltip() {
      return this.isDomain ? "关联模型" : "可视化分析";
    },
  },
  data() {
    return {
      visible: false,
      relatedProps: {
        relatedList: [],
      },
    };
  },
  methods: {
    handleShowRelationDrawer() {
      const config = getAllLayerConfig();
      const _isStatic = this.$store.getters["setting/isStatic"];

      let _relatedList = [];

      !_isStatic &&
        config?.elements?.forEach((i) => {
          _relatedList.push({
            objectId: i.resourceId,
            type: "resource",
          });
        });

      this.relatedProps.relatedList = _relatedList;
      this.$dataView.update({ relatedList: _relatedList });

      this.$nextTick(() => {
        if (this.isDomain) {
          this.visible = true;
        } else {
          const resource = this.$dataView.dataView.value?.relatedList;
          if (resource && resource.length) {
            let resourceId = resource[0].objectId;
            this.$newTabPage(`/daas/tool/spreadsheet?resourceId=${resourceId}`);
          }
        }
      });
    },
  },
};
</script>
