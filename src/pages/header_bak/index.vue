<template>
  <div>
    <SurfaceLayout
      ediableModelTitle
      showToolHeader
      :use-save="canEditPage"
      :fill="false"
      :top="false"
      :viewType="viewType"
      :useTypeSwitchConfirm="false"
      :siderDrawerProps="{
        mask: false,
      }"
      @changeViewType="changeViewType"
      @beforeSave="handleSavePageConfig"
      @changeMode="changeMode"
    >
      <template #toolHeaderLeft>
        <template v-if="canEditPage && !isDomain">
          <ExtraPanel ref="refExtra" />
          <DsWorkToolbarDivider style="margin: 4px"></DsWorkToolbarDivider>
        </template>
        <RightPanel
          ref="refRight"
          :saveLoading.sync="saveLoading"
          :isSaveDisabled.sync="isSaveDisabled"
        />
      </template>
      <template #extra>
        <MapSetting />
      </template>
    </SurfaceLayout>
    <!-- 静态数据处理 -->
    <SdtoddModal
      v-if="showStaticDataModal"
      :staticData="staticData"
      @ok="handleStaticData"
      @cancel="showStaticDataModal = false"
    />
  </div>
</template>

<script>
import { DsWorkToolbar } from "@daas/components";
import { Layout as SurfaceLayout } from "@daas/surface-web-lib/components";

import ExtraPanel from "./extra";
import RightPanel from "./right";
import { mapGetters } from "vuex";
import { getAllLayerConfig } from "@/utils/map-utils";

import SdtoddModal from "@daas/surface-web-lib/sdtodd-modal";
import { MarkerColumns, PolygonColumns } from "@/model/layer";
import { _TYPE_ENUMERATION } from "@/model/StaticData";
import MapSetting from "./map-setting";

export default {
  inject: ["getMap", "initLayerByResource", "initLayerByConfig"],
  name: "ToolHeader",
  components: {
    DsWorkToolbarDivider: DsWorkToolbar.Divider,
    ExtraPanel,
    RightPanel,
    SurfaceLayout,
    SdtoddModal,
    MapSetting,
  },
  data() {
    return {
      saveLoading: false,
      isSaveDisabled: false,
      imgDataUrl: "",
      showStaticDataModal: false,
      staticData: [],
    };
  },
  computed: {
    ...mapGetters("setting", ["isStatic", "isDomain"]),
    viewType() {
      return this.isStatic ? "static" : "dynamic";
    },
    canEditPage() {
      return this.$store.getters["setting/canEditPage"];
    },
  },
  props: {
    top: { type: Boolean, default: true },
  },
  methods: {
    handleSavePageConfig() {
      const map = this.getMap();
      const config = getAllLayerConfig();
      const _isStatic = this.$store.getters["setting/isStatic"];

      let _relatedList = [];

      !_isStatic &&
        config?.elements?.forEach((i) => {
          _relatedList.push({
            objectId: i.resourceId,
            type: "resource",
          });
        });

      config.settings = JSON.parse(JSON.stringify(map.settings));

      this.$dataView.updateDataViewConfig({ mapConfig: config, isStaticData: _isStatic });
      this.$dataView.update({ relatedList: _relatedList, type: "map" });
    },
    headerInsert() {
      this.$refs.refRight.headerInsert();
    },
    changeViewType(val) {
      if (this.isStatic !== val) {
        return;
      }

      const tipName = val ? "动态" : "静态";
      const tip = val ? "当前没有数据的图层将不会进行转换" : "";

      this.$confirm({
        title: `该操作会将视图修改为${tipName}视图，是否继续操作?`,
        content: tip,
        onOk: () => {
          return new Promise((resolve) => {
            val ? this.handleStaticToDynamic() : this.handleDynamicToStatic();
            this.$store.commit("setting/setShowConifgModal", false);
            resolve();
          });
        },
        onCancel: () => {},
      });
    },
    // 1. 动 --> 静， 将数据进行转化
    async handleDynamicToStatic() {
      // 1. 切换状态
      this.$store.commit("setting/SET_IS_STATIC", true);

      // 2. 获取所有图层及数据
      const _layers = this.$store.getters["layers"];
      const flag = _layers.find((i) => !i.type);
      if (flag) {
        this.$message.warning("您还有图层没有映射字段，无法切换静态视图");
        this.$store.commit("setting/SET_IS_STATIC", false);
        return;
      }

      // 3. 清空现有图层
      await this.$store.dispatch("resetData");

      // 4. 重新渲染
      _layers.forEach((layer) => {
        const { layerConfig } = layer;
        const { type, displayName } = layerConfig;
        const mappingConfig = _TYPE_ENUMERATION[type].columns;

        const _data = layer.getAllCover();

        this.initLayerByConfig({
          type,
          name: displayName,
          mappingConfig,
          localData: _data.map((i) => i.data),
        });
      });
    },
    // 2. 静 --> 动； 清空原本数据
    handleStaticToDynamic() {
      // 1. 收集数据
      const _layers = this.$store.getters["layers"];
      this.staticData = _layers
        .map((i) => {
          const body = i.staticStore.getAllLocalData();
          const { columns, displayName: name } = i.staticStore.mockData;
          const headers = columns.map((i) => i.mapColumnName);
          const key = `${i.staticStore.type}_${i.id}`;
          return {
            key,
            name,
            headers,
            body,
          };
        })
        .filter((i) => i.body?.length);

      if (this.staticData.length > 0) {
        this.$nextTick(() => (this.showStaticDataModal = true));
      } else {
        this.handleStaticData([]);
      }
    },
    // 静态转动态组件回调
    async handleStaticData(res) {
      // 更新配置为动态数据
      await this.$store.dispatch("resetData");
      await this.$store.commit("setting/SET_IS_STATIC", false);

      // 处理数据
      const _configs = res.map((i) => {
        const { resourceName, resourceId, specifiedMap, key } = i;

        const type = key.split("_")[0];
        let columnsConfig = type === "marker" ? MarkerColumns : PolygonColumns;

        let mappingConfig = [];
        for (let key in specifiedMap) {
          const name = specifiedMap[key];
          const mapColumnName = key;

          let _tempColumn = columnsConfig.find((cItem) => cItem.name === name);
          if (_tempColumn) {
            _tempColumn.mapColumnName = mapColumnName;
          } else {
            continue;
          }

          mappingConfig.push(_tempColumn);
        }

        return {
          mappingConfig,
          displayName: resourceName,
          name: resourceName,
          resourceId,
          type,
        };
      });

      _configs.forEach((i) => {
        this.initLayerByConfig(i);
      });

      // 关闭弹窗
      this.showStaticDataModal = false;
      this.$message.success("切换成功");
    },
    // 切换查看模式和编辑模式
    changeMode(mode) {
      this.$store.dispatch("setting/changePageMode", { mode, Map: this.getMap() });
    },
  },
};
</script>
