<template>
  <a-modal width="800px" title="编辑数据" visible @cancel="cancel" @ok="handleUpdate">
    <div style="max-height: 50vh; overflow-y: auto">
      <a-form-model :model="form" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
        <a-form-model-item v-for="i in resourceColumns" :key="i.name" :label="i.displayName">
          <a-input
            placeholder="请输入"
            :disabled="i.mapColumnName === 'id'"
            v-model="form[i.mapColumnName]"
          />
        </a-form-model-item>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
const _FILTER_TYPE = {
  marker: ["id", "name", "lng", "lat"],
  polygon: ["id", "name", "pointList", "centerPoint"],
};

export default {
  name: "StaticUpdateData",
  data() {
    return {
      form: {},
      resourceColumns: [],
    };
  },
  computed: {
    currentLayer() {
      return this.$store?.getters["currentLayer"] || {};
    },
    showForm() {
      this.initForm();
      return this.resourceColumns;
    },
  },
  mounted() {
    const editData = this.$store.getters["setting/updateModalData"] || {};
    const exData = editData?.extData || {};
    this.tempId = editData?.id;

    this.init(exData);
  },
  methods: {
    init(exData) {
      const { resourceColumns = [], type = "marker" } = this.currentLayer;
      const filterArr = _FILTER_TYPE[type];
      let tempForm = {};
      this.resourceColumns = resourceColumns.filter((i) => {
        const flag = filterArr.includes(i.name);
        if (flag) tempForm[i.mapColumnName] = exData[i.mapColumnName] || "";
        return flag;
      });
      this.initForm(tempForm);
    },
    initForm(data) {
      this.form = data;
    },
    handleUpdate() {
      this.currentLayer.updateData(this.form);

      this.cancel();
    },
    cancel() {
      this.$store.commit("setting/setUpdateModalOpen", false);
      this.$store.commit("setting/setUpdateModalData", {});
    },
  },
};
</script>
