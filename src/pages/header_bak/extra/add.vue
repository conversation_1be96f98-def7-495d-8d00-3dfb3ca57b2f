<template>
  <div>
    <a-dropdown placement="bottomLeft" v-model="showPopover" :trigger="['click']">
      <DsWorkToolbarItem
        title="新增"
        tooltip="新增图层"
        icon="plus"
        @click.native.stop="handleCreateLayer"
      />
      <template v-if="isStatic" #overlay>
        <a-menu slot="overlay">
          <a-menu-item @click="createStatciLayer('marker')">
            <div class="layer-type-wrap">
              <img src="~@/assets/imgs/marker.png" class="type-wrap" />
              <div class="type-text">点标记图层</div>
            </div>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item @click="createStatciLayer('polygon')">
            <div class="layer-type-wrap">
              <img src="~@/assets/imgs/polygon.png" class="type-wrap" />
              <div class="type-text">图形标记图层</div>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script>
import StaticData from "@/model/StaticData";
import { DsWorkToolbar } from "@daas/components";

export default {
  inject: ["initLayerByConfig"],
  components: {
    DsWorkToolbarItem: DsWorkToolbar.Item,
  },
  data() {
    return {
      showPopover: false,
    };
  },
  computed: {
    isStatic() {
      return this.$store.getters["setting/isStatic"];
    },
  },
  methods: {
    handleCreateLayer() {
      this.isStatic ? this.handleStaticLayer() : this.showResourceSelect();
    },
    handleStaticLayer() {
      this.showPopover = true;
    },
    showResourceSelect() {
      this.$store.commit("setting/setShowResourceSelect", true);
    },
    createStatciLayer(type) {
      const _staticData = new StaticData({ type });
      this.initLayerByConfig(_staticData.mockData);
      this.showPopover = false;
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.ant-dropdown-menu-item) {
  .layer-type-wrap {
    display: flex;
    align-items: center;
    .type-wrap {
      width: 32px;
      height: auto;
      margin-right: 8px;
    }
  }
}
</style>
