<template>
  <a-space :size="0">
    <Add v-bind="$attrs" v-on="$listeners" />
    <MappingField v-show="!isStatic" v-bind="$attrs" v-on="$listeners" />
  </a-space>
</template>
<script>
import Add from "./add";
import MappingField from "./mapping-field";
export default {
  components: {
    Add,
    MappingField,
  },
  computed: {
    isStatic() {
      return this.$store.getters["setting/isStatic"];
    },
  },
};
</script>
