<template>
  <DsFormModelModal
    v-if="show"
    visible
    ref="refFormModal"
    :title="title"
    :mode="pageMode"
    :resourceId="resourceInfo.resourceId"
    :rowKeyId="resourceItem[resourcePrimaryId]"
    :extendAuthParams="extendAuthParams"
    :formProps="{
      labelCol: { span: 4 },
      wrapperCol: { span: 17 },
    }"
    @cancel="$emit('cancel')"
    @ok="handleOk"
  >
  </DsFormModelModal>
</template>

<script>
import { DsFormModel } from "@daas/components";
const DsFormModelModal = DsFormModel.Modal;

export default {
  props: {
    show: { type: Boolean, default: false },
  },
  // inject: ["getMapOptions"],
  components: { DsFormModelModal },
  computed: {
    title() {
      return this.pageMode === "view" ? "查看数据" : "";
    },
    modelData() {
      return this.$store.getters["setting/updateModalData"];
    },
    resourceItem() {
      return this.modelData.extData;
    },
    currentLayer() {
      return this.modelData.layer;
    },
    resourceInfo() {
      return this.currentLayer && this.currentLayer.resourceStore.getters["resourceInfo"];
    },
    resourcePrimaryId() {
      return this.currentLayer ? this.currentLayer.resourceStore.getters["resourcePrimaryId"] : "";
    },
    extendAuthParams() {
      const modularType = this.$store.getters["modularType"];
      const modularId = this.$store.getters["modularId"];
      if (modularId && modularId != "") {
        return {
          modularType: modularType,
          modularId: modularId,
        };
      }
      return {};
    },
    pageMode() {
      return this.$store.getters["setting/pageMode"];
    },
  },
  data() {
    return {
      // sett,
      relationMap: { names: {} },
      columnAllConfig: {},
    };
  },
  methods: {
    // 目前一改就提交接口
    handleOk() {
      const formModel = this.$refs.refFormModal.$refs.refFormModel.formModel;
      this.currentLayer.updateData(formModel, true);
      this.$emit("cancel");
    },
  },
};
</script>
