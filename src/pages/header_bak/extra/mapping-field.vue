<template>
  <div>
    <DsWorkToolbarItem
      title="映射"
      tooltip="逻辑映射"
      :src="`${BASE_URL}static/icons/mapping.svg`"
      :disabled="isDisabledMappingField"
      @click="openLayerMapping"
    ></DsWorkToolbarItem>
  </div>
</template>

<script>
import { DsWorkToolbar } from "@daas/components";
const { BASE_URL } = process.env;

export default {
  components: {
    DsWorkToolbarItem: DsWorkToolbar.Item,
  },
  data() {
    return {
      BASE_URL,
    };
  },
  computed: {
    isDisabledMappingField() {
      return this.$store.getters["layers"].length > 0 ? false : true;
    },
  },
  methods: {
    openLayerMapping() {
      this.$store.commit("setting/setShowLayerMapping", true);
      this.$store.commit("setting/setShowConifgModal", false);
    },
  },
};
</script>
