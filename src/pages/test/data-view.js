import {
  getOptionsByMicro,
  getOptionsByRoute,
  loadAppData,
  loadTplData,
  marginDataViewOptions,
} from "./utils";
import { Logger } from "@daas/core";

const { ref, computed } = require("vue");

// dataView 默认配置
const _DEFAULT_DATA_VIEW = {
  appId: "",
  appName: "未命名视图",

  pageConfig: {
    isStaticData: true,
    staticData: null,
  },

  type: "spreadsheet",
  relatedList: [],

  resourceId: "",
  mode: "edit", // "view" | "edit" | ""，默认空即配置模式
  permissions: [], // 当前视图拥有的权限
  top: true, // 默认 true

  // 以下参数只针对有appId情况下
  modularType: "",
  modularId: "",
};

const startLoaded = ref(false);
// 视图
const dataView = ref({});

const loading = ref(false);
const error = ref({
  initDataView: null,
  initOptions: null,
  bootByOptions: null,
  update: null,
  start: null,
});

/**
 * 初始化 dataView 相关配置
 * @param {object} options 配置
 * @returns void
 */
const initDataView = (options) => {
  Logger.debug(`useDataView/initDataView`, { options });
  try {
    loading.value = true;
    return new Promise((resolve) => {
      dataView.value = marginDataViewOptions(_DEFAULT_DATA_VIEW, options);
      resolve();
    });
    error.value.initDataView = null;
  } catch (err) {
    error.value.initDataView = err;
    Logger.error("useDataView/initDataView", err);
  } finally {
    loading.value = false;
  }
};

/**
 * 通过微前端或路由初始化配置数据
 * @param {VueRoute} route 路由
 */
const initOptions = async (route) => {
  try {
    loading.value = true;
    // 1. 获取微前端的数据
    let options = await getOptionsByMicro();
    // 2. 不在微前端中就直接使用路由参数
    if (options.unInMicro) {
      options = await getOptionsByRoute(route, options);
    }
    // 3. 更新 dataView
    update(options);
    // 开发模式下输出配置数据
    Logger.debug(`useDataView/initOptions`, { options });
    error.value.initOptions = null;
  } catch (err) {
    error.value.initOptions = err;
    Logger.error(`useDataView/initOptions`, err);
  } finally {
    loading.value = false;
  }
};

/**
 * 通过获取最终数据进行启动
 */
const bootByOptions = async () => {
  try {
    loading.value = true;
    const { appId } = dataView.value;
    if (appId) {
      const res = await loadAppData(appId);
      update(res);

      const curDataViewConfig = dataView.value.pageConfig;
      if (curDataViewConfig && JSON.stringify(curDataViewConfig) == "{}") {
        // 没有视图配置且有 modelTypeId 情况下取视图模板配置
        if (dataView.value.modelTypeId) {
          const tpl = await loadTplData(dataView.value.modelTypeId);
          update({ pageTplConfig: tpl, relations: tpl.relations });
        }
      }
    }
    Logger.debug(`useDataView/bootByOptions`, { dataView: dataView.value });
    error.value.bootByOptions = null;
  } catch (err) {
    error.value.bootByOptions = err;
    Logger.error(`useDataView/bootByOptions`, err);
  } finally {
    loading.value = false;
  }
};

/**
 * 更新 dataView
 * @param {object} opts 配置
 * @param {boolean} coverage 是否全覆盖
 */
const update = (opts, coverage) => {
  try {
    loading.value = true;
    dataView.value = marginDataViewOptions(dataView.value, opts, coverage);
    Logger.debug(`useDataView/update`, { dataView: dataView.value });
    error.value.update = null;
  } catch (err) {
    error.value.update = err;
    Logger.error(`useDataView/update`, err);
  } finally {
    loading.value = false;
  }
};

// 使用
export const useDataView = () => {
  const start = async (route, options = {}) => {
    try {
      // 1. 初始化变量等配置
      await initDataView(options);
      // 2. 获取路由或微前端配置数据并更新配置
      await initOptions(route);
      // 3. 根据配置处理数据
      await bootByOptions();
      error.value.start = null;
    } catch (err) {
      error.value.start = err;
      Logger.error(`useDataView/start`, err);
    } finally {
      loading.value = false;
    }
  };

  return {
    start,
    dataView: computed(() => dataView.value),
  };
};
