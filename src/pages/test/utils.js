import { computed, watch } from "vue";
import { request } from "@daas/core";

/**
 * 合并配置
 * @param {object} oldOpts 旧配置
 * @param {object} newOpts 新配置
 * @param {boolea} coverage 是否全覆盖
 * @returns
 */
export const marginDataViewOptions = (oldOpts, newOpts, coverage = false) => {
  // FIX 兼容旧版的配置方法
  const { options } = newOpts?.pageConfig || {};
  if (options) {
    newOpts = {
      ...newOpts,
      ...options,
    };
    delete newOpts.pageConfig.options;
  }

  return coverage ? newOpts : { ...oldOpts, ...newOpts };
};

/**
 * 通过路由获取当前 dataView 模式
 * 传入的 route 如果是 vue Route 实例对象的话，只获取 path 来匹配，没有处理 query
 * @param {string | VueRotuer} route 判断当前dataView模式的路由，可以是string也可以是vue Rotue 对象
 * @param {string | RegExp} route 默认 /view/
 * @returns {String} dataView 模式
 */
export const getDataViewTypeByRoute = (route, reg = /view/) => {
  let _path = route?.path || route;

  const modeReg = new RegExp(reg);
  return modeReg.test(_path) ? "view" : "edit";
};

/**
 * 后端接收保存的 pageConfig 为字符串，转换函数
 * @param {string | object} config 需要解析的配置文件
 * @param {boolean} [toStr] 默认： true; false：解析为对象 ; true：格式化为字符串
 * @returns {string | object} 根据 toStr 返回对应的配置
 */
const toStrDataViewConfig = (config, toStr = true) => {
  let dataViewConfig = {};

  try {
    if (config) {
      if (toStr && typeof config === "object") {
        dataViewConfig = JSON.stringify(config);
      }
      if (!toStr && typeof config === "string") {
        dataViewConfig = JSON.parse(config);

        // FIX: 历史遗留
        if (dataViewConfig.pageConfig) {
          dataViewConfig = dataViewConfig.pageConfig;
        }

        if (dataViewConfig.options) {
          delete dataViewConfig.options;
        }
      }
    }
  } catch (error) {
    // do nothing
  }
  return dataViewConfig;
};

// 获取微前端传入的配置
export const getOptionsByMicro = () => {
  return new Promise((resolve) => {
    if (window.__MICRO_APP_ENVIRONMENT__) {
      const dataListener = (params) => {
        if (params && params.key === "workplaceState") {
          const data = params.value || {};
          const resourceId = data.resourceId;
          const appId = data.pageId || data.appId; // FIX: 兼容 pageId，后面可去除
          const modularId = data.modularId;

          data?.options && delete data.options; // FIX: 兼容模式

          // 传入的mode优先
          const mode = getDataViewTypeByRoute(window.__MICRO_APP_BASE_ROUTE__ || "");
          data.mode = data.mode || mode;

          if (resourceId) {
            resolve({
              resourceId,
              microAppData: data,
            });
          } else if (appId) {
            resolve({ appId, microAppData: data });
          } else if (modularId) {
            resolve({
              modularType: data.modularType,
              modularId,
              microAppData: data,
            });
          } else {
            resolve({ microAppData: data });
          }
        }
      };
      // 添加监听
      window.microApp.addDataListener(dataListener, true);
    } else {
      resolve({ unInMicro: true });
    }
  });
};

// 获取url中的参数
export const getOptionsByRoute = (route, options = {}) => {
  return new Promise((resolve, reject) => {
    if (!route) {
      throw new Error("缺少参数 route");
    }

    const watchRouteQuery = (query) => {
      const mode = getDataViewTypeByRoute(route);

      let res = {};

      if (query.appId) {
        res = { appId: query.appId };
      } else if (query.resourceId) {
        const type = query.type;
        res = { resourceId: query.resourceId, ...(type && { type }) };
      }

      resolve({ microAppData: { mode }, ...options, ...res });
    };

    const queryRoute = computed(() => route.query);
    watch(queryRoute, watchRouteQuery, { immediate: true });
  });
};

/**
 * 获取应用数据
 * @param {number | string} appId 应用 Id
 * @returns
 */
export const loadAppData = async (appId) => {
  const results = await request.get(`/insightApp/domain/${appId}`);
  // 获取当前视图的权限
  let permissions =
    results?.behaviors
      ?.filter((i) => ["edit", "view", "detail"].includes(i.code))
      ?.map((i) => i.code) || [];

  return {
    permissions,
    appId: results.appId,
    appName: results.appName,
    type: results.type,
    modelTypeId: results.modelTypeId,
    relatedList: results.relatedList,
    pageConfig: toStrDataViewConfig(results.appPageEntity.pageConfig, false),
    rawAppData: results,
  };
};

/**
 * 获取模板数据
 * @param {number | string} modelTypeId 模块 ID
 * @returns
 */
export const loadTplData = (modelTypeId) =>
  request.post("/api/daasModelDesigner/GetModelTypeDesigner", {
    modelTypeId,
  });
