<template>
  <pre>{{ code }}</pre>
</template>

<script>
import { MarkerColumns, PolygonColumns } from "@/model/layer";

export default {
  data() {
    return {
      code: {},
    };
  },
  async mounted() {
    const temp = [
      {
        headers: ["id", "mingchen", null, null, null],
        body: [
          {
            name: "未命名",
            centerPoint: '{"lng":110.86682566666666,"lat":25.352293666666668}',
            markerList:
              "[[109.174274,26.516397],[108.870719,24.975173],[109.671001,23.947923],[111.933869,23.905643],[113.092899,25.460537],[112.458192,27.308089]]",
            id: "51c821ff-d9ef-4317-8acb-26f77fcadcfa",
          },
        ],
        key: "polygon_sdeidlwgga",
        name: "静态图层-面",
        rowCount: 1,
        resourceId: "42982829-05e0-41fb-9671-f07f45f44501",
        resourceName: "地图点类型测试-02",
        specifiedMap: {
          id: "id",
          mingchen: "name",
          dianji: "markerList",
          zhongxindian: "centerPoint",
        },
      },
      {
        headers: ["id", "mingchen", null, null],
        body: [
          {
            name: "未命名",
            lng: 110.250516,
            lat: 26.25126,
            id: "7807b330-ee73-4ee7-8a85-bb7ea8059a34",
          },
          {
            name: "未命名",
            lng: 111.345155,
            lat: 26.267849,
            id: "570b5259-21a2-4416-b91a-48a61a7f129a",
          },
          {
            name: "未命名",
            lng: 111.501533,
            lat: 25.293392,
            id: "04814cc1-f533-437b-80fe-24b006be92d2",
          },
          {
            name: "未命名",
            lng: 110.49888,
            lat: 24.681339,
            id: "da9cfabd-d3c7-4bf8-8a38-41c5b4a63068",
          },
          {
            name: "未命名",
            lng: 109.910166,
            lat: 25.285028,
            id: "3f92e770-0f18-4b3b-8b38-b1d3ab42c2be",
          },
        ],
        key: "marker_gehmz7tlhw",
        name: "静态图层-点",
        rowCount: 5,
        resourceId: "42982829-05e0-41fb-9671-f07f45f44501",
        resourceName: "地图点类型测试-02",
        specifiedMap: {
          id: "id",
          mingchen: "name",
          jingdu: "lng",
          weidu: "lat",
        },
      },
    ];

    this.handleStaticData(temp);
  },
  methods: {
    handleStaticData(data) {

      const _configs = data.map((i) => {
        const { resourceName, resourceId, specifiedMap, key } = i;

        const type = key.split("_")[0];
        let columnsConfig = type === "marker" ? MarkerColumns : PolygonColumns;

        let mappingConfig = [];
        for (let key in specifiedMap) {
          const name = specifiedMap[key];
          const mapColumnName = key;

          let _tempColumn = columnsConfig.find((cItem) => cItem.name === name);
          if (_tempColumn) {
            _tempColumn.mapColumnName = mapColumnName;
          } else {
            continue;
          }

          mappingConfig.push(_tempColumn);
        }

        return {
          mappingConfig,
          displayName: resourceName,
          name: resourceName,
          resourceId,
          type,
        };
      });
      console.log('%c [ _configs ]-134-「index.vue」', 'font-size:13px; background:pink; color:#bf2c9f;', _configs)
    },
  },
};
</script>
