<template>
  <a-spin v-bind="_loading" class="ds-spin-loading">
    <MapHeader :top="top" ref="mapHeader" />
    <slot name="error" v-if="loadFailMsg" />
    <div class="map-content" v-show="!loadFailMsg">
      <div class="map-content-inner">
        <slot name="map" />
      </div>
    </div>
    <slot name="action" />
  </a-spin>
</template>

<script>
import MapHeader from "./header/index.vue";
import { mapGetters } from "vuex";
export default {
  data() {
    return {};
  },
  props: {
    loading: { type: [Boolean, String], default: false },
    top: { type: Boolean, default: true },
  },
  components: {
    MapHeader,
  },
  computed: {
    _loading() {
      const l = this.loading;
      if (typeof l === "string") {
        return { spinning: true, tip: l };
      } else {
        return { spinning: l, tip: "加载中..." };
      }
    },
    isStatic() {
      return this.$store.getters["setting/isStatic"];
    },
    layerList() {
      const layers = this.$store.getters["layers"];
      return layers;
    },
    ...mapGetters("setting", ["loadFailMsg"]),
  },
  watch: {},
  mounted() {},
  methods: {
    toLocation(point) {
      //调用点位居中方法
      this.$emit("centerMap", point);
    },
    clickViewList(sameLayer) {
      //列表隐藏
      this.$refs.child.mapToViewList(sameLayer);
    },
    async headerInsert(drawData) {
      await this.$refs.child.headerInsert(drawData);
    },
  },
};
</script>

<style lang="less" scoped>
.ds-spin-loading {
  width: 100%;
  height: 100%;
  :deep(.ant-spin-container) {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .map-content {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    .map-content-inner {
      position: absolute;
      inset: 0;
    }
  }
}
</style>
