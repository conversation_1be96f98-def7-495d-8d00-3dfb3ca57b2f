import "./utils/map-load";
import { renderApp } from "@daas/components";
import routes from "./router";
import Vue from "vue";
import store from "./store";
import "@styles/global.less";

import microAppPlugin from "@daas/surface-web-lib/plugin/micro-app";
import { useDataView } from "@daas/surface-web-lib/plugin/domain";
import EntityDetailModal from "@daas/surface-web-lib/entity-detail-modal/index.js";
import { DsDataQuery } from "@daas/components";

Vue.use(EntityDetailModal);
Vue.prototype.$dataView = useDataView();

Vue.use(DsDataQuery);

Vue.use(microAppPlugin, {
  store,
  module: "workplace",
  unmount: () => {},
});

renderApp({ store, routerConfig: { routes } });
