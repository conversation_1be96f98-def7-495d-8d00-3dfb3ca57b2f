@font-face {
  font-family: "iconfont"; /* Project id  */
  src: url("./iconfont.ttf") format("truetype");
}

.BMap_bottom {
  display: none;
}
.BMap_pop {
  > div,
  > img:nth-child(10) {
    display: none;
    overflow: unset;
  }
  > div:nth-child(9) {
    display: block;
    overflow: unset !important;
  }
  .BMap_top {
    display: none;
  }
  .BMap_center {
    position: sticky !important;
    height: 100%;
    background: transparent;
    border: none;
  }
}
.BMap_shadow {
  display: none;
}
// 信息窗
.BMap_bubble_content {
  .ant-notification {
    position: absolute;
    bottom: 0;
    left: 0;
    margin: 0;
    border-radius: 5px;

    .noti-arr {
      position: absolute;
      bottom: 16px;
      left: 0;
      width: 51px;
      height: 31px;
      transform: translate(-75%, 0) rotate(90deg) rotateY(180deg);
    }

    .ant-notification-notice {
      margin: 0;
    }
  }

  .ant-btn {
    & + .ant-btn {
      margin-left: 8px;
    }
  }

  .info-window {
    width: 100%;
    max-height: 280px;
    padding-right: 8px;
    overflow: auto;
    .el-divider {
      background: #ccccccbf;
    }
    .address {
      color: #fff;
    }
  }
  //自定义滚动条样式
  .info-window::-webkit-scrollbar {
    width: 6px;
    height: 1px;
  }
  .info-window::-webkit-scrollbar-thumb {
    background: #535353;
    border-radius: 6px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  }
  .info-window::-webkit-scrollbar-track {
    background: #ededed;
    border-radius: 6px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  }
}

// 菜单
.BMap_contextMenu {
  font-size: 14px;
  font-family: "iconfont" !important;
  font-style: normal;
  div {
    & ::before {
      margin-right: 4px;
    }

    width: auto !important;
    padding: 8px 12px !important;
    border-bottom: 1px solid #eee;
  }

  #menu-detail {
    & ::before {
      content: "\e793";
    }
  }

  #menu-move {
    & ::before {
      content: "\e67b";
    }
  }
  #menu-edit {
    & ::before {
      content: "\e66f";
    }
  }
  #menu-del {
    & ::before {
      content: "\e67e";
    }
  }
}

.BMap_cpyCtrl,[title='到百度地图查看此区域'] {
  display: none;
}
