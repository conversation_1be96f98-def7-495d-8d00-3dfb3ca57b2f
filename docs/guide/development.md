# 开发指南

## 项目概述

本项目是一个基于 Vue.js 和百度地图 API 的地图组件库，采用模块化架构设计，提供了完整的图层管理、数据处理、样式配置和性能优化功能。

## 架构设计

### 整体架构

```
src/
├── model/                  # 核心模型层
│   ├── layer/             # 图层相关模块
│   │   ├── index.js       # 主 Layer 类
│   │   ├── LayerDataManager.js    # 数据管理
│   │   ├── LayerStyleManager.js   # 样式管理
│   │   ├── LayerEventManager.js   # 事件管理
│   │   ├── LayerRenderer.js       # 渲染逻辑
│   │   └── types.js       # 类型定义
│   ├── datasource/        # 数据源模块
│   │   ├── DataSourceInterface.js # 数据源基类
│   │   ├── StaticDataSource.js    # 静态数据源
│   │   ├── DynamicDataSource.js   # 动态数据源
│   │   └── DataSourceFactory.js   # 数据源工厂
│   └── ...                # 其他模型
├── utils/                 # 工具模块
│   ├── ErrorCenter.js     # 错误处理中心
│   ├── ResourceManager.js # 资源管理器
│   ├── PerformanceOptimizer.js    # 性能优化工具
│   ├── MapPerformanceOptimizer.js # 地图性能优化
│   └── LazyLoader.js      # 懒加载工具
├── mixins/                # Mixins 模块
│   ├── common/            # 通用 Mixins
│   ├── performance/       # 性能相关 Mixins
│   └── index.js           # 统一导出
├── components/            # Vue 组件
└── tests/                 # 测试文件
    ├── unit/              # 单元测试
    ├── integration/       # 集成测试
    └── setup.js           # 测试环境配置
```

### 设计原则

1. **单一职责原则**: 每个类和模块都有明确的职责
2. **开闭原则**: 对扩展开放，对修改封闭
3. **依赖倒置原则**: 依赖抽象而不是具体实现
4. **组合优于继承**: 通过组合实现功能复用
5. **关注点分离**: 数据、样式、事件、渲染分离管理

## 开发环境搭建

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0 或 yarn >= 1.22.0
- Vue.js 2.6+

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发命令

```bash
# 启动开发服务器
npm run serve

# 构建生产版本
npm run build

# 运行测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行 ESLint 检查
npm run lint

# 自动修复 ESLint 问题
npm run lint:fix
```

## 开发规范

### 代码规范

#### 文件命名
- 组件文件使用 PascalCase: `MyComponent.vue`
- 工具类文件使用 PascalCase: `ErrorCenter.js`
- Mixin 文件使用 camelCase: `formMixin.js`
- 常量文件使用 UPPER_CASE: `CONSTANTS.js`

#### 代码风格
- 使用 2 空格缩进
- 使用单引号
- 行末不加分号
- 最大行长度 100 字符

#### 注释规范
```javascript
/**
 * 函数描述
 * @param {Type} paramName 参数描述
 * @returns {Type} 返回值描述
 */
function myFunction(paramName) {
  // 实现逻辑
}
```

### Git 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 类型说明
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构代码
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```
feat(layer): add support for custom markers

Add ability to use custom marker icons with configurable size and color.

Closes #123
```

## 核心概念

### Layer 图层系统

Layer 是地图组件的核心概念，采用组合模式设计：

```javascript
class Layer {
  constructor(map, options) {
    this.dataManager = new LayerDataManager(this);
    this.styleManager = new LayerStyleManager(this);
    this.eventManager = new LayerEventManager(this);
    this.renderer = new LayerRenderer(this);
  }
}
```

每个管理器负责特定的功能：
- **DataManager**: 数据获取、处理、缓存
- **StyleManager**: 样式配置、主题管理
- **EventManager**: 事件处理、用户交互
- **Renderer**: 覆盖物渲染、性能优化

### 数据源系统

数据源系统提供了统一的数据访问接口：

```javascript
// 静态数据源
const staticSource = DataSourceFactory.createStatic({
  localData: [...]
});

// 动态数据源
const dynamicSource = DataSourceFactory.createDynamic({
  resourceId: 'api-resource'
});
```

### Mixins 体系

Mixins 提供了可复用的功能模块：

```javascript
import { formMixin, performanceMixin } from '@/mixins';

export default {
  mixins: [formMixin, performanceMixin],
  // 组件逻辑
};
```

## 开发最佳实践

### 1. 错误处理

始终使用 ErrorCenter 进行错误处理：

```javascript
import ErrorCenter from '@/utils/ErrorCenter.js';

try {
  await someOperation();
} catch (error) {
  ErrorCenter.handle(error, 'ComponentName.methodName');
}
```

### 2. 性能优化

#### 使用防抖和节流
```javascript
import { debounce, throttle } from '@/utils/PerformanceOptimizer.js';

const debouncedHandler = debounce(this.handleInput, 300);
const throttledHandler = throttle(this.handleScroll, 100);
```

#### 使用虚拟滚动
```javascript
import { VirtualScrollManager } from '@/utils/PerformanceOptimizer.js';

const virtualScroll = new VirtualScrollManager({
  itemHeight: 50,
  containerHeight: 400
});
```

#### 使用懒加载
```javascript
import { LazyLoader } from '@/utils/LazyLoader.js';

const LazyComponent = LazyLoader.component(
  () => import('./MyComponent.vue')
);
```

### 3. 资源管理

使用 ResourceManager 管理资源生命周期：

```javascript
import { globalResourceManager } from '@/utils/ResourceManager.js';

// 注册资源
globalResourceManager.register('my-resource', resource, cleanup);

// 添加事件监听器
const listenerId = globalResourceManager.addEventListener(
  element, 'click', handler
);

// 组件销毁时自动清理
```

### 4. 测试编写

#### 单元测试
```javascript
import { mount } from '@vue/test-utils';
import MyComponent from '@/components/MyComponent.vue';

describe('MyComponent', () => {
  it('should render correctly', () => {
    const wrapper = mount(MyComponent, {
      propsData: { prop: 'value' }
    });
    
    expect(wrapper.text()).toContain('expected text');
  });
});
```

#### 集成测试
```javascript
describe('Layer Integration', () => {
  it('should complete full lifecycle', async () => {
    const layer = new Layer(mockMap);
    await layer.init(config);
    await layer.loadData();
    
    expect(layer.getResourceData()).toHaveLength(3);
    
    layer.destroy();
  });
});
```

### 5. 代码质量

#### 文件大小控制
- 单个文件不超过 300 行
- 单个函数不超过 50 行
- 复杂逻辑及时拆分

#### 模块化设计
- 高内聚，低耦合
- 明确的接口定义
- 合理的依赖关系

## 调试技巧

### 1. 性能调试

```javascript
import { performanceMonitor } from '@/utils/PerformanceOptimizer.js';

// 测量执行时间
performanceMonitor.start('operation');
await someOperation();
const duration = performanceMonitor.end('operation');

// 查看所有指标
console.log(performanceMonitor.getAllMetrics());
```

### 2. 错误调试

```javascript
import ErrorCenter from '@/utils/ErrorCenter.js';

// 查看错误日志
console.log(ErrorCenter.getErrorLog());

// 注册调试处理器
ErrorCenter.register('DEBUG', (error) => {
  console.group('Debug Error');
  console.error(error);
  console.trace();
  console.groupEnd();
});
```

### 3. 内存调试

```javascript
import { globalResourceManager } from '@/utils/ResourceManager.js';

// 检查内存泄漏
const leakCheck = globalResourceManager.checkMemoryLeaks();
if (leakCheck.hasLeaks) {
  console.warn('Memory leaks detected:', leakCheck.warnings);
}

// 查看资源统计
console.log(globalResourceManager.getStats());
```

## 常见问题

### Q: 如何添加新的覆盖物类型？

A: 在 LayerRenderer 中添加新的创建方法，并在 types.js 中定义相关常量。

### Q: 如何自定义数据源？

A: 继承 DataSourceInterface 并实现必要的方法，然后在 DataSourceFactory 中注册。

### Q: 如何优化大量数据的渲染性能？

A: 使用 ViewportManager 进行视口管理，或使用 ClusterManager 进行聚合显示。

### Q: 如何处理组件间通信？

A: 使用 communicationMixin 提供的标准通信方法，或通过 Vuex 进行状态管理。

## 贡献流程

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/my-feature`
3. 提交更改: `git commit -am 'feat: add my feature'`
4. 推送分支: `git push origin feature/my-feature`
5. 创建 Pull Request

## 发布流程

1. 更新版本号
2. 更新 CHANGELOG.md
3. 运行完整测试
4. 构建生产版本
5. 创建 Git 标签
6. 发布到 npm（如适用）

## 技术支持

如有问题，请通过以下方式获取支持：

- 提交 Issue
- 查看文档
- 联系维护团队
