# 地图组件 API 文档

## 概述

本文档提供了地图组件库的完整 API 参考，包括核心类、工具函数、Mixins 和组件的详细说明。

## 目录

### 核心模块
- [Layer 图层类](./core/Layer.md) - 地图图层的核心类
- [LayerDataManager 数据管理器](./core/LayerDataManager.md) - 图层数据管理
- [LayerStyleManager 样式管理器](./core/LayerStyleManager.md) - 图层样式管理
- [LayerEventManager 事件管理器](./core/LayerEventManager.md) - 图层事件处理
- [LayerRenderer 渲染器](./core/LayerRenderer.md) - 图层渲染逻辑

### 数据源模块
- [DataSourceInterface 数据源接口](./datasource/DataSourceInterface.md) - 数据源基类
- [StaticDataSource 静态数据源](./datasource/StaticDataSource.md) - 静态数据处理
- [DynamicDataSource 动态数据源](./datasource/DynamicDataSource.md) - 动态数据处理
- [DataSourceFactory 数据源工厂](./datasource/DataSourceFactory.md) - 数据源创建和管理

### 工具模块
- [ErrorCenter 错误处理中心](./utils/ErrorCenter.md) - 统一错误处理
- [ResourceManager 资源管理器](./utils/ResourceManager.md) - 资源生命周期管理
- [PerformanceOptimizer 性能优化工具](./utils/PerformanceOptimizer.md) - 性能优化相关工具
- [MapPerformanceOptimizer 地图性能优化](./utils/MapPerformanceOptimizer.md) - 地图专用性能优化
- [LazyLoader 懒加载工具](./utils/LazyLoader.md) - 组件和资源懒加载

### Mixins 模块
- [表单 Mixins](./mixins/form.md) - 表单相关通用逻辑
- [上传 Mixins](./mixins/upload.md) - 文件上传通用逻辑
- [通信 Mixins](./mixins/communication.md) - 组件通信规范
- [样式 Mixins](./mixins/style.md) - 样式和交互规范
- [性能 Mixins](./mixins/performance.md) - 性能优化相关 Mixins

### 组件模块
- [地图组件](./components/Map.md) - 主地图组件
- [图层管理组件](./components/LayerManager.md) - 图层管理界面
- [样式配置组件](./components/StyleConfig.md) - 样式配置界面

## 快速开始

### 基本用法

```javascript
import Layer from '@/model/layer/index.js';
import { createMap } from '@/utils/map-utils.js';

// 创建地图实例
const map = createMap('map-container');

// 创建图层
const layer = new Layer(map, {
  id: 'my-layer',
  isStatic: true
});

// 初始化图层
await layer.init({
  type: 'marker',
  displayName: '我的图层',
  localData: [
    { id: 1, name: '北京', lng: 116.404, lat: 39.915 },
    { id: 2, name: '上海', lng: 121.473, lat: 31.230 }
  ],
  mappingConfig: [
    { name: 'id', mapColumnName: 'id' },
    { name: 'name', mapColumnName: 'name' },
    { name: 'lng', mapColumnName: 'lng' },
    { name: 'lat', mapColumnName: 'lat' }
  ]
});

// 加载数据
await layer.loadData();
```

### 使用数据源

```javascript
import DataSourceFactory from '@/model/datasource/DataSourceFactory.js';

// 创建静态数据源
const staticDataSource = DataSourceFactory.createStatic({
  id: 'my-static-source',
  localData: [
    { id: 1, name: '数据1', value: 100 },
    { id: 2, name: '数据2', value: 200 }
  ]
});

await staticDataSource.init();
const data = await staticDataSource.loadData();

// 创建动态数据源
const dynamicDataSource = DataSourceFactory.createDynamic({
  id: 'my-dynamic-source',
  resourceId: 'api-resource-id'
});

await dynamicDataSource.init();
const apiData = await dynamicDataSource.loadData();
```

### 使用 Mixins

```javascript
import { formMixin, performanceMixin } from '@/mixins/index.js';

export default {
  name: 'MyComponent',
  mixins: [formMixin, performanceMixin],
  
  data() {
    return {
      fieldValidators: {
        name: (value) => value ? true : '名称不能为空'
      }
    };
  },
  
  methods: {
    async handleSubmit() {
      // 使用性能监控
      await this.measureAsyncPerformance('submit', async () => {
        // 使用表单验证
        const isValid = await this.validateForm();
        if (isValid) {
          // 提交逻辑
        }
      });
    }
  }
};
```

### 错误处理

```javascript
import ErrorCenter, { ERROR_TYPES } from '@/utils/ErrorCenter.js';

// 注册自定义错误处理器
ErrorCenter.register(ERROR_TYPES.VALIDATION_ERROR, (errorInfo) => {
  console.error('Validation Error:', errorInfo);
  // 自定义处理逻辑
});

// 处理错误
try {
  await someAsyncOperation();
} catch (error) {
  ErrorCenter.handle(error, 'MyComponent.someOperation');
}

// 包装函数自动处理错误
const safeFunction = ErrorCenter.wrapAsync(
  async () => {
    // 可能抛出错误的异步操作
  },
  'MyComponent.safeOperation'
);
```

### 性能优化

```javascript
import { 
  debounce, 
  throttle, 
  VirtualScrollManager,
  ImageLazyLoader 
} from '@/utils/PerformanceOptimizer.js';

// 防抖和节流
const debouncedSearch = debounce(this.handleSearch, 300);
const throttledScroll = throttle(this.handleScroll, 100);

// 虚拟滚动
const virtualScroll = new VirtualScrollManager({
  itemHeight: 50,
  containerHeight: 400
});

// 图片懒加载
const lazyLoader = new ImageLazyLoader({
  rootMargin: '50px'
});
```

## 版本信息

- **当前版本**: 2.0.0
- **最后更新**: 2025-01-16
- **兼容性**: Vue 2.6+, 百度地图 API 3.0+

## 更新日志

### v2.0.0 (2025-01-16)
- 🎉 完全重构的架构设计
- ✨ 新增数据源抽象层
- ✨ 新增完整的 Mixins 体系
- ✨ 新增性能优化工具
- ✨ 新增统一错误处理
- ✨ 新增完整的测试体系
- 🐛 修复内存泄漏问题
- 📚 完善文档和示例

### v1.x.x
- 基础功能实现

## 贡献指南

请参考 [开发指南](../guide/development.md) 了解如何参与项目开发。

## 许可证

MIT License
